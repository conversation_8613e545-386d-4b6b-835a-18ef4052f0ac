using System;

namespace TMW.tmwvrsnVersion
{
  /// <summary> Version info </summary>
  public class tmwvrsnVersionInfo
  {
    /// <summary> Version string </summary>
    public const String managedVersion = "3.31.0000.0000";
    /// <summary> Version string </summary>
    public const String version = "3.31.0000.0";
    /// <summary> Build Date </summary>
    public const String buildDate = "Wed Sep 24 09:08:08 2025";
    /// <summary> Build Number </summary>
    public const String buildNumber = "1029";
    /// <summary> Build Year </summary>
    public const int buildYear = 2025;
    /// <summary> Build Month </summary>
    public const int buildMonth = 9;
    /// <summary> Build Day </summary>
    public const int buildDay = 24;
  };
}
