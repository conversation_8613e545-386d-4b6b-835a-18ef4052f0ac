#include "stdafx.h"
#include <openssl/x509.h>
#include <openssl/pem.h>
#include <openssl/bio.h>
#include "HttpServerCommon.h"
#include "HttpStatic.h"
#include "GTWOsUtils/GtwLogger.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


bool HttpServerCommon::_running = false;
int  HttpServerCommon::_started = 0; // 0==unknown, 1==ok, 2==failed
bool HttpServerCommon::_openOrigin;
bool HttpServerCommon::_engineStarted = false;

std::mutex HttpServerCommon::broadcastQLock;
std::queue<std::string> *HttpServerCommon::pBroadcastQ = nullptr;


DoSetLogFilterConfigCBfunDef HttpServerCommon::DoSetLogFilterConfigCBfun = nullptr;



bool HttpServerCommon::_SendWebTextMessage(
  const BroadcastEventDTOMsg &dto
)
{
  bool err = false;
  if (HttpStatic::pHttpServer)
  {
    if (HttpStatic::pHttpServer->_SendBroadcastEvent(dto.getJson()) == false)
      err = true;
    return err;
  }
  if (HttpStatic::pHttpsServer)
  {
    if (HttpStatic::pHttpsServer->_SendBroadcastEvent(dto.getJson()) == false)
      err = true;
    return err;
  }
  if (GtwSysConfig::AppType() != APP_TYPE::APP_MON)
  {
    if (HttpClientBase::SendBroadcastEvent(dto.getJson()) == false)
      err = true;
  }
  return err;
}

bool HttpServerCommon::compareFilesFunction(const std::string &a, const std::string &b) 
{ 
  return a > b; 
}

void HttpServerCommon::genConfig(nlohmann::json& pt_result, bool bGenForSave, bool isWs)
{
  nlohmann::json global;
  nlohmann::json workspace;
  auto& config = GtwConfiguration::instance();

  config.saveToJson(bGenForSave, global, workspace);
  if (isWs)
  {
    pt_result = workspace;
  }
  else
  {
    pt_result = global;
  }

  //pt_result["gtwHttpPort"] = GtwSysConfig::gtwHttpPort();
  //pt_result["gtwHost"] = GtwSysConfig::gtwHost();
  //pt_result["monHttpPort"] = GtwSysConfig::monHttpPort();
  //pt_result["monHost"] = GtwSysConfig::monHost();
  //pt_result["redHttpPort"] = GtwSysConfig::redHttpPort();
  //pt_result["redHost"] = GtwSysConfig::redHost();

  //pt_result["redIpNic1"] = GtwSysConfig::redIpNic1();
  //pt_result["redNic1"] = GtwSysConfig::redNic1();
  //pt_result["redIpNic2"] = GtwSysConfig::redIpNic2();
  //pt_result["redNic2"] = GtwSysConfig::redNic2();

  //pt_result["redPeerHost"] = GtwSysConfig::redPeerHost();
  //pt_result["redPrimary"] = GtwSysConfig::redPrimary();
  //pt_result["redEnable"] = GtwSysConfig::redEnable();
  //pt_result["redFavorPrimary"] = GtwSysConfig::redFavorPrimary();
  //pt_result["redEnableSyncConfig"] = GtwSysConfig::redEnableSyncConfig();
  //pt_result["redEnableAutoFailover"] = GtwSysConfig::redEnableAutoFailover();
  //pt_result["redSlaveTolerancePercentage"] = GtwSysConfig::redSlaveTolerancePercentage();
  //pt_result["redMasterTolerancePercentage"] = GtwSysConfig::redMasterTolerancePercentage();

  //pt_result["redCheckLocalEngineOnlineTimeout"] = GtwSysConfig::redCheckLocalEngineOnlineTimeout();
  //pt_result["redCheckRemoteEngineOnlineTimeout"] = GtwSysConfig::redCheckRemoteEngineOnlineTimeout();

  //pt_result["redMonitorReStartRetryLimit"] = GtwSysConfig::redMonitorReStartRetryLimit();
  //pt_result["redEngineReStartRetryLimit"] = GtwSysConfig::redEngineReStartRetryLimit();

  //pt_result["gtwWebDir"] = GtwSysConfig::gtwWebDir() == "" ? GtwSysConfig::getWebPath() : GtwSysConfig::gtwWebDir();
  //pt_result["gtwTzPath"] = GtwSysConfig::gtwTzPath() == "" ? GtwSysConfig::getTimeZonePath() : GtwSysConfig::gtwTzPath();

  ////pt_result["httpsPrivateKeyFile"] = GtwSysConfig::httpsPrivateKeyFile();
  ////pt_result["httpsCertificateFile"] = GtwSysConfig::httpsCertificateFile();
  ////pt_result["httpsPrivateKeyPassPhrase"] = GtwSysConfig::httpsPrivateKeyPassPhrase();

  //pt_result["gtwDoValidateConfig"] = GtwSysConfig::gtwDoValidateConfig();
  //pt_result["gtwDoValidateIntegrity"] = GtwSysConfig::gtwDoValidateIntegrity();
  //pt_result["gtwDoWorkSpaceMD5Verification"] = GtwSysConfig::gtwDoWorkSpaceMD5Verification();
  //pt_result["gtwDoAuth"] = GtwSysConfig::gtwDoAuth();
  //pt_result["gtwPasswordComplexity"] = GtwSysConfig::gtwPasswordComplexity();
  //pt_result["gtwAllowedIPs"] = GtwSysConfig::gtwAllowedIPs();
  //pt_result["gtwMaxLogFiles"] = GtwSysConfig::gtwMaxLogFiles();
  //pt_result["gtwMaxBackupFiles"] = GtwSysConfig::gtwMaxBackupFiles();
  //pt_result["gtwDoAudit"] = GtwSysConfig::gtwDoAudit();
  //pt_result["fullLogOnRestart"] = GtwSysConfig::fullLogOnRestart();
  //pt_result["mirrorAllToLog"] = GtwSysConfig::mirrorAllToLog();
  //pt_result["gtwUseWebSSL"] = GtwSysConfig::gtwUseWebSSL();
  //if (bGenForSave)
  //{
  //  pt_result["gtwDoFastShutdown"] = GtwSysConfig::gtwDoFastShutdown();
  //  pt_result["gtwMaxPathLength"] = GtwSysConfig::gtwMaxPathLength();
  //  pt_result["maxNumThreads"] = GtwSysConfig::DEFAULT_MAX_NUM_THREADS;
  //  pt_result["numThreads"] = GtwSysConfig::DEFAULT_NUM_THREADS;
  //  pt_result["sclCategoryMask"] = GtwSysConfig::sclCategoryMask();
  //  pt_result["sclSeverityMask"] = GtwSysConfig::sclSeverityMask();
  //  pt_result["sdgCategoryMask"] = GtwSysConfig::sdgCategoryMask();
  //  pt_result["sdgOpcTraceEnable"] = GtwSysConfig::sdgOpcTraceEnable();

  //  pt_result["sdgSeverityMask"] = GtwSysConfig::sdgSeverityMask();
  //  pt_result["t6CategoryMask"] = GtwSysConfig::t6CategoryMask();
  //  pt_result["t6SeverityMask"] = GtwSysConfig::t6SeverityMask();
  //}
  //else
  //{
  //  pt_result["gtwHttpsCertIsTmwSigned"] = GtwSysConfig::gtwHttpsCertIsTmwSigned();
  //}

  //pt_result["gtwUseLocalHostForEngineAndMonitorComms"] = GtwSysConfig::gtwUseLocalHostForEngineAndMonitorComms();
  //pt_result["gtwEnableHttpDeflate"] = GtwSysConfig::gtwEnableHttpDeflate();

  //pt_result["gtwAuthExpVIEWER_ROLE"] = GtwSysConfig::gtwAuthExpVIEWER_ROLE();
  //pt_result["gtwAuthExpOPERATOR_ROLE"] = GtwSysConfig::gtwAuthExpOPERATOR_ROLE();
  //pt_result["gtwAuthExpCONFIGURATOR_ROLE"] = GtwSysConfig::gtwAuthExpCONFIGURATOR_ROLE();
  //pt_result["gtwAuthExpSU_ROLE"] = GtwSysConfig::gtwAuthExpSU_ROLE();

  //pt_result["gtwWsUpdateRate"] = GtwSysConfig::gtwWsUpdateRate();
  //pt_result["gtwWsUpdateBlockSize"] = GtwSysConfig::gtwWsUpdateBlockSize();
  //pt_result["gtwHttpPageBlockSize"] = GtwSysConfig::gtwHttpPageBlockSize();
  //pt_result["gtwLicGracePeriodInMinutes"] = GtwSysConfig::gtwLicGracePeriodInMinutes();

#ifdef _WIN32
  GtwSysConfig::sDataFilePath() = getenv("ProgramData");
#else
  GtwSysConfig::sDataFilePath() = "/etc";
#endif
  //pt_result["currentWorkSpaceName"] = GtwSysConfig::sCurrentWorkSpaceName();
}

bool HttpServerCommon::ValidateConfig(nlohmann::json pt, std::string& err_msg, bool bForceValidate)
{
  bool bOK = true;
  err_msg = "";

  // validate
  if (bForceValidate)
  {
    std::string gtwHost = pt["gtwHost"].get<std::string>();
    if (ping(gtwHost.c_str(), 4) == 0)
    {
      err_msg += "GTW Engine Host address invalid\r\n ";
      bOK = false;
    }
    std::string monHost = pt["monHost"].get<std::string>();
    if (ping(monHost.c_str(), 4) == 0)
    {
      err_msg += "GTW Monitor Host address invalid\r\n ";
      bOK = false;
    }
    std::string redHost = pt["redHost"].get<std::string>();
    if (ping(redHost.c_str(), 4) == 0)
    {
      err_msg += "GTW Redundancy Host address invalid\r\n ";
      bOK = false;
    }
  }

  std::string gtwHost = pt["gtwHost"].get<std::string>();
  if (gtwHost == "localhost")
  {
    err_msg += "GTW Engine Host address can not be localhost, use 127.0.0.1\r\n ";
    bOK = false;
  }
  std::string monHost = pt["monHost"].get<std::string>();
  if (monHost == "localhost")
  {
    err_msg += "GTW Engine Host address can not be localhost, use 127.0.0.1\r\n ";
    bOK = false;
  }
  std::string redHost = pt["redHost"].get<std::string>();
  if (redHost == "localhost")
  {
    err_msg += "GTW Redundancy Host address can not be localhost, use 127.0.0.1\r\n ";
    bOK = false;
  }

  // web dir
  std::string webDir = pt["gtwWebDir"].get<std::string>();
  if (std::filesystem::exists(webDir) == false)
  {
    err_msg += "web site directory is invalid\r\n ";
    bOK = false;
  }

  // work space name
  std::string currentWorkSpaceName = pt["currentWorkSpaceName"].get<std::string>();
  std::filesystem::path p = std::filesystem::path(GtwSysConfig::getWorkSpacesPath()) / std::filesystem::path(currentWorkSpaceName);
  if (std::filesystem::exists(p) == false)
  {
    // Try to create the workspace directory
    try {
      if (std::filesystem::create_directories(p)) {
        // Successfully created workspace directory
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "Created workspace directory: %s", p.string().c_str());
      } else {
        err_msg += "Failed to create WorkSpaces sub directory for: " + currentWorkSpaceName + "\r\n";
        bOK = false;
      }
    } catch (const std::exception& e) {
      err_msg += "Failed to create WorkSpaces sub directory for: " + currentWorkSpaceName + " - " + e.what() + "\r\n";
      bOK = false;
    }
  }

  //// private key file
  //std::string httpsPrivateKeyFile = pt["httpsPrivateKeyFile"].get<string>();
  //p = std::filesystem::path(GtwSysConfig::getPrivateKeyPath()) / std::filesystem::path(httpsPrivateKeyFile);
  //if (std::filesystem::exists(p) == false && GtwSysConfig::gtwUseWebSSL() == true)
  //{
  //  err_msg += "https private key file path is invalid\r\n ";
  //  bOK = false;
  //}
  // public key file
  //std::string httpsCertificateFile = pt["httpsCertificateFile"].get<string>();
  //p = std::filesystem::path(GtwSysConfig::getPublicKeyPath()) / std::filesystem::path(httpsCertificateFile);
  //if (std::filesystem::exists(p) == false && GtwSysConfig::gtwUseWebSSL() == true)
  //{
  //  err_msg += "https public certificate file path is invalid\r\n ";
  //  bOK = false;
  //}
  // role expiration limits
  int gtwAuthExpVIEWER_ROLE = pt["gtwAuthExpVIEWER_ROLE"].get<int>();
  int gtwAuthExpOPERATOR_ROLE = pt["gtwAuthExpOPERATOR_ROLE"].get<int>();
  int gtwAuthExpCONFIGURATOR_ROLE = pt["gtwAuthExpCONFIGURATOR_ROLE"].get<int>();
  int gtwAuthExpSU_ROLE = pt["gtwAuthExpSU_ROLE"].get<int>();
  if (gtwAuthExpVIEWER_ROLE < 0 || gtwAuthExpOPERATOR_ROLE < 0 || gtwAuthExpCONFIGURATOR_ROLE < 0 || gtwAuthExpSU_ROLE < 0)
  {
    err_msg += "role timeout can not be negative\r\n ";
    bOK = false;
  }
  if (gtwAuthExpOPERATOR_ROLE < 60)
  {
    err_msg += "Operator role timeout must be at least 60 seconds\r\n ";
    bOK = false;
  }
  if (gtwAuthExpCONFIGURATOR_ROLE < 60)
  {
    err_msg += "Configurator role timeout must be at least 60 seconds\r\n ";
    bOK = false;
  }
  if (gtwAuthExpSU_ROLE < 60)
  {
    err_msg += "Super User role timeout must be at least 60 seconds\r\n ";
    bOK = false;
  }
  if (gtwAuthExpOPERATOR_ROLE > 36000)
  {
    err_msg += "Operator role timeout must be less than or equal to 10 hours\r\n ";
    bOK = false;
  }
  if (gtwAuthExpCONFIGURATOR_ROLE > 36000)
  {
    err_msg += "Configurator role timeout must be less than or equal to 10 hours\r\n ";
    bOK = false;
  }
  if (gtwAuthExpSU_ROLE > 36000)
  {
    err_msg += "Super User role timeout must be less than or equal to 10 hours\r\n ";
    bOK = false;
  }

  // ip port values
  int gtwHttpPort = pt["gtwHttpPort"].get<int>();
  int monHttpPort = pt["monHttpPort"].get<int>();
  int redHttpPort = pt["redHttpPort"].get<int>();
  if (gtwHttpPort > 65535 || gtwHttpPort < 0)
  {
    err_msg += "Engine ip port must be less than or equal to 65535 and greater than or equal to 0\r\n ";
    bOK = false;
  }

  if (monHttpPort > 65535 || monHttpPort < 0)
  {
    err_msg += "Monitor ip port must be less than or equal to 65535 and greater than or equal to 0\r\n ";
    bOK = false;
  }
  if (redHttpPort > 65535 || redHttpPort < 0)
  {
    err_msg += "Redundancy ip port must be less than or equal to 65535 and greater than or equal to 0\r\n ";
    bOK = false;
  }

  int gtwWsUpdateRate = pt["gtwWsUpdateRate"].get<int>();
  if (gtwWsUpdateRate < 1)
  {
    err_msg += "Update rate must be greater than or equal to 1 second\r\n ";
    bOK = false;
  }

  int gtwWsUpdateBlockSize = pt["gtwWsUpdateBlockSize"].get<int>();
  if (gtwWsUpdateBlockSize < 100)
  {
    err_msg += "Update block size must be greater than or equal to 100 log messages\r\n ";
    bOK = false;
  }

  int gtwHttpPageBlockSize = pt["gtwHttpPageBlockSize"].get<int>();
  if (gtwHttpPageBlockSize < 10 && gtwHttpPageBlockSize != 0)
  {
    err_msg += "Page size must be greater than or equal to 10 items per page or can be 0 to disable paging\r\n ";
    bOK = false;
  }

  return bOK;
}

