#!/bin/sh
# Raw Docker deployment script for SDG
# Alternative to docker-compose for environments without compose support
# Production-ready version with persistent storage for licenses, config, and data

set -e

# Default environment file
ENV_FILE="${ENV_FILE:-.env}"

# Load environment file if it exists
if [ -f "${ENV_FILE}" ]; then
    echo "Loading environment from: ${ENV_FILE}"
    # Export variables from env file (skip comments and empty lines)
    set -a
    . "${ENV_FILE}"
    set +a
else
    echo "Warning: Environment file ${ENV_FILE} not found. Using defaults."
fi

# Configuration (with environment variable overrides)
IMAGE_NAME="${IMAGE_NAME:-docker-sdg:latest}"
CONTAINER_NAME="${CONTAINER_NAME:-sdg-application}"
NETWORK_NAME="${NETWORK_NAME:-sdg-network}"

# Volume names (matching docker-compose setup exactly)
VOLUME_SDG_DATA="${VOLUME_SDG_DATA:-docker_sdg_data}"
VOLUME_SDG_CONFIG="${VOLUME_SDG_CONFIG:-docker_sdg_config}"
VOLUME_HASPLM_DATA="${VOLUME_HASPLM_DATA:-docker_hasplm_data}"
VOLUME_HASPLM_CONFIG="${VOLUME_HASPLM_CONFIG:-docker_hasplm_config}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}SDG Docker Deployment Script${NC}"
echo "================================"

# Show configuration summary
echo "Configuration:"
echo "  Environment file: ${ENV_FILE}"
if [ -n "${SDG_PACKAGE}" ]; then
    echo "  SDG Package: ${SDG_PACKAGE}"
fi
if [ -n "${HASP_PACKAGE}" ]; then
    echo "  HASP Package: ${HASP_PACKAGE}"
fi
if [ -n "${HASP_LICENSE_SERVER}" ]; then
    echo "  License Server: ${HASP_LICENSE_SERVER}"
fi
if [ -n "${LICENSE_SERVER_IP}" ]; then
    echo "  License Server IP: ${LICENSE_SERVER_IP}"
fi
echo ""

# Function to check if container exists
container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if image exists
image_exists() {
    docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"
}

# Function to create network if it doesn't exist
create_network() {
    if ! docker network ls --format '{{.Name}}' | grep -q "^${NETWORK_NAME}$"; then
        echo -e "${YELLOW}Creating network: ${NETWORK_NAME}${NC}"
        docker network create "${NETWORK_NAME}"
    else
        echo -e "${GREEN}Network ${NETWORK_NAME} already exists${NC}"
    fi
}

# Function to create volumes if they don't exist
create_volumes() {
    # Create each volume individually
    for volume in "${VOLUME_SDG_DATA}" "${VOLUME_HASPLM_DATA}" "${VOLUME_HASPLM_CONFIG}"; do
        if ! docker volume ls --format '{{.Name}}' | grep -q "^${volume}$"; then
            echo -e "${YELLOW}Creating volume: ${volume}${NC}"
            docker volume create "${volume}"
        else
            echo -e "${GREEN}Volume ${volume} already exists${NC}"
        fi
    done
}

# Function to build image
build_image() {
    echo -e "${YELLOW}Building image: ${IMAGE_NAME}${NC}"

    # Build arguments from environment
    BUILD_ARGS=""
    if [ -n "${SDG_PACKAGE}" ]; then
        BUILD_ARGS="${BUILD_ARGS} --build-arg SDG_PACKAGE=${SDG_PACKAGE}"
        echo "Using SDG package: ${SDG_PACKAGE}"
    fi
    if [ -n "${HASP_PACKAGE}" ]; then
        BUILD_ARGS="${BUILD_ARGS} --build-arg HASP_PACKAGE=${HASP_PACKAGE}"
        echo "Using HASP package: ${HASP_PACKAGE}"
    fi

    # Build with arguments
    docker build -f Dockerfile.sdg -t "${IMAGE_NAME}" ${BUILD_ARGS} .
}

# Function to run container
run_container() {
    echo -e "${YELLOW}Starting container: ${CONTAINER_NAME}${NC}"

    # Build environment variables for container
    ENV_VARS=""

    # Core SDG environment variables
    ENV_VARS="${ENV_VARS} -e SDG_ADMIN_PASSWORD=${SDG_ADMIN_PASSWORD:-passwordA1.}"
    ENV_VARS="${ENV_VARS} -e SDG_HOST_IP=${SDG_HOST_IP:-127.0.0.1}"
    ENV_VARS="${ENV_VARS} -e SDG_USE_LOCALHOST_COMMS=${SDG_USE_LOCALHOST_COMMS:-true}"

    ENV_VARS="${ENV_VARS} -e container=docker"

    # GTW supervision environment variables
    ENV_VARS="${ENV_VARS} -e GTW_LOG_DIR=${GTW_LOG_DIR:-/var/log}"
    ENV_VARS="${ENV_VARS} -e GTW_MAX_RESTART_ATTEMPTS=${GTW_MAX_RESTART_ATTEMPTS:-5}"
    ENV_VARS="${ENV_VARS} -e GTW_RESTART_DELAY=${GTW_RESTART_DELAY:-5}"
    ENV_VARS="${ENV_VARS} -e GTW_HEALTH_CHECK_INTERVAL=${GTW_HEALTH_CHECK_INTERVAL:-5}"

    # HASP licensing environment variables (optional)
    if [ -n "${HASP_LICENSE_SERVER}" ]; then
        ENV_VARS="${ENV_VARS} -e HASP_LICENSE_SERVER=${HASP_LICENSE_SERVER}"
        echo "Using HASP license server: ${HASP_LICENSE_SERVER}"
    fi

    # Extra hosts for license server (if configured)
    EXTRA_HOSTS=""
    if [ -n "${LICENSE_SERVER_IP}" ]; then
        EXTRA_HOSTS="--add-host license-server:${LICENSE_SERVER_IP}"
        echo "Adding license server host mapping: license-server:${LICENSE_SERVER_IP}"
    else
        # Default to local container
        EXTRA_HOSTS="--add-host license-server:127.0.0.1"
    fi

    # Capabilities and security options
    CAPS="--cap-add SYS_ADMIN --cap-add SYS_PTRACE --cap-add MKNOD --cap-add SYS_RAWIO"
    SECURITY="--privileged --security-opt seccomp:unconfined --security-opt apparmor:unconfined"

    # Device mappings
    DEVICES="--device /dev/urandom:/dev/urandom:rwm --device /dev/fuse:/dev/fuse:rwm"

    docker run -d \
        --name "${CONTAINER_NAME}" \
        --network "${NETWORK_NAME}" \
        --hostname sdg-gateway \
        --domainname local \
        --platform linux/amd64 \
        ${CAPS} \
        ${SECURITY} \
        ${DEVICES} \
        ${EXTRA_HOSTS} \
        --tmpfs /run \
        --tmpfs /run/lock \
        --tmpfs /tmp \
        -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
        -v "${VOLUME_SDG_DATA}":/opt/sdg/data \
        -v "${VOLUME_HASPLM_DATA}":/var/hasplm \
        -v "${VOLUME_HASPLM_CONFIG}":/etc/hasplm \
        -p 58090:58090 \
        -p 58080:58080 \
        -p 4885:4885 \
        -p 1947:1947 \
        -p 102:102 \
        -p 103:103 \
        -p 2404:2404 \
        -p 2405:2405 \
        -p 2406:2406 \
        -p 20000:20000 \
        -p 20001:20001 \
        -p 20002:20002 \
        -p 20003:20003 \
        -p 502:502 \
        -p 503:503 \
        --restart unless-stopped \
        ${ENV_VARS} \
        "${IMAGE_NAME}"
}

# Parse command line arguments
case "${1:-start}" in
    "build")
        build_image
        ;;
    "start")
        # Check if image exists
        if ! image_exists; then
            echo -e "${YELLOW}Image not found. Building...${NC}"
            build_image
        fi
        
        # Create network and volumes
        create_network
        create_volumes
        
        # Stop existing container if running
        if container_running; then
            echo -e "${YELLOW}Stopping existing container...${NC}"
            docker stop "${CONTAINER_NAME}"
        fi
        
        # Remove existing container if exists
        if container_exists; then
            echo -e "${YELLOW}Removing existing container...${NC}"
            docker rm "${CONTAINER_NAME}"
        fi
        
        # Run new container
        run_container
        
        echo -e "${GREEN}Container started successfully!${NC}"
        echo -e "Web Interface: ${YELLOW}https://127.0.0.1:58090${NC}"
        echo -e "Login: ${YELLOW}admin / passwordA1.${NC}"
        ;;
    "stop")
        if container_running; then
            echo -e "${YELLOW}Stopping container...${NC}"
            docker stop "${CONTAINER_NAME}"
            echo -e "${GREEN}Container stopped${NC}"
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "restart")
        bash "$0" stop
        sleep 2
        bash "$0" start
        ;;
    "status")
        if container_running; then
            echo -e "${GREEN}Container is running${NC}"
            docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "logs")
        if container_exists; then
            docker logs -f "${CONTAINER_NAME}"
        else
            echo -e "${RED}Container does not exist${NC}"
        fi
        ;;
    "shell")
        if container_running; then
            docker exec -it "${CONTAINER_NAME}" bash
        else
            echo -e "${RED}Container is not running${NC}"
        fi
        ;;
    "cleanup")
        echo -e "${YELLOW}Cleaning up...${NC}"
        if container_exists; then
            docker rm -f "${CONTAINER_NAME}" 2>/dev/null || true
        fi
        if image_exists; then
            docker rmi "${IMAGE_NAME}" 2>/dev/null || true
        fi

        # Remove all volumes
        for volume in "${VOLUME_SDG_DATA}" "${VOLUME_HASPLM_DATA}" "${VOLUME_HASPLM_CONFIG}"; do
            if docker volume ls --format '{{.Name}}' | grep -q "^${volume}$"; then
                echo -e "${YELLOW}Removing volume: ${volume}${NC}"
                docker volume rm "${volume}" 2>/dev/null || true
            fi
        done

        # Remove network
        if docker network ls --format '{{.Name}}' | grep -q "^${NETWORK_NAME}$"; then
            echo -e "${YELLOW}Removing network: ${NETWORK_NAME}${NC}"
            docker network rm "${NETWORK_NAME}" 2>/dev/null || true
        fi

        echo -e "${GREEN}Cleanup complete${NC}"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "SDG Raw Docker Deployment Script"
        echo "Alternative to docker-compose for environments without compose support"
        echo ""
        echo "Commands:"
        echo "  build     Build the Docker image"
        echo "  start     Start the container (default)"
        echo "  stop      Stop the container"
        echo "  restart   Restart the container"
        echo "  status    Show container status"
        echo "  logs      Show container logs"
        echo "  shell     Connect to container shell"
        echo "  cleanup   Remove container, image, network, and all volumes"
        echo "  help      Show this help message"
        echo ""
        echo "Features:"
        echo "  • Environment file configuration support"
        echo "  • Configurable package versions via build arguments"
        echo "  • Production-ready persistent storage (3 volumes)"
        echo "  • Smart license management with preservation"
        echo "  • Network and local licensing support"
        echo "  • Complete configuration preservation"
        echo "  • Self-healing service supervision"
        echo ""
        echo "Environment Files:"
        echo "  • Use ENV_FILE=.env.hybrid-licensing $0 start for hybrid licensing"
        echo "  • Default: .env (local licensing)"
        echo ""
        echo "Web Interface: https://127.0.0.1:58090"
        echo "Default Login: admin / passwordA1."
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
