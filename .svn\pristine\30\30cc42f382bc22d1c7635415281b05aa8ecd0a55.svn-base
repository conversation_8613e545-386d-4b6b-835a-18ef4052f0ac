#!/bin/bash

set -e

# Get SVN version and revision
svnversion=$(./getVersion.sh)
echo "SVN Version: $svnversion"

svnrevision=$(./getRevision.sh)
echo "SVN Revision: $svnrevision"

# Build the Debian package for the current version and revision
./buildDebianPkg $svnversion $svnrevision || { echo "Failed to build Debian package"; exit 1; }

# Create install and update script for the current version
chmod +x mk-setup.sh 
./mk-setup.sh $svnversion $svnrevision

# Set new build output directory under InstallArmRaspberryPI
build_output_dir="./NightlyBuild/SDG5.2.3/RaspberryPiInstall/"

# Ensure the build output directory exists
mkdir -p "$build_output_dir"

# Copy scripts and installer to the new directory
cp ./install_script.sh "$build_output_dir"
cp ./update_script.sh "$build_output_dir"
cp ./update_from_5_1_3_script.sh "$build_output_dir"
cp /home/<USER>/SDG_Linux_build/tmwsdg-$svnversion-$svnrevision.arm64-buster.deb "$build_output_dir"

# Make the v2c file executable
cd "$build_output_dir"
chmod +x install_v2c_arm64

# Copy the updated installation documentation
cp ../doc/install_pi.md "$build_output_dir"

# Create a tar of all release files in the new directory
tar -cvf "$build_output_dir/SDG5.2.3-arm64-buster.tar" tmwsdg-$svnversion-$svnrevision.arm64-buster.deb install_script.sh update_script.sh update_from_5_1_3_script.sh aksusbd_8.53-1_arm64.deb haspvlib_arm64_102099.so install_v2c_arm64 SDG_Unlocked_20190321_171527.v2c install_pi.md
