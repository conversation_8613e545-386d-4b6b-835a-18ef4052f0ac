
#include "stdafx.h"

#include "TMW61850/i61850/TMW61850/TMW61850Library.h"
#include "GTW61850Client.h"
#include "GTW61850PolledPointSet.h"
#include "GTW61850DataAttributeMDO.h"
#include "GTW61850PolledPointSetEditor.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


#ifdef DEBUG
//#define DEBUG_61850_PPS
//#define DEBUG_61850_PPS_VERBOSE
#endif

static const unsigned int PPS_INITIALIZATION_POLL_RATE = 200;
static const unsigned int PPS_FLOW_CONTROL_PAUSE = 200;
static const unsigned int PPS_FLOW_CONTROL_MAX=3;
static const unsigned int PPS_WORKQ_MAX_STUCK_MS = 60000; // 1 minute

ImplementClassBaseInfo(GTW61850PolledPointSet, GTW61850ControlBlock, pClassInfo1, nullptr);

class Read61850PointSetWorkItem : public WorkItemBase
{
public:
  Read61850PointSetWorkItem()
  {
  }

  virtual void DoWork(void* param);

  virtual void Abort()
  {
  }

  GTW61850PolledPointSet *m_pPolledPointSet;
};

class GTW61850PolledPointSetREADControl : public GTWChannelBinaryControl
{
private:
  CStdString m_name;
  GTW61850PolledPointSet* m_pPolledPointSet;

  virtual CStdString GetBaseName(void)
  {
    return m_name;
  }

public:
  GTW61850PolledPointSetREADControl(CStdString _name, GTW61850PolledPointSet* _rpt) :
    GTWChannelBinaryControl(TMWDEFS_FALSE)
  {
    m_name = _name + "_READ_POINTS";
    m_name.Replace(".", "_");
    m_pPolledPointSet = _rpt;
  }

  void UpdateName(CStdString _name)
  {
    m_name = _name + "_READ_POINTS";
    m_name.Replace(".", "_");
  }

  void UponInsert(GTWCollectionBase* pParent)
  {
    GTWChannelBinaryControl::UponInsert(pParent);
  }

  virtual CStdString GetFullName() override
  {
    CStdString sn = GTWChannelBinaryControl::GetFullName();
    return sn;
  }

  virtual GTWDEFS_CTRL_STAT SetValue(bool newValue);

  virtual GTWDEFS_CTRL_STAT SetInitialValue(CStdString initialValue)
  {
    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }

  //virtual CStdString GetFullName() override
  //{
  ///  return m_pPolledPointSet->GetGtwClient()->GetMemberName() + "." + m_name;
  //}

  virtual GTWDEFS_TYPE getMdoType(void)
  {
    return GTWDEFS_TYPE_BOOL;
  }
};

class PPSTimerThread
{
private:
  //tmw::AutoCriticalSection  m_TimerCS;
  uint32_t m_nPeriod;
  GTW61850PolledPointSet* m_pPPS;
  std::thread* m_pThread;
  bool m_bStop;

public:

  PPSTimerThread(GTW61850PolledPointSet* pPps) :
    m_nPeriod(0),
    m_pPPS(pPps),
    m_pThread(nullptr),
    m_bStop(false)
  {
  }

  uint32_t GetPeriod() { return m_nPeriod; }

  void SetPeriod(uint32_t per)
  {
    //tmw::CriticalSectionLock lock(m_TimerCS);
    m_nPeriod = per;

    if (m_nPeriod == 0)
    {
      //TRACE("stopping thread completely\n", per);
      m_bStop = true;
      if (m_pThread)
      {
        m_pThread->join();
        delete m_pThread;
        m_pThread = nullptr;
      }
    }
    else
    {
      if (m_pThread)
      {
        //TRACE("stopping thread newperiod = %d\n", per);
        m_bStop = true;
        m_pThread->join();
        delete m_pThread;
      }
      m_bStop = false;
      m_pThread = new std::thread(std::bind(threadFunc, this));
    }
  }

  void Stop()
  {
    SetPeriod(0);
  }

  static unsigned long __stdcall threadFunc(void* timerThread)
  {
    PPSTimerThread* pTimerThread = (PPSTimerThread*)timerThread;

    while (!pTimerThread->m_bStop && !GetGTWApp()->IsShuttingDown())
    {
      if (pTimerThread->m_nPeriod > 0)
      {
        try
        {
          pTimerThread->m_pPPS->DoRead();
        }
        catch (std::exception& ex)
        {
          LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "PPSTimerThread exception thrown : %s", ex.what());
        }
        catch (...)
        {
          LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "%s", "PPSTimerThread unknown exception thrown");
        }
        uint32_t ms = pTimerThread->m_nPeriod;
        tmw::Thread::SleepMS(ms);
      }
    }

    return 0;
  }

};

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

GTW61850PolledPointSet::GTW61850PolledPointSet(CStdString name, GTW61850Client *p61850Client, TMWTYPES_USHORT index)
  :
  GTW61850ControlBlock(name,index),
  m_pClientConnection(p61850Client->GetClientConnection()),
  m_bCacheDirty(true),
  m_nMDOCount(0),
  m_nArrayReadCount(0),
  m_nCompletedArrayCount(0),
  m_nTimerCount(0),
  m_nPreviousCompletedCount(0),
  m_pGTW61850PolledPointSetREADControl(nullptr),
  m_nCurPollRate(PPS_INITIALIZATION_POLL_RATE),
  m_pReadPolledPointSetTimerInfo(nullptr),
  m_pReadPolledPointSetTimer(nullptr),
  m_ppsTM(nullptr),
  m_bUseDedicatedThread(false)
{
}

GTW61850PolledPointSet::~GTW61850PolledPointSet()
{
  if (m_bUseDedicatedThread)
  {
    m_ppsTM->Stop();
    delete m_ppsTM;
  }
  else
  {
    if (m_pReadPolledPointSetTimer)
    {
      m_pReadPolledPointSetTimer->KillTimer();
    }
    if (m_pReadPolledPointSetTimerInfo)
    {
      delete m_pReadPolledPointSetTimerInfo;
      m_pReadPolledPointSetTimerInfo = NULL;
    }
    if (m_pReadPolledPointSetTimer)
    {
      delete m_pReadPolledPointSetTimer;
      m_pReadPolledPointSetTimer = NULL;
    }
  }

  GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

GTWBaseEditor *GTW61850PolledPointSet::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTW61850PolledPointSetEditor(dto, this, (GTW61850Client *)GetParentMember(), false);
  else
    m_pEditor->SetDTO(dto);
  
  return m_pEditor;
}

TMWTYPES_UINT GTW61850PolledPointSet::GetPollRate()
{
  return GTWConfig::I61850PolledPointSetPeriod(Get61850Client()->Get61850ClientIndex(),GetBlockIndex());
}

void GTW61850PolledPointSet::OnAddMDO(GTW61850DataAttributeMDO *pMdo)
{
  tmw::CriticalSectionLock lock(m_nodeListCriticalSection);
  GTW61850ControlBlock::OnAddMDO(pMdo);

  m_nMDOCount++;
  m_bCacheDirty = true;
}

void GTW61850PolledPointSet::OnRemoveMDO(GTW61850DataAttributeMDO *pMdo)
{
  tmw::CriticalSectionLock lock(m_nodeListCriticalSection);
  GTW61850ControlBlock::OnRemoveMDO(pMdo);

  m_nMDOCount--;
  m_bCacheDirty = true;
}

void GTW61850PolledPointSet::GetDescriptionColText(CStdString &sText)
{
  sText.Format("PPS: PollPeriod = %d, MDO Count = %u", GetPollRate(), m_nMDOCount); 
}

bool GTW61850PolledPointSet::UponRemove(GTWCollectionBase *pParent)
{
  bool ret = GTW61850ControlBlock::UponRemove(pParent);

  if (m_bUseDedicatedThread)
  {
    m_ppsTM->Stop();
  }
  else
  {
    if (m_pReadPolledPointSetTimer)
    {
      m_pReadPolledPointSetTimer->KillTimer();
    }
  }

  if (m_pGTW61850PolledPointSetREADControl)
  {
    m_pGTW61850PolledPointSetREADControl->DeleteCollectionMember();
    m_pGTW61850PolledPointSetREADControl = nullptr;
  }

  return ret;
}

void GTW61850PolledPointSet::UponInsert(GTWCollectionBase *pParent)
{
  GTW61850ControlBlock::UponInsert(pParent);

  GTWCollectionList *pMemberCollection = m_p61850Client->GetMemberCollection();

  m_pGTW61850PolledPointSetREADControl = new GTW61850PolledPointSetREADControl(this->GetMemberName(),this);
  if (m_pGTW61850PolledPointSetREADControl)
  {
    pMemberCollection->InsertCollectionMember(m_pGTW61850PolledPointSetREADControl);
    m_pGTW61850PolledPointSetREADControl->SetShowInGUIOnlyUnderParent(m_pMemberCollection);
    m_pMemberCollection->InsertCollectionMember(m_pGTW61850PolledPointSetREADControl);
  }

  m_bUseDedicatedThread = GTWConfig::I61850UsePPSThread[m_p61850Client->Get61850ClientIndex()];
  if (m_bUseDedicatedThread)
  {
    m_ppsTM = new PPSTimerThread(this);
    LOG6(m_pClientConnection, GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Using dedicated thread for 61850 PPS '%s'", GetFullName().c_str());
  }
  else
  {
    m_pReadPolledPointSetTimerInfo = new ReadPolledPointSetTimerInfo("Read Polled Point Set Timer");
    m_pReadPolledPointSetTimer = new WinTimer(m_pReadPolledPointSetTimerInfo);
  }
}

void GTW61850PolledPointSet::PolledPointSetREADControlUpdateName()
{
  m_pGTW61850PolledPointSetREADControl->UpdateName(this->GetMemberName());
}

void GTW61850PolledPointSet::SetPolledPointSet(tmw61850::Client *pClient)
{
  m_pClientConnection = pClient;
  Enable();
}

void GTW61850PolledPointSet::Enable()
{
  LOG6(m_pClientConnection, GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Enable PPS '%s'", GetFullName().c_str());

  Reset(true, false);

  // We do this for the initial read to occur quickly (within 200 ms) and then set it back to the persisted period if needed
  // when the first read is completed
  unsigned int ds_period = GetPollRate();
  SetReadPolledPointSetTimer(std::min(ds_period, PPS_INITIALIZATION_POLL_RATE));
}

void GTW61850PolledPointSet::Disable()
{
  SetReadPolledPointSetTimer(0);
  Reset(true, false);
}

void GTW61850PolledPointSet::ReadArrayFailed(tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsErrorCode)
{
  LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** Polled Point Set (%s) read array error = %s", (const char*)this->GetFullName(), tmw61850::ClientMessage::ClientMMSErrorCodeToString(mmsErrorCode));

  OnEndClientPointChange();
}

//tmw::Array<tmw::String> nodenamelist;

void GTW61850PolledPointSet::OnClientPointReadError(tmw61850::DataAttribute *pDA, tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsErrorCode)
{
  tmw::String s;
  LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** Polled Point Set (%s) read error for Data Attribute '%s', error = %s", (const char*)this->GetFullName(), (const char *)pDA->GetFullName(s), tmw61850::ClientMessage::ClientMMSErrorCodeToString(mmsErrorCode));

  // this is called from OnOperationalMsgStruct work item and calling OnClientPointChange may result in an extra read if the OnEnd was called by the library for the same read error
  OnClientPointChange(pDA);
}

void GTW61850PolledPointSet::OnClientPointChange(tmw61850::DataAttribute *pDA)
{
  // do nothing until all nodes are read
}

void GTW61850PolledPointSet::OnEndClientPointChange()
{
  m_nCompletedArrayCount++;
  //assert(m_nCompletedArrayCount <= m_nArrayReadCount);
  if (m_nCompletedArrayCount >= m_nArrayReadCount) // finished reading all arrays
  {
#ifdef DEBUG_61850_PPS_VERBOSE
    LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "*** PPS Info %s finished with expected equals actual (%d), called update MODs and SetWorkQueProcessing to false\n", (const char*)this->GetFullName(), m_nExpectedPointChangeCount);
#endif
    //UpdateMDOs();

    // We do this for the initial read to occur quickly and then reset to the persisted period when first one is completed
    int persistedPeriod = GetPollRate();
    int curPeriod = m_bUseDedicatedThread ? m_ppsTM->GetPeriod() : m_pReadPolledPointSetTimerInfo->getPeriod();
    if (persistedPeriod != curPeriod)
    {
      this->SetReadPolledPointSetTimer(persistedPeriod);
#ifdef DEBUG_61850_PPS_VERBOSE
      LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "*** PPS %s reset poll rate to %d\n", (const char*)this->GetFullName(), persistedPeriod);
#endif
    }

    if (m_nCompletedArrayCount > m_nArrayReadCount) // Got more replies than expecting?? See Note above in ReadArrayError
    {
      LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** 61850 PPS %s finished with expected (%d) > actual (%d), called update MODs and SetWorkQueProcessing to false. Resetting...\\n", (const char*)this->GetFullName(), m_nArrayReadCount, m_nCompletedArrayCount);
      Reset(true, false);
    }
    else
    {
      SetWorkQueueProcessing(false);
    }
#ifdef DEBUG_61850_PPS
    LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "*** PPS %s finished --- expected equals actual\n\n", (const char*)this->GetFullName());
#endif
  }
}

void GTW61850PolledPointSet::Disconnect()
{
  //SetWorkQueueProcessing(false);
  Disable();
}

void GTW61850PolledPointSet::Reset(bool bResetWQProcessing, bool bSetCacheDirty)
{
  m_nPointChangeCount       = 0;
  m_nTimerCount             = 0;
  m_nArrayReadCount         = 0;
  m_nCompletedArrayCount    = 0;
  m_nPreviousCompletedCount = 0;

  m_bCacheDirty = bSetCacheDirty;
  if (bResetWQProcessing)
  {
    SetWorkQueueProcessing(false);
  }
}

void GTW61850PolledPointSet::AddNode(tmw61850::Node *pNode, std::vector<tmw61850::Node *> &nodes)
{
  if (pNode)
  {
    if (std::find(nodes.begin(), nodes.end(), pNode) == nodes.end())
    {
      nodes.push_back(pNode);
      //if (this->GetFullName().Find("WT50.Poll5000") >= 0)
      //{
      //  tmw::String s;
      //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "adding node : %s, change count=%d\n", (const char*)pNode->GetFullName(s), ncount);
      //}

    }
  }
}

void GTW61850PolledPointSet::BuildNodeList()
{
  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850,"%s",  "\n------------------- BuildNodeList: -----------------------\n");
  
  m_SplitNodes.clear();

  std::vector<tmw61850::Node *> nodes;

  std::list<GTW61850DataAttributeMDO*> list;
  Get61850Client()->GetControlBlockMDOs(this, list);

  for (std::list<GTW61850DataAttributeMDO*>::iterator iter = list.begin(); iter != list.end(); ++iter)
  {
    GTW61850DataAttributeMDO *pMdo = *iter;
    AddNode(pMdo->getValueDataAttribute(), nodes);
    AddNode(pMdo->getTimeDataAttribute(), nodes);
    AddNode(pMdo->getQualityDataAttribute(), nodes);
  }

  std::sort(nodes.begin(), nodes.end(), compareFC);
  SplitNodes(m_SplitNodes, nodes);

  m_nArrayReadCount = 0;
  for (std::list<std::vector<tmw61850::Node *>>::iterator iter = m_SplitNodes.begin();
    iter != m_SplitNodes.end(); ++iter)
  {
    std::vector<tmw61850::Node *> &nodes1 = *iter;
    ReadNodesArray(nodes1, true);
  }

  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\n----------------- NodeList.total=%u, expected read count=%d -----------------------\n\n", m_nodes.size(), m_nExpectedPointChangeCount);
}

bool GTW61850PolledPointSet::compareFC(tmw61850::Node *p1, tmw61850::Node *p2)
{
  tmw61850::DataAttribute *dp1 = (tmw61850::DataAttribute *)p1;
  tmw61850::DataAttribute *dp2 = (tmw61850::DataAttribute *)p2;
  return strcmp(dp1->GetFC(), dp2->GetFC()) > 0;
}

void GTW61850PolledPointSet::SplitNodes(std::list<std::vector<tmw61850::Node *>> &splitNodes, const std::vector<tmw61850::Node *> &nodes)
{
  size_t maxSize = 250;
  splitNodes.clear();

  std::vector<tmw61850::Node *> nodeList;
  nodeList.reserve(std::min(maxSize, nodes.size()));
  for (unsigned int i=0; i < nodes.size(); i++)
  {
    nodeList.push_back(nodes[i]);
    if (nodeList.size() >= maxSize)
    {
      splitNodes.push_back(nodeList);
      nodeList.clear();
      nodeList.reserve(maxSize);
    }
  }

  if (nodeList.size() > 0)
  {
    splitNodes.push_back(nodeList);
  }
}

/*
void GTW61850PolledPointSet::ReadNodesArray(std::vector<tmw61850::Node *> &nodes)
{
  std::sort(nodes.begin(), nodes.end(), compareFC);
  m_readNodes.clear();

  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\n========== Start Read Nodes AS ARRAY for %s =============\n", (const char*)this->GetFullName());
  
  for (unsigned int i=0; i < nodes.size(); i++)
  {
    tmw61850::DataAttribute *dacur = (tmw61850::DataAttribute *)nodes[i];

    tmw61850::DataAttribute *danext = i < nodes.size()-1 ? (tmw61850::DataAttribute *)nodes[i+1] : NULL;

    tmw::String fccur = dacur->GetFC();
    tmw::String fcnext = danext != NULL ? danext->GetFC() : "";

    //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "array[%d] = %s, %s\n", i, (const char*)fccur, dacur->GetName().c_str());
    m_readNodes.add(nodes[i]);

    tmw::String s;
    nodes[i]->GetFullName(s);
    //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Adding node[%d]: %s to m_readNodes for fc=%s\n", i, (const char *)s, (const char*)fccur);
    //nodenamelist.add(s);

    if (!fccur.equals(fcnext) || m_readNodes.size() == i)
    {
      //if (this->GetFullName().Find("WT50.Poll5000") >= 0)
      //{
      //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, ">>>>> Calling ReadNodeArray for %d nodes for fc=%s\n", m_readNodes.size(), fccur.c_str());
      //}
      tmw61850::ClientEnumDefs::ClientMMSErrorCode err = this->GetClientConnection()->ReadNodeArray(m_readNodes, fccur, this);
      if (err == tmw61850::ClientEnumDefs::MMSErrorCode_Success)
      {
        m_nArrayReadCount++;
      }
      else
      {
        for (unsigned int k = 0; k < m_readNodes.size(); k++)
        {
          //m_nPointChangeCount += m_readNodes[k]->GetUserDataArrayPtr()->size();
          m_nPointChangeCount += GetExpectedNodeReads(m_readNodes[k]);
        }
        tmw::String fullName;
        LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "61850 Error: %s, Could not read polled point array for: %s error: %s", 
          (const char *)this->Get61850Client()->GetAliasName(), 
          this->GetFullName().c_str(),
          tmw61850::ClientMessage::ClientMMSErrorCodeToString(err));
      }
      m_readNodes.clear();
    }
  }
#ifdef DEBUG_61850_PPS
    //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\n========== END Read Nodes AS ARRAY for %s =============\n\n", (const char*)this->GetFullName());
#endif
}
*/

void GTW61850PolledPointSet::ReadNodesArray(std::vector<tmw61850::Node *> &nodes, bool bCountOnly)
{
  tmw::Array<tmw61850::Node *> readNodes;

  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\n========== Start Read Nodes AS ARRAY for %s =============\n", (const char*)this->GetFullName());

  for (unsigned int i = 0; i < nodes.size(); i++)
  {
    tmw61850::DataAttribute *dacur = (tmw61850::DataAttribute *)nodes[i];
    tmw61850::DataAttribute *danext = i < nodes.size() - 1 ? (tmw61850::DataAttribute *)nodes[i + 1] : NULL;

    tmw::String fccur = dacur->GetFC();
    tmw::String fcnext = danext != NULL ? danext->GetFC() : "";

    //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "array[%d] = %s, %s\n", i, (const char*)fccur, dacur->GetName().c_str());

    readNodes.add(nodes[i]);

    //if (GetFullName() == "ccc.pps1")
    //{
    //  tmw::String s;
    //  nodes[i]->GetFullName(s);
    //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "ccc.pps1: >>>>>>>> m_nReadCalledCount = %d <<<<<<<<<<< Adding node[%d]: %s to m_readNodes for fc=%s\n", m_nReadCalledCount, i, (const char *)s, (const char*)fccur);
    //}

    if (!fccur.equals(fcnext) || i == nodes.size() - 1)
    {
      //if (this->GetFullName().Find("WT50.Poll5000") >= 0)
      //{
      //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, ">>>>> Calling ReadNodeArray for %d nodes for fc=%s\n", m_readNodes.size(), fccur.c_str());
      //}
      if (bCountOnly)
      {
        m_nArrayReadCount++;
      }
      else
      {
        //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s : Calling ReadNodeArray..m_nReadCalledCount = %d\n", (const char*)GetFullName(), ++m_nReadCalledCount);
        int nCount = 0;
        tmw61850::ClientEnumDefs::ClientMMSErrorCode err = this->GetClientConnection()->ReadNodeArray(readNodes, fccur, this);
        while (err == tmw61850::ClientEnumDefs::ClientMMSErrorCode::ClientRequest_Flow_Controlled)
        {
          GtwOsSleep(PPS_FLOW_CONTROL_PAUSE);

          nCount++;
          //if (nCount >= 10 && nCount % 10 == 0)  // Note currently this could be an infinite loop
          //{
          //  CStdString c;
          //  c.Format("*** PPS-Error %s, PPS ReadNodeArray is stuck in flow control, tries=%d.\n", (const char*)GetFullName(), nCount);
          //  trace61850Error(c);
          //}
          if (nCount*PPS_FLOW_CONTROL_PAUSE > PPS_WORKQ_MAX_STUCK_MS)
          {
            LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** PPS-Error %s, PPS ReadNodeArray is stuck in flow control, tries=%d. Will Reset and try again.\n", (const char*)GetFullName(), nCount);
            Reset(true, false);
            return;
          }
          //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, ">>> Flow controlled in ReadNodeArray: %d\n", nCount);
          
          err = this->GetClientConnection()->ReadNodeArray(readNodes, fccur, this);
        }

        if (err != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
        {
          m_nCompletedArrayCount++;

          tmw::String fullName;
          LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "61850 Error: %s, Could not read polled point array for: %s error: %s",
              (const char *)this->Get61850Client()->GetAliasName(),
              this->GetFullName().c_str(),
              tmw61850::ClientMessage::ClientMMSErrorCodeToString(err));

          if (!IsConnected())
          {
            SetWorkQueueProcessing(false);
            return;
          }
        }
      }
      readNodes.clear();
    }
  }
#ifdef DEBUG_61850_PPS
  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\n========== END Read Nodes AS ARRAY for %s =============\n\n", (const char*)this->GetFullName());
#endif
}


void GTW61850PolledPointSet::ReadPoints()
{
  tmw::CriticalSectionLock lock(m_nodeListCriticalSection);

  //if (this->GetFullName().Find("WT50.Poll5000") >= 0)
 // {
   // LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "========== Start ReadPoints %s  =============\n\n", this->GetFullName().c_str());
  //}
  if (m_nMDOCount == 0)
  {
    m_bCacheDirty = false;
    return;
  }
  if (m_bCacheDirty || m_nArrayReadCount == 0)
  {
    BuildNodeList();
    m_bCacheDirty = false;
    if (m_nArrayReadCount == 0)
    {
      LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** PPS-Error %s: this list has no valid MDOs to read in the model. This is likely because of a mismatch between the current SDG configuration and the models being used in the client and/or server.\n This PPS will be disabled and will no longer process until a new connection is established.", (const char*)GetFullName());

      Disable();
      Reset(true, true);
      return;
    }
  }

  // NOW - Always read as array
  if (m_SplitNodes.size() > 0)
  {
    assert(m_nArrayReadCount != 0);

    m_nCompletedArrayCount = 0;
    m_nPreviousCompletedCount = 0;
    //m_nReadCalledCount = 0;

    for (std::list<std::vector<tmw61850::Node *>>::iterator iter = m_SplitNodes.begin(); iter != m_SplitNodes.end(); ++iter)
    {
      std::vector<tmw61850::Node *> &nodes = *iter;
      ReadNodesArray(nodes, false);
    }
  }

#ifdef DEBUG_61850_PPS
  //if (this->GetFullName().Find("WT50.Poll5000") >= 0)
  //{
  //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "========== End ReadPoints %s =============\n\n", this->GetFullName().c_str());
  //}

  //LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "========== End ReadPoints %s =============\n\n", this->GetFullName().c_str());
#endif
}


void GTW61850PolledPointSet::ClearPointsCache()
{
  //tmw::CriticalSectionLock lock(m_nodeListCriticalSection);
  m_bCacheDirty = true;
}

void GTW61850PolledPointSet::SetReadPolledPointSetTimer(int newTime)
{
  if (m_bUseDedicatedThread)
  {
    m_ppsTM->SetPeriod(newTime);
  }
  else
  {
    if (m_pReadPolledPointSetTimer)
    {
      LOG6(m_pClientConnection, GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Update PPS '%s' timer to %d", GetFullName().c_str(), newTime);

      m_nCurPollRate = newTime;
      if (newTime > 0)
      {
        m_pReadPolledPointSetTimer->SetTimer(this, newTime);
      }
      else
      {
        m_pReadPolledPointSetTimerInfo->SetPeriod(0);
        m_pReadPolledPointSetTimer->KillTimer();
      }
    }
  }
}

void GTW61850PolledPointSet::DoRead()
{
  //tmw::CriticalSectionLock lock(m_nodeListCriticalSection);
  if (!IsConnected())
  {
#ifdef DEBUG_61850_PPS
    LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s Polled Point Set Timer fired, but IsConnected is false...Resetting state.\n",(const char*)GetFullName());
#endif
    SetWorkQueueProcessing(false);
    return;
  }

  if (m_nMDOCount == 0)
  {
    ClearPointsCache();
    SetWorkQueueProcessing(false);
    return;
  } 

  if (IsWorkQueueProcessing()) // Wait until previous read is completely done before doing another one
  {
    //if (m_nTimerCount > 16)
    //{
    //  LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "*** PPS-Warning %s, PPS is stuck: timercount=%d. ExpectedArrayReads=%d, CurrentArrayReads=%d\n", (const char*)GetFullName(), m_nTimerCount, m_nArrayReadCount, m_nCompletedArrayCount);
    //}
    if (m_nPreviousCompletedCount == m_nCompletedArrayCount)
    {
      LOG6(m_pClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "*** PPS-Warning %s, PPS is stuck, tries=%d. ExpectedArrayReads=%d, CurrentArrayReads=%d\n", (const char*)GetFullName(), m_nTimerCount, m_nArrayReadCount, m_nCompletedArrayCount);
      m_nTimerCount++; // only increment if idle (i.e. nothing else has completed) since last time timer fired.
    }
    m_nPreviousCompletedCount = m_nCompletedArrayCount;

    unsigned int idle = m_nCurPollRate*m_nTimerCount;
    if (idle < PPS_WORKQ_MAX_STUCK_MS) // make sure not idle forever
    {
      return;
    }

    // Idle for too int...
    LOG6(m_pClientConnection, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "*** PPS-Error %s, PPS is stuck, idle for %d ms. ExpectedArrayReads=%d, CurrentArrayReads=%d. Calling Reset.\n", (const char*)GetFullName(), idle, m_nArrayReadCount, m_nCompletedArrayCount);

    Reset(true, false);
  }

  if (IsWorkQueueProcessing()) // Wait until previous read is completely done before doing another one
    return;

  m_nTimerCount = 0;
  m_nPreviousCompletedCount = 0;
  m_nCompletedArrayCount = 0;
  SetWorkQueueProcessing(true);

  if (this->Get61850Client()->GetResponseWorkQueue()->IsActive())
  {
    Read61850PointSetWorkItem *pReadWorkItem = new Read61850PointSetWorkItem();
    pReadWorkItem->m_pPolledPointSet = this;
    this->Get61850Client()->GetResponseWorkQueue()->AddWorkItemToQueue(pReadWorkItem);
  }
}

bool GTW61850PolledPointSet::IsConnected()
{
  if (!GetGTWApp() || GetGTWApp()->IsShuttingDown() || GetGTWApp()->IsStartingUp())
  {
    return false;
  }

  GTW61850Client *pGtwClt = Get61850Client();
  tmw61850::Client *p61850Clt = pGtwClt->GetClientConnection();
  if (!p61850Clt->IsConnectionAliveAndReady() || !pGtwClt->IsConnectedAndInitialized())
  {
    return false;
  }

  return true;
}

GTWDEFS_CTRL_STAT GTW61850PolledPointSetREADControl::SetValue(bool newValue)
{
  m_pPolledPointSet->DoRead();
  GTWInternalDataObjectBool::SetValue(newValue);

  return(GTWDEFS_CTRL_STAT_SUCCESS);
}

void ReadPolledPointSetTimerInfo::OnTimer(void *pCallBackData)
{
  GTW61850PolledPointSet *pPolledPointSet = (GTW61850PolledPointSet *)pCallBackData;
  if (!pPolledPointSet)
  {
    return;
  }
  pPolledPointSet->DoRead();
}

void Read61850PointSetWorkItem::DoWork(void* param)
{
  std::unique_ptr<Read61850PointSetWorkItem> deleteWhenReturn(this);
  //SetNeedCreateNewWorkItemOnReturn autoset(m_pTimer);

  GTW61850Client *pGtwClt = m_pPolledPointSet->Get61850Client();
  try
  {
    if (pGtwClt == NULL)
    {
      return;
    }
    tmw61850::Client *p61850Clt = pGtwClt->GetClientConnection();
    if (p61850Clt == NULL)
    {
      return;
    }

    if (p61850Clt->IsConnectionAliveAndReady() == false)
    {
      return;
    }

    if (p61850Clt->GetRequestQueueSize() > p61850Clt->GetRequestQueueMAXSize())
    {
      LOG6(pGtwClt->GetClientConnection(), GtwLogger::Severity_Warning, GtwLogger::SDG_Category_61850, "PPS-Warning '%s', 61850 client '%s' RequestQueueSize (%d) is too large (max=%d). The polled point set will not read until corrected.",
        m_pPolledPointSet->GetFullName().c_str(), pGtwClt->GetFullName().c_str(), p61850Clt->GetRequestQueueSize(), p61850Clt->GetRequestQueueMAXSize());

      return;
    }

    m_pPolledPointSet->ReadPoints();
  }
  catch (...)
  {
    LOG6(pGtwClt->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "I61850: Read61850PointSetWorkItem::DoWork exception caught");
  }
}

