#!/bin/sh
# Container Migration Script for SDG
# Captures container state and creates a new image with SSL certificates and configuration

set -e

# Configuration
CONTAINER_NAME="${CONTAINER_NAME:-sdg-application}"
SOURCE_IMAGE="${SOURCE_IMAGE:-docker-sdg:latest}"
TARGET_IMAGE="${TARGET_IMAGE:-docker-sdg:migrated-$(date +%Y%m%d-%H%M%S)}"
BACKUP_DIR="${BACKUP_DIR:-./migration-backup}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}SDG Container Migration Tool${NC}"
echo "===================================="

# Function to check if container exists and is running
check_container() {
    if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${RED}Error: Container ${CONTAINER_NAME} is not running${NC}"
        echo "Please start the container first:"
        echo "  docker-compose up -d"
        echo "  # or"
        echo "  ./run-docker.sh start"
        exit 1
    fi
    echo -e "${GREEN}✓ Container ${CONTAINER_NAME} is running${NC}"
}

# Function to create backup of container data
create_backup() {
    echo -e "${YELLOW}Creating backup of container data...${NC}"
    mkdir -p "${BACKUP_DIR}"

    # Backup SSL certificates and GTW configuration (excluding license logs)
    echo "Backing up SSL certificates and GTW configuration..."
    docker exec "${CONTAINER_NAME}" tar -czf /tmp/sdg-config-backup.tar.gz -C /etc/tmw/sdg \
        --exclude='LicenseLogs' \
        --exclude='*.log' \
        . 2>/dev/null || true
    docker cp "${CONTAINER_NAME}:/tmp/sdg-config-backup.tar.gz" "${BACKUP_DIR}/sdg-config-backup.tar.gz"

    # Backup application data (optional - usually in volumes)
    echo "Backing up application data..."
    docker exec "${CONTAINER_NAME}" tar -czf /tmp/sdg-data-backup.tar.gz -C /opt/sdg/data . 2>/dev/null || true
    docker cp "${CONTAINER_NAME}:/tmp/sdg-data-backup.tar.gz" "${BACKUP_DIR}/sdg-data-backup.tar.gz"

    # Note: License data is NOT backed up as it's environment-specific
    echo "Note: License data (/var/hasplm) is not backed up - it's environment-specific"

    echo -e "${GREEN}✓ Backup created in ${BACKUP_DIR}${NC}"
}

# Function to clean up environment-specific data before commit
cleanup_for_migration() {
    echo -e "${YELLOW}Cleaning up environment-specific data...${NC}"

    # Remove license logs (environment-specific)
    docker exec "${CONTAINER_NAME}" rm -rf /etc/tmw/sdg/LicenseLogs/* 2>/dev/null || true

    # Remove any other log files in GTW config directory
    docker exec "${CONTAINER_NAME}" find /etc/tmw/sdg -name "*.log" -delete 2>/dev/null || true

    echo -e "${GREEN}✓ Environment-specific data cleaned up${NC}"
}

# Function to commit container to new image
commit_container() {
    echo -e "${YELLOW}Committing container to new image...${NC}"

    # Clean up environment-specific data first
    cleanup_for_migration

    # Get container info
    CONTAINER_ID=$(docker ps --filter "name=${CONTAINER_NAME}" --format "{{.ID}}")

    # Commit container with SSL certificates and configuration (but no license logs)
    docker commit \
        --message "SDG container with SSL certificates and GTW configuration (license logs excluded)" \
        --author "SDG Migration Tool" \
        "${CONTAINER_ID}" \
        "${TARGET_IMAGE}"

    echo -e "${GREEN}✓ New image created: ${TARGET_IMAGE}${NC}"
}

# Function to show migration instructions
show_instructions() {
    echo ""
    echo -e "${BLUE}Migration Complete!${NC}"
    echo "==================="
    echo ""
    echo "Your container state has been captured in:"
    echo "  📦 Image: ${TARGET_IMAGE}"
    echo "  💾 Backup: ${BACKUP_DIR}/"
    echo ""
    echo -e "${YELLOW}To deploy in a new environment:${NC}"
    echo ""
    echo "1. Export the image:"
    echo "   docker save ${TARGET_IMAGE} | gzip > sdg-migrated.tar.gz"
    echo ""
    echo "2. Transfer to new environment and load:"
    echo "   gunzip -c sdg-migrated.tar.gz | docker load"
    echo ""
    echo "3. Update docker-compose.yml or run-docker.sh to use new image:"
    echo "   IMAGE_NAME=${TARGET_IMAGE}"
    echo ""
    echo "4. Deploy in new environment:"
    echo "   docker-compose up -d"
    echo "   # or"
    echo "   IMAGE_NAME=${TARGET_IMAGE} ./run-docker.sh start"
    echo ""
    echo -e "${YELLOW}To restore from backup (if needed):${NC}"
    echo ""
    echo "1. Start container with new image"
    echo "2. Restore configuration:"
    echo "   docker cp ${BACKUP_DIR}/sdg-config-backup.tar.gz CONTAINER:/tmp/"
    echo "   docker exec CONTAINER tar -xzf /tmp/sdg-config-backup.tar.gz -C /etc/tmw/sdg"
    echo ""
    echo -e "${GREEN}SSL certificates and GTW configuration are now part of the image!${NC}"
    echo -e "${YELLOW}Note: License logs and environment-specific data were excluded from migration.${NC}"
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  migrate   Create migrated image with current container state (default)"
    echo "  backup    Create backup only (no image commit)"
    echo "  help      Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  CONTAINER_NAME  Source container name (default: sdg-application)"
    echo "  SOURCE_IMAGE    Source image name (default: docker-sdg:latest)"
    echo "  TARGET_IMAGE    Target image name (default: docker-sdg:migrated-TIMESTAMP)"
    echo "  BACKUP_DIR      Backup directory (default: ./migration-backup)"
    echo ""
    echo "Examples:"
    echo "  # Basic migration"
    echo "  $0 migrate"
    echo ""
    echo "  # Custom target image"
    echo "  TARGET_IMAGE=my-sdg:v1.0 $0 migrate"
    echo ""
    echo "  # Backup only"
    echo "  $0 backup"
}

# Parse command line arguments
case "${1:-migrate}" in
    "migrate")
        check_container
        create_backup
        commit_container
        show_instructions
        ;;
    "backup")
        check_container
        create_backup
        echo -e "${GREEN}✓ Backup complete in ${BACKUP_DIR}${NC}"
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
