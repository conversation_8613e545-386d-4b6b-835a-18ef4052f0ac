#include "stdafx.h"
#include "MonHttpServerImplStartStop.h"
#include "GTWWebLib/HttpClientBase.h"
#include "GTWWebLib/HttpServerCommon.h"
#include "GTWWebLib/GtwWebConfig.h"
#include "GTWOsUtils/GtwOsUtils.h"
#include "GTWOsUtils/cpuMemStats.h"
#include "GTWWebMonitorAPI.h"
#include "GTWOsUtils/DirectoryLockChecker.h"

using namespace std;
using namespace SimpleWeb;

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoStopMon(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  TEST_AUTH_IMPL(response, request);
  http_audit_record audit_record;

  try
  {
    std::string query = request->query_string;
    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    audit_record = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    // store query key/value pairs in a map
    std::map<std::string, std::string> params;

    pServer->ExtractParams(query, params);
    bool bothFlag = params["bothFlag"] == "true" || params["bothFlag"] == "True" ? true : false;

    if (bothFlag == true)
    {

#ifdef _WIN32
      killProcessByName("GTWEngine");
      killProcessByName("GTWRedunMnger");
#else        
        //killProcessByName("GTWEngine");

        system("sudo pkill -f GTWEngine");
        system("sudo systemctl stop tmwsdg-engine");
        system("sudo systemctl start tmwsdg-engine");

        system("sudo pkill -f GTWRedunMnger");
        system("sudo systemctl stop tmwsdg-redundancy");
        system("sudo systemctl start tmwsdg-redundancy");
#endif
      HttpServerBase<SOCK_TYP>::SetRunning(false);

    }
    else
    {
      HttpServerBase<SOCK_TYP>::SetRunning(false);
    }

    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
  }
  catch (std::exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }

}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoNewWorkSpace(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  TEST_AUTH_IMPL(response, request);
  http_audit_record audit_record;

  try
  {
    auto query = request->content.string();

    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    audit_record = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    // store query key/value pairs in a map
    map<string, string> params;

    pServer->ExtractParams(query, params);

    auto workSpaceName = params["workSpaceName"];

    if (workSpaceName == "")
    {
      time_t rawtime;
      struct tm * timeinfo;
      char buffer[80];

      time(&rawtime);
      timeinfo = localtime(&rawtime);

      strftime(buffer, sizeof(buffer), "%Y-%d-%m_%H-%M-%S", timeinfo);
      std::string str(buffer);

      workSpaceName = "tmwgtway_" + str;
      GtwSysConfig::sCurrentWorkSpaceName() = workSpaceName;
    }
    else
    {
      std::filesystem::path ws_path = std::filesystem::path(GtwSysConfig::getWorkSpacesPath() + "/" + workSpaceName);

      if (std::filesystem::exists(ws_path) == true)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, pServer->GetToken(request).c_str(), "TR_ERROR_WORK_SPACE_EXISTS", "Error Work Space {{arg1}} already exists", workSpaceName.c_str());
        pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "work space already exists");
        return;
      }
      GtwSysConfig::sCurrentWorkSpaceName() = workSpaceName;
    }

    GtwWebConfig::SaveGtwConfig(false);

    //HttpClientBase::SendEngineStopRequest(token);
#ifdef _WIN32
    killProcessByName("GTWEngine");
    StartGTWService();

    killProcessByName("GTWRedunMnger");
    StartREDService();
#else        
    //killProcessByName("GTWEngine");

    system("sudo pkill -f GTWEngine");
    system("sudo systemctl stop tmwsdg-engine");
    system("sudo systemctl start tmwsdg-engine");

    system("sudo pkill -f GTWRedunMnger");
    system("sudo systemctl stop tmwsdg-redundancy");
    system("sudo systemctl start tmwsdg-redundancy");
#endif
    //HttpServerBase<SOCK_TYP>::SetRunning(false);

    // since we expect the operating system to start the engine back up no need to do it here
    nlohmann::json res;
    res["result"] = true;
    audit_record.append("workSpace: " + workSpaceName);
    pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
  }
  catch (exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoSelectWorkSpace(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  TEST_AUTH_IMPL(response, request);
  http_audit_record audit_record;
  try
  {
    auto query = request->content.string();

    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    audit_record = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    // store query key/value pairs in a map
    map<string, string> params;

    pServer->ExtractParams(query, params);

    auto workSpaceName = params["workSpaceName"];

    if (workSpaceName == "")
    {
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    else
    {
      std::filesystem::path ws_path = std::filesystem::path(GtwSysConfig::getWorkSpacesPath() + "/" + workSpaceName);

      if (std::filesystem::exists(ws_path) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, pServer->GetToken(request).c_str(), "TR_ERROR_WORK_SPACE_DOESNT_EXISTS", "Error Work Space {{arg1}} doesn't exists", workSpaceName.c_str());
        pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "work space doesn't exists");
        return;
      }
      GtwSysConfig::sCurrentWorkSpaceName() = workSpaceName;
    }

    GtwWebConfig::SaveGtwConfig(false);

    if (HttpClientBase::IsEngineRunning() == false)
    {
#ifdef _WIN32
      StartGTWService();
      StartREDService();
#else
      system("sudo systemctl start tmwsdg-engine") == 0 ? true : false;
      system("sudo systemctl start tmwsdg-redundancy") == 0 ? true : false;
#endif
      int cnt = 0;
      while (HttpClientBase::IsEngineRunning() == false)
      {
        if (cnt > 120)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, pServer->GetToken(request).c_str(), "TR_ERROR_FAILED_TO_START_AFTER_120_SEC", "Failed to start engine after 120 seconds");
          pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to start engine after 120 seconds");
          return;
        }
        GtwOsSleep(1000);
        ++cnt;
      }
      // since we expect the operating system to start the engine back up no need to do it here
      nlohmann::json res;
      res["result"] = true;
      audit_record.append("workSpace: " + workSpaceName);
      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
      return;
    }

#ifdef _WIN32
    killProcessByName("GTWEngine");
    StartGTWService();

    killProcessByName("GTWRedunMnger");
    StartGTWService();

#else        
    //killProcessByName("GTWEngine");

    system("sudo pkill -f GTWEngine");
    system("sudo systemctl stop tmwsdg-engine");
    system("sudo systemctl start tmwsdg-engine");

    system("sudo pkill -f GTWRedunMnger");
    system("sudo systemctl stop tmwsdg-redundancy");
    system("sudo systemctl start tmwsdg-redundancy");
#endif


    // since we expect the operating system to start the engine back up no need to do it here
    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
  }
  catch (exception & e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoStartEngine(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  http_audit_record audit_record;
  try {
    auto query = request->content.string();

    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    audit_record = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

#ifndef _WIN32
    // This should be checking to see the if Engine is running as a service,
    // But for now if the monitor is running as a service, assume the Engine is as well.
    // The systemd service script has configured the GTW processes to restart so nothing needs to be done here.

    {
	    bool bOk = system("sudo systemctl start tmwsdg-engine") == 0 ? true : false;
	    if (bOk)
	    {
	      nlohmann::json res;
	      res["result"] = true;
	      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
	    }
	    else
	    {
  	    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to start: sudo systemctl start tmwsdg-engine");
	    }
	
	    return;
    }

    //if (GtwSysConfig::GetRunningAsService())
    //{
    //  return;
    //}
#endif

    // store query key/value pairs in a map
    map<string, string> params;

    pServer->ExtractParams(query, params);

    auto arg = params["arg"];
    std::filesystem::path appDir = getExecutablePath();
    string exePath = (appDir / "GTWEngine.exe").string();
    if (arg == "")
    {
      arg = GtwSysConfig::GetEngineINIFullName();
    }
    if (arg == "")
    {
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to start sdg no ini file specified");
      return;
    }

    string cmd = exePath + " " + "\"" + arg + "\"";
    if (HttpClientBase::IsEngineRunning())
    {
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Already running, Failed to start: " + cmd);
      return;
    }

    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_HTTP, nullptr, "RunCmd: %s", cmd.c_str());
    bool bOk = ::RunCmd(cmd.c_str(), false);
    if (bOk)
    {
      nlohmann::json res;
      res["result"] = true;
      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
    }
    else
    {
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to start: " + cmd);
    }
  }
  catch (exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}


template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoStopEngine(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  TEST_AUTH_IMPL(response, request);
  http_audit_record audit_record;
  try
  {
    auto query = request->content.string();

    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    audit_record = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    // store query key/value pairs in a map
    map<string, string> params;

    pServer->ExtractParams(query, params);
    bool stayDown = params["stayDown"] == "1" || params["stayDown"] == "true" || params["stayDown"] == "True" ? true : false;

    if (stayDown)
    {
#ifdef _WIN32
      bool bOk = StopGTWService();
      if (!bOk)
      {
        pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to stop Engine Service");
        return;
      }
#else
      system("sudo systemctl stop tmwsdg-engine");
#endif
      nlohmann::json res;
      res["result"] = true;
      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
      return;
    }


    if (HttpClientBase::IsEngineRunning() == false)
    {
#ifdef _WIN32
      StartGTWService();
      StartREDService();
#else        
        
      system("sudo pkill -f GTWEngine");
      system("sudo systemctl stop tmwsdg-engine");
      system("sudo systemctl start tmwsdg-engine");

      system("sudo pkill -f GTWRedunMnger");
      system("sudo systemctl stop tmwsdg-redundancy");
      system("sudo systemctl start tmwsdg-redundancy");
#endif
    }


    HttpClientBase::SendEngineStopRequest();
    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", res.dump());
  }
  catch (exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoMonHealthGet(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
  try {
    PerfStats ps;

    // Write json.
    nlohmann::json pt_result;

    MONITOR_STATE_ENUM e = GTWWebMonitorAPI::GetMonState();
    pt_result["monitorState"] = MONITOR_STATE_ENUM_to_string(e);



    double monitorCpu = -1.0;
    double monitorMem = -1.0;
    ps.getAll();
    monitorCpu = ps.cpuPctMe;
    if (std::isnan(monitorCpu))
    {
      monitorCpu = 0.0;
    }

    // Use physical memory (RSS) consistently, like top command
    monitorMem = ((double)ps.physMemUsedByMe / (double)ps.totalPhysMem) * 100.0;

    pt_result["monitorCpu"] = monitorCpu;
    pt_result["monitorMem"] = monitorMem;

    std::string json = pt_result.dump();

    //json.pop_back(); // remove last char

    pServer->BuildOkResponse(response, "application/json", json);
    //*response << "HTTP/1.1 200 OK\r\n"
    //  << "Content-Type: application/json\r\n"
    //  << "Content-Length: " << json.length() << "\r\n\r\n"
    //  << json;
  }
  catch (std::exception& e) {
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
    //*response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n" << e.what();
  }
}


static bool ListWorkspaceBackups(const std::string& workspaceFile,
  const std::string& backupPath,
  nlohmann::json& result) {
  try {
    std::string fullBackupPath = backupPath + "/" + workspaceFile;
    if (!std::filesystem::exists(fullBackupPath)) {
      result["result"] = false;
      result["error"] = "Backup directory does not exist";
      result["backups"] = nlohmann::json::array();
      return false;
    }

    std::vector<nlohmann::json> backups;
    const std::string prefix = workspaceFile + "_";
    const std::string suffix = ".gws";

    for (const auto& entry : std::filesystem::directory_iterator(fullBackupPath)) {
      // Use status() to check if it's a regular file
      if (std::filesystem::is_regular_file(std::filesystem::status(entry.path()))) {
        std::string filename = entry.path().filename().string();
        
        // Check prefix (starts_with replacement)
        bool hasPrefix = (filename.rfind(prefix, 0) == 0);
        
        // Check suffix (ends_with replacement)
        bool hasSuffix = (filename.length() >= suffix.length() && 
                         filename.compare(filename.length() - suffix.length(), 
          suffix.length(),
          suffix) == 0);
                                      
        if (hasPrefix && hasSuffix) {
          auto fileSize = std::filesystem::file_size(entry.path());
          auto lastWriteTime = std::filesystem::last_write_time(entry.path());
          
          // Convert file time to system time without clock_cast
          auto timeT = std::time(nullptr);
          
#ifdef _WIN32
          FILETIME ft;
          ULARGE_INTEGER ull;
          ull.QuadPart = lastWriteTime.time_since_epoch().count();
          ft.dwLowDateTime = ull.LowPart;
          ft.dwHighDateTime = ull.HighPart;
          SYSTEMTIME st;
          FileTimeToSystemTime(&ft, &st);
          
          std::tm tm = { };
          tm.tm_year = st.wYear - 1900;
          tm.tm_mon = st.wMonth - 1;
          tm.tm_mday = st.wDay;
          tm.tm_hour = st.wHour;
          tm.tm_min = st.wMinute;
          tm.tm_sec = st.wSecond;
          timeT = std::mktime(&tm);
#else
          // On Unix, convert nanoseconds to seconds
          timeT = std::chrono::system_clock::to_time_t(
              std::chrono::system_clock::time_point(
                  std::chrono::duration_cast<std::chrono::system_clock::duration>(
                      lastWriteTime.time_since_epoch()
                  )
              )
          );
#endif

          std::tm localTime;
#ifdef _WIN32
          localtime_s(&localTime, &timeT);
#else
          localtime_r(&timeT, &localTime);
#endif
          char timeStr[30];
          std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &localTime);

          nlohmann::json backupInfo;
          backupInfo["filename"] = filename;
          backupInfo["path"] = entry.path().string();
          backupInfo["size"] = fileSize;
          backupInfo["lastModified"] = timeStr;
          backups.push_back(backupInfo);
        }
      }
    }

    std::sort(backups.begin(),
      backups.end(),
      [](const nlohmann::json& a, const nlohmann::json& b) {
        return a["filename"].get<std::string>() > b["filename"].get<std::string>();
      });

    result["result"] = true;
    result["backups"] = backups;
    return true;
  }
  catch (const std::exception& e) {
    result["result"] = false;
    result["error"] = e.what();
    result["backups"] = nlohmann::json::array();
    return false;
  }
}

static bool RestoreWorkspaceBackup(const std::string& workspaceFile,
  const std::string& backupPath,
  nlohmann::json& result) {
  try {
    std::string fullBackupPath = backupPath + "/" + workspaceFile;
    if (!std::filesystem::exists(fullBackupPath)) {
      result["result"] = false;
      result["error"] = "Backup file does not exist";
      return false;
    }

    // build some paths for the restore operation
    std::string workspaceName = GtwSysConfig::sCurrentWorkSpaceName();
    std::string workspacesPath = GtwSysConfig::getWorkSpacesPath();
    std::string targetPath = workspacesPath + "/" + workspaceName;

    // stop the engine
#if DEBUG || _DEBUG
    HttpClientBase::SendEngineStopRequest();
    GtwOsSleep(15000);
#else
#ifdef _WIN32
    bool bOk = StopGTWService();
    if (!bOk)
    {
      result["result"] = false;
      result["error"] = "Engine could not be stopped";
      return false;
    }
#else
    system("sudo systemctl stop tmwsdg-engine");
#endif
#endif

    // close the log files
    HttpClientBase::PauseAllLogging(true);

    if (!DirectoryLockChecker::waitForDirectoryUnlock(targetPath, 60)) {
      result["result"] = false;
      result["error"] = "Timeout waiting for workspace to be unlocked";
      return false;
    }

    // Remove existing workspace if present
    if (std::filesystem::exists(targetPath)) {
#ifdef _WIN32
      // Windows-specific permission changes
      std::filesystem::permissions(targetPath,
        std::filesystem::perms::owner_read |
        std::filesystem::perms::owner_write |
        std::filesystem::perms::owner_exec,
        std::filesystem::perm_options::replace);
#else
      // Unix-style chmod
      chmod(targetPath.c_str(), S_IRWXU);
#endif

      try {
        std::filesystem::remove_all(targetPath);
      }
      catch (const std::filesystem::filesystem_error& e) {
        result["result"] = false;
        result["error"] = "Failed to remove existing workspace: " + std::string(e.what());
        return false;
      }
    }

    // Unzip backup file to workspace directory
    cUnpackFile unpackTool;
    if (!unpackTool.CreateDirFromWorkspace(workspacesPath, fullBackupPath)) {
      result["result"] = false;
      result["error"] = "Failed to restore backup file: " + fullBackupPath;
      return false;
    }

    HttpClientBase::PauseAllLogging(false);

    // start the engine
#ifdef _WIN32
    {
      bool bOk = StartGTWService();
      if (!bOk)
      {
        result["result"] = false;
        result["error"] = "Engine could not be started";
        return false;
      }
    }
#else
    system("sudo systemctl start tmwsdg-engine");
#endif

    result["result"] = true;
    result["restoredFrom"] = fullBackupPath;
    result["workspacePath"] = targetPath;

    return true;
  }
  catch (const std::exception& e) {
    result["result"] = false;
    result["error"] = e.what();
    return false;
  }
}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoBackupFilesGet(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
    std::string sessionToken = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    http_audit_record audit_record = pServer->IsTokenValidRequest(request, sessionToken, userName, userPermissions, userRole);
  try {


    string query = request->query_string;

    // store query key/value pairs in a map
    std::map<std::string, std::string> params;

    pServer->ExtractParams(query, params);

    auto wsName = params["workspaceFile"];
    if (wsName == "")
    {
      wsName = GtwSysConfig::sCurrentWorkSpaceName();
    }

    nlohmann::json result;
    if (ListWorkspaceBackups(wsName, GtwSysConfig::getWorkSpacesBackupPath(), result)) {
      // Success - use result.dump() to get JSON string if needed
      std::string jsonStr = result.dump(4);
      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", jsonStr);
    }
    else {
      // Handle error - error message is in result["error"]
      std::string errorMsg = result["error"];
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", errorMsg);
    }
  }
  catch (std::exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}

template <typename SOCK_TYP>
void MonHttpServerImplStartStop::DoRestoreWorkspace(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
{
    std::string sessionToken = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    http_audit_record audit_record = pServer->IsTokenValidRequest(request, sessionToken, userName, userPermissions, userRole);
  try {

    string query = request->query_string;

    // store query key/value pairs in a map
    std::map<std::string, std::string> params;

    pServer->ExtractParams(query, params);

    auto backupName = params["workspaceFile"];

    nlohmann::json result;
    if (RestoreWorkspaceBackup(backupName, GtwSysConfig::getWorkSpacesBackupPath() + "/" + GtwSysConfig::sCurrentWorkSpaceName(), result)) {
      // Success - use result.dump() to get JSON string if needed
      std::string jsonStr = result.dump(4);
      pServer->BuildOkAuditResponse(response, audit_record, "", "application/json", jsonStr);
    }
    else {
      // Handle error - error message is in result["error"]
      std::string errorMsg = result["error"];
      pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", errorMsg);
    }
  }
  catch (std::exception& e) {
    pServer->BuildBadAuditResponse(response, audit_record, "", SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
  }
}



// linker
template void MonHttpServerImplStartStop::DoStopMon<HTTP>(HttpServerDef *pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoStopMon<HTTPS>(HttpsServerDef *pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoNewWorkSpace<HTTP>(HttpServerDef *pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoNewWorkSpace<HTTPS>(HttpsServerDef *pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoSelectWorkSpace<HTTP>(HttpServerDef* pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoSelectWorkSpace<HTTPS>(HttpsServerDef* pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoStartEngine<HTTP>(HttpServerDef *pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoStartEngine<HTTPS>(HttpsServerDef *pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoStopEngine<HTTP>(HttpServerDef* pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoStopEngine<HTTPS>(HttpsServerDef* pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoMonHealthGet<HTTP>(HttpServerDef* pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoMonHealthGet<HTTPS>(HttpsServerDef* pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoBackupFilesGet<HTTP>(HttpServerDef* pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoBackupFilesGet<HTTPS>(HttpsServerDef* pServer, HttpsResponseDef response, HttpsRequestDef request);

template void MonHttpServerImplStartStop::DoRestoreWorkspace<HTTP>(HttpServerDef* pServer, HttpResponseDef response, HttpRequestDef request);
template void MonHttpServerImplStartStop::DoRestoreWorkspace<HTTPS>(HttpsServerDef* pServer, HttpsResponseDef response, HttpsRequestDef request);
