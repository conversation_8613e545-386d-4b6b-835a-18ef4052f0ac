/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright(c) 1997 - 2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWEquationFunctionSquare.h                                   */
/* DESCRIPTION:  Class definition for BitWise equations                      */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com(919) 870 - 6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#ifndef GTWEquationFunctionSquare_DEFINITIONS
#define GTWEquationFunctionSquare_DEFINITIONS

#include "GTWEquationFunctionBitWise.h"

//**************************************************************************
// this class overrides the timer callback function
class FunctionSquareTimerInfo : public WinTimerInfo
{
public:
  FunctionSquareTimerInfo(CStdString name)
    : WinTimerInfo(name)
  {
  }
protected:
  virtual void OnTimer(void* pCallBackData);
};

class GTWEquationFunctionSquare : public GTWEquationFunctionBitWise
{
  friend class FunctionSquareTimerInfo;
public:
  GTWEquationFunctionSquare(const GTWEQUAT_FUNC_DESCRIPTOR *pDescriptor, GTWEquationDataObject *pEQOB) : 
      GTWEquationFunctionBitWise(pDescriptor) 
  {
    m_pEQOB = pEQOB;
    m_pTimerInfo = NULL;
    m_pTimer = NULL;
    m_lowValue=0;
    m_highValue=0;
    m_value=0;
  }

  virtual ~GTWEquationFunctionSquare()
  {
    if (m_pTimer)
    {
      m_pTimer->KillTimer();
    }
    if (m_pTimerInfo)
    {
      delete m_pTimerInfo;
      m_pTimerInfo = NULL;
    }
    if (m_pTimer)
    {
      delete m_pTimer;
      m_pTimer = NULL;
    }
  }
  static GTWEquationFunctionConverter *gtweqlgc_allocSquare(GTWEquationDataObject *pEQOB)
  {
    return (new GTWEquationFunctionSquare(&gtweqlgc_SquareFunction, pEQOB));
  }
  
private:
  TMWTYPES_UINT m_lowValue;
  TMWTYPES_UINT m_highValue;
  TMWTYPES_UINT m_value;
  GTWEquationDataObject *m_pEQOB;
  FunctionSquareTimerInfo *m_pTimerInfo;
  WinTimer        *m_pTimer;
  virtual void getValue(GTWMasterDataObject *pMdo, TMWTYPES_UINT  *pValue, TMWTYPES_USHORT *pStdQuality);

  bool funcCheckNumArguments(const char* connectionToken) override
  {
    if (getNumArguments() != 3)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_FUNCTION_THREE_ARGS", "The '{{arg1}}' function must have 3 arguments",funcGetIdentifier());
      return(TMWDEFS_FALSE);
    }
    return(TMWDEFS_TRUE);
  }

  virtual bool funcAddArgument(GTWSlaveDataObject *pUpdateSdo,GTWEquationArgument *pArgument, const char* connectionToken)
  {
    if (getNumArguments() == 3)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_FUNCTION_THREE_ARGS", "The '{{arg1}}' function may only have 3 arguments",funcGetIdentifier());
      return(TMWDEFS_FALSE);
    }
    return(GTWEquationFunctionBitWise::funcAddArgument(pUpdateSdo,pArgument, connectionToken));
  }
};

class EquationTimerThread;

class GTWEquationFunctionSquareDT : public GTWEquationFunctionBitWise
{
  friend class EquationTimerThread;
public:
  GTWEquationFunctionSquareDT(const GTWEQUAT_FUNC_DESCRIPTOR* pDescriptor, GTWEquationDataObject* pEQOB);
  virtual ~GTWEquationFunctionSquareDT();

  static GTWEquationFunctionConverter* gtweqlgc_allocSquareDT(GTWEquationDataObject* pEQOB)
  {
    return new GTWEquationFunctionSquareDT(&gtweqlgc_SquareDTFunction, pEQOB);
  }

  void OnTimer();

private:
  TMWTYPES_UINT m_lowValue;
  TMWTYPES_UINT m_highValue;
  TMWTYPES_UINT m_value;
  GTWEquationDataObject* m_pEQOB;

  EquationTimerThread* m_pTimerThread;
  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_UINT* pValue, TMWTYPES_USHORT* pStdQuality) override;
  bool funcCheckNumArguments(const char* connectionToken) override;
  virtual bool funcAddArgument(GTWSlaveDataObject* pUpdateSdo, GTWEquationArgument* pArgument, const char* connectionToken) override;
};
#endif // GTWEquationFunctionSquare_DEFINITIONS