/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright(c) 1997 - 2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWEquationFunctionSquare.cpp                                 */
/* DESCRIPTION:  Class definition for BitWise equations                      */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com(919) 870 - 6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

#include "GTWEquationDataObject.h"
#include "GTWEquationFunctionConverter.h"
#include "GTWEquationFunctionSquare.h"


GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SquareFunction = {"square", "square(expr1,expr2,expr3)", "Generates a square waveform between expr1 and expr2 (inclusive) toggling every expr3 milliseconds", GTWEquationFunctionSquare::gtweqlgc_allocSquare};
GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SquareDTFunction = { "square_dt", "square_dt(expr1,expr2,expr3)", "Generates a square waveform using a dedicated thread timer (for better accuracy but using more resources) between expr1 and expr2 (inclusive) toggling every expr3 milliseconds", GTWEquationFunctionSquareDT::gtweqlgc_allocSquareDT };

void GTWEquationFunctionSquare::getValue(GTWMasterDataObject *pMdo, TMWTYPES_UINT  *pValue, TMWTYPES_USHORT *pStdQuality)
{
  *pValue      = 0;
  *pStdQuality = GTWDEFS_STD_QLTY_GOOD;

  GTWDEFS_STD_QLTY tempStdQuality;
  TMWTYPES_UINT timerValue;
  
  getConverter(0).getValue(pMdo, &m_lowValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;
  getConverter(1).getValue(pMdo, &m_highValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;
  getConverter(2).getValue(pMdo, &timerValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;

  if (m_pTimerInfo == TMWDEFS_NULL)
  {
    m_pTimerInfo = new FunctionSquareTimerInfo("FunctionSquareTimer");
    if (m_pTimerInfo)
    {
      m_pTimer = new WinTimer(m_pTimerInfo);
      m_value = m_lowValue;
    }
  }
 
  if ((timerValue == 0) && m_pTimer)
  {
    *pStdQuality = GTWDEFS_STD_QLTY_INVALID;
    m_pTimer->KillTimer();
    *pValue = 0;
  }

  if (m_pTimerInfo->getPeriod() != timerValue 
    && *pStdQuality == GTWDEFS_STD_QLTY_GOOD
    && ((timerValue != 0) && m_pTimer))
  {
    m_pTimer->SetTimer(this,timerValue);
  }

  if (*pStdQuality == GTWDEFS_STD_QLTY_GOOD)
  {
    *pValue = m_value;
  }

}

void FunctionSquareTimerInfo::OnTimer(void* pCallBackData) 
{ 
  if (GetGTWApp()->IsInitialized())
  {
    GTWEquationFunctionSquare *pDo = (GTWEquationFunctionSquare *)pCallBackData;
    if (pDo->m_value != pDo->m_lowValue)
    {
      pDo->m_value = pDo->m_lowValue;
    }
    else if (pDo->m_value != pDo->m_highValue)
    {
      pDo->m_value = pDo->m_highValue;
    }

    pDo->m_pEQOB->updateSDO(GTWDEFS_UPDTRSN_CHNG_INDICATED,TMWDEFS_NULL);
  }
  return; 
}


//
// Used as a dedicated thread for better timer control
// If other equations need this functionality this should be put in a more global location (likely GTWEquationFunctionConverter.h)
// It is written generically to be used with any equation 
//
class EquationTimerThread
{
public:
  EquationTimerThread(GTWEquationFunctionSquareDT* pEq) :
    m_nPeriod(0),
    m_pEq(pEq),
    m_pThread(nullptr),
    m_bStop(false)
  {
  }

  uint32_t GetPeriod() { return m_nPeriod; }

  void SetPeriod(uint32_t per)
  {
    //tmw::CriticalSectionLock lock(m_TimerCS);
    m_nPeriod = per;

    if (m_nPeriod == 0)
    {
      //TRACE("stopping thread completely\n", per);
      m_bStop = true;
      if (m_pThread)
      {
        m_pThread->join();
        delete m_pThread;
        m_pThread = nullptr;
      }
    }
    else
    {
      if (m_pThread)
      {
        //TRACE("stopping thread newperiod = %d\n", per);
        m_bStop = true;
        m_pThread->join();
        delete m_pThread;
      }
      m_bStop = false;
      m_pThread = new std::thread(std::bind(threadFunc, this));
    }
  }

  void Stop()
  {
    SetPeriod(0);
  }

private:
  //tmw::AutoCriticalSection  m_TimerCS;
  uint32_t m_nPeriod;
  GTWEquationFunctionSquareDT* m_pEq;
  std::thread* m_pThread;
  bool m_bStop;

  static unsigned long __stdcall threadFunc(void* timerThread)
  {
    EquationTimerThread* pTimerThread = (EquationTimerThread*)timerThread;

    while (!pTimerThread->m_bStop && !GetGTWApp()->IsShuttingDown())
    {
      if (pTimerThread->m_nPeriod > 0)
      {
        try
        {
          pTimerThread->m_pEq->OnTimer();
        }
        catch (std::exception& ex)
        {
          LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "PPSTimerThread exception thrown : %s", ex.what());
        }
        catch (...)
        {
          LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "%s", "PPSTimerThread unknown exception thrown");
        }
        uint32_t ms = pTimerThread->m_nPeriod;
        tmw::Thread::SleepMS(ms);
      }
    }

    return 0;
  }
};


GTWEquationFunctionSquareDT::GTWEquationFunctionSquareDT(const GTWEQUAT_FUNC_DESCRIPTOR* pDescriptor, GTWEquationDataObject* pEQOB) :
  GTWEquationFunctionBitWise(pDescriptor)
{
  m_pEQOB = pEQOB;
  m_lowValue = 0;
  m_highValue = 0;
  m_value = 0;
  m_pTimerThread = new EquationTimerThread(this);
}

GTWEquationFunctionSquareDT::~GTWEquationFunctionSquareDT()
{
  m_pTimerThread->Stop();
  delete m_pTimerThread;
}

bool GTWEquationFunctionSquareDT::funcCheckNumArguments(const char* connectionToken)
{
  if (getNumArguments() != 3)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_FUNCTION_THREE_ARGS", "The '{{arg1}}' function must have 3 arguments", funcGetIdentifier());
    return(TMWDEFS_FALSE);
  }
  return(TMWDEFS_TRUE);
}

bool GTWEquationFunctionSquareDT::funcAddArgument(GTWSlaveDataObject* pUpdateSdo, GTWEquationArgument* pArgument, const char* connectionToken)
{
  if (getNumArguments() == 3)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_FUNCTION_THREE_ARGS", "The '{{arg1}}' function may only have 3 arguments", funcGetIdentifier());
    return(TMWDEFS_FALSE);
  }
  return(GTWEquationFunctionBitWise::funcAddArgument(pUpdateSdo, pArgument, connectionToken));
}

void GTWEquationFunctionSquareDT::getValue(GTWMasterDataObject* pMdo, TMWTYPES_UINT* pValue, TMWTYPES_USHORT* pStdQuality)
{
  *pValue = 0;
  *pStdQuality = GTWDEFS_STD_QLTY_GOOD;

  GTWDEFS_STD_QLTY tempStdQuality;
  TMWTYPES_UINT timerValue;

  getConverter(0).getValue(pMdo, &m_lowValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;
  getConverter(1).getValue(pMdo, &m_highValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;
  getConverter(2).getValue(pMdo, &timerValue, &tempStdQuality);
  *pStdQuality |= tempStdQuality;

  if ((timerValue == 0) && m_pTimerThread)
  {
    *pStdQuality = GTWDEFS_STD_QLTY_INVALID;
    m_pTimerThread->Stop();
    *pValue = 0;
  }

  if (m_pTimerThread->GetPeriod() != timerValue
    && *pStdQuality == GTWDEFS_STD_QLTY_GOOD
    && (timerValue != 0))
  {
    m_pTimerThread->SetPeriod(timerValue);
  }

  if (*pStdQuality == GTWDEFS_STD_QLTY_GOOD)
  {
    *pValue = m_value;
  }
}

void GTWEquationFunctionSquareDT::OnTimer()
{
  if (GetGTWApp()->IsInitialized())
  {
    if (m_value != m_lowValue)
    {
      m_value = m_lowValue;
    }
    else if (m_value != m_highValue)
    {
      m_value = m_highValue;
    }
    m_pEQOB->updateSDO(GTWDEFS_UPDTRSN_CHNG_INDICATED, TMWDEFS_NULL);
  }
  return;
}
