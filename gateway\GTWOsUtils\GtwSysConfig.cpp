#include "StdAfx.h"
#include "GtwSysConfig.h"
#include "GtwOsUtils.h"
#include "icmp_pinger.h"
#include "IpAddrUtils.h"
#include "TmwAsymCrypto.h"
#include "gateway/GTWOsUtils/filesystem.hpp"
#include <GtwConfiguration.h>
#include <GtwLogger.h>

// Static member initialization
const int GtwSysConfig::DEFAULT_NUM_THREADS = 4;
const int GtwSysConfig::DEFAULT_MAX_NUM_THREADS = 100;
int GtwSysConfig::m_nNumThreads = DEFAULT_NUM_THREADS;
int GtwSysConfig::m_nMaxNumThreads = DEFAULT_MAX_NUM_THREADS;
std::string GtwSysConfig::cachedConfigPath;

std::string GtwSysConfig::getConfigPath() {
    if (!cachedConfigPath.empty()) {
        return cachedConfigPath;
    }

    std::filesystem::path executablePath;
#ifdef _WIN32
    wchar_t path[MAX_PATH] = { 0 };
    GetModuleFileNameW(NULL, path, MAX_PATH);
    executablePath = std::filesystem::path(path).parent_path();
#else
    char result[PATH_MAX];
    ssize_t count = readlink("/proc/self/exe", result, PATH_MAX);
    executablePath = std::filesystem::path(std::string(result, (count > 0) ? count : 0)).parent_path();
#endif

    std::filesystem::path configFilePath = executablePath / "gtw_config.json";
    if (std::filesystem::exists(configFilePath)) {
        cachedConfigPath = executablePath.string();
        return cachedConfigPath;
    }

#ifdef _WIN32
    const char* sDataFilePath = getenv("ProgramData");
#else
    const char* sDataFilePath = "/etc";
#endif

    std::filesystem::path p1 = std::filesystem::path(sDataFilePath) / SDG_CONFIG_PATH1;
    std::filesystem::create_directories(p1);

    std::filesystem::path p2 = p1 / SDG_CONFIG_PATH2;
    std::filesystem::create_directories(p2);

    cachedConfigPath = p2.string();
    return cachedConfigPath;
}

std::string GtwSysConfig::getWorkSpacesPath() {
    std::filesystem::path p1 = std::filesystem::path(getConfigPath()) / "WorkSpaces";
    std::filesystem::create_directories(p1);

    if (GtwConfiguration::instance().isConfigLoaded()) {
        std::filesystem::path p2 = p1 / GtwConfiguration::instance().getCurrentWorkspaceName();
        std::filesystem::create_directories(p2);
    }

    return p1.string();
}

std::string GtwSysConfig::getWorkSpacesBackupPath() {
  std::filesystem::path p1 = std::filesystem::path(getConfigPath()) / "WorkSpaces_Backup";
  std::filesystem::create_directories(p1);

  if (GtwConfiguration::instance().isConfigLoaded()) {
    std::filesystem::path p2 = p1 / GtwConfiguration::instance().getCurrentWorkspaceName();
    std::filesystem::create_directories(p2);
  }

  return p1.string();
}

std::string GtwSysConfig::getWorkSpacesLocalOverridesPath() {
  std::filesystem::path p1 = std::filesystem::path(getConfigPath()) / "WorkSpaces_LocalOverrides";
  std::filesystem::create_directories(p1);

  if (GtwConfiguration::instance().isConfigLoaded()) {
    std::filesystem::path p2 = p1 / GtwConfiguration::instance().getCurrentWorkspaceName();
    std::filesystem::create_directories(p2);
  }

  return p1.string();
}

std::string GtwSysConfig::getLocalParamsPath() {
  return
    GtwSysConfig::getWorkSpacesLocalOverridesPath() + "/" +
    GtwSysConfig::sCurrentWorkSpaceName() + "/" +
    GtwSysConfig::sCurrentWorkSpaceName() + ".json";
}

std::string GtwSysConfig::getCurrentWorkSpacePath() {
  return (std::filesystem::path(getWorkSpacesPath()) /
    GtwConfiguration::instance().getCurrentWorkspaceName()).string();
}

std::string GtwSysConfig::getLocalWorkSpacePath() {
  return (std::filesystem::path(getWorkSpacesLocalOverridesPath()) /
    GtwConfiguration::instance().getCurrentWorkspaceName()).string();
}

std::string GtwSysConfig::getLicenseLogPath() {
    std::filesystem::path p = std::filesystem::path(getConfigPath()) / "LicenseLogs";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getSOELogPath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "SOELogs";
    return p.string();
}

void GtwSysConfig::SetRedundantIpAddresses(bool enable) {
    auto& config = GtwConfiguration::instance();

    auto configureNic = [](const std::string& nicName, const std::string& ipAddr, bool add) {
        if (nicName.empty()) return;

        std::string subnetMask = IpAddrUtils::calculateSubnetMask(24);
        std::string errorMessage;

        if (add) {
            if (IpAddrUtils::add_ip_address(nicName, ipAddr, subnetMask, errorMessage)) {
                LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Redundancy, nullptr,
                    "IP address %s added to adapter %s: %s",
                    ipAddr.c_str(), nicName.c_str(), errorMessage.c_str());
            }
            else {
                LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr,
                    "Failed to add IP address %s to adapter %s: %s",
                    ipAddr.c_str(), nicName.c_str(), errorMessage.c_str());
            }
        }
        else {
            if (IpAddrUtils::del_ip_address(nicName, ipAddr, errorMessage)) {
                LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Redundancy, nullptr,
                    "IP address %s removed from adapter %s: %s",
                    ipAddr.c_str(), nicName.c_str(), errorMessage.c_str());
            }
            else {
                LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr,
                    "Failed to remove IP address %s from adapter %s: %s",
                    ipAddr.c_str(), nicName.c_str(), errorMessage.c_str());
            }
        }
        };

    configureNic(config.getRedNic1(), config.getRedIpNic1(), enable);
    configureNic(config.getRedNic2(), config.getRedIpNic2(), enable);
}

std::string GtwSysConfig::GetTrialLicenseString() {
    return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
        "\n"
        "<hasp_info>\n"
        "\n"
        "  <haspscope>\n"
        "    <vendor id=\"102099\">\n"
        "      <hasp id=\"18446744073709551615\"/>\n"
        "    </vendor>\n"
        "  </haspscope>\n"
        "\n"
        "  <v2c>\n"
        "YoIarqCCGqWABHZ0YzKBggImXGCnHcD9H4/S7scsLgy/6yLUiVp8z8qw1P3hIYu9i/llS2pPLZoX\n"
        "83i73orNk0gLL0NvOn0+aEQkgt8qzS8OhzuJ62m89K7oWFLkmVPE8j+CSdNmai5OiqdKS3j3mbmL\n"
        "qRBgxotGRX+dvc2VJRF1fK/HTEbSwLMhEyZnSKyVIW0gT1/SY8twsUsdo6hEGagWRu3RwsC7lX52\n"
        "zMF0bZ5I7p6L3afyWAEkZ1OwoL43u18rHSWVlLqKxm+kCUCkGuUAl6IAkaMYQ7mkCzGpplQ/VbB3\n"
        "sIANw8WKtBY7JinC47coTBaGnBWiV1ECDqNuTlv08oq77TB3o49bK348P1q0CCB7t/6wsTkbSYcc\n"
        "1OuOf8VF6CBi5eb9twSxK426Aa+Tkj+fqn+9jZMlM2bcbbKKerXgp5RjmKQgapyiZZd7+CmeUYar\n"
        "eMvckRqLJp/n8cAZcfPj5naYElcTjsCxjIwSSMD3pvHsKR3mWQfJCJ/MqLkxuWN1YaSofW+NwWD7\n"
        "9nrQlVEut/ln41rx2CPD1qr9YiZpbC1WO30fYmQsAeYBjKFhxUqTTwrW4vGAstF8WpButWyVomA7\n"
        "/9qlVezm9TUShdqUEVlzAG6qMIo0VE9KuMGJOkhmm8xnu86wpghMrZAvBt73aO2HHjx9tdPGA4TP\n"
        "wbW4zglpN55bNyrQ9wgT6+scxZdytT7BA67Gk/eUWsArAyLOaHkUWlW1+jgnPAYK2Z8/FwudAoIB\n"
        "AIUBAYYDAY7TpIIYZoAJAP//////////gQEEpoIWfYAJAP//////////oRyhGoAFALwLhBOBAwCI\n"
        "24ICRi+DCJEdZlBl8dVto4IVRoAQ5DQolbEjpG+RHpbOqwpCG4GCFTAtaI5l7UITUY60weOkc9Kf\n"
        "RJPIsnQcZkGXzkqA4TnY+9X0j2dEeITeAguFswv5hKd1dos7uqqMSn3UNknmsjhJGbJZ6hDbq49y\n"
        "nzeDVKsEw5SUcw6iXIoRg7A+AVX2rzkp0j0IYfljG3+EkO8jSIckC7MW9AiFwDe0Z6e15TptU4ow\n"
        "mCMDoXqy78/049xRH1x6GJIjf3yGwKSN/3OIp/uQhNIDk7Pegm1CtXZ53k2GiVD4BtH27pBZi8XQ\n"
        "x0mw4Q48zt0qaUCRXYEAmyyW5RtigHM4kanzFEYyx4nrQJelDkJ8jSGPJWgEyp4LBRrK1X2yfN2K\n"
        "+fXGga3TDWvrPa11aLg+jAFMlXGEDbC19GN2XFUHdTyD/snD/OQC0/AkmwNhUvUmOkdu6fD+n0YD\n"
        "LR5OWtdZEx+j1N9T3Fk4/9Pnt9xMhMv2Iyv0taOT1bTG8IE5nJogYszrN3Zc7rI7+IUN6oQBQeh6\n"
        "CYumPLi/hFvZq3ZIBg3qGLTiKePOcOCXVKFuw05NZ7OFT4A9l/tuMiIMsHxzZkTqNEhkA1qoDNOq\n"
        "crsFC86J7OEWuEZaUtOHHgtjel5xLRDY0x+6igXBWuUQaPnNhbog5ZqLrn67aWo0QsszHzdKHgNW\n"
        "tohyZMGNF2J3FX0DemCoSBxupA74csHixXXlFKYU6SXTOrigbwYhaRRfTEr4BbL4OHnczFL+ycCC\n"
        "MpPAlcv1Esj7JgwxwpNdp+2Nln1C2TnaQg/0u4xJpiNyJ7iejjzOVREDCssiru4NibM8W7sA/7W0\n"
        "5SyqvY7n7Jt+B09X6TFuJClSBeQA2P8mTWRq4tZ62IcF/f75BQZrrPE/MUlryQezFVRZuNmZYoN+\n"
        "+D4sW/GC6fc4RbbF8fUu5KazhZ74F/hpgTzEIe6U6d2RPtEWshkEzENCHq+fDt0DR5SD7GHCW1S0\n"
        "ruT24j2oaI52xlP30zdAQi3S4dVkV6cIa91thA804yQrVf+pctbfAJq7bLJ5rl3ORmlFq6MNSNj2\n"
        "iGk58GtT2W5PlrtYevOLJz9Ij2/FCFvOmWpmlOrMs+QNuQZYmIEeX405xguFwXHc1ApP+FdCvc6B\n"
        "HBt7JAvJIKeha2HPpVgIrZE5GDAOLkXSoX62NaJ+wLN2z+7ieo414gIx58OmcjCnQdcJd5IPEptb\n"
        "HbY3zSsbBH1gI85PWJCn5Ge0ri8h02Aql8BKm1LSl42Ib/Mvu5ALpdijKCa+YoHnTZwVjim+bzL+\n"
        "d0VS5uSeGlsEsiLpcu4PW2JLwZ2Ln1s1IQRdVaeDUpDKIWbO7zLgxVETd9xMF2df6yedNPRWDIyT\n"
        "/iw11xSNdbeRpnueT46qRxa1P4G+LvcWtnJ0StPmxo1++sCY3rm5bAB2uoVnXw97cmDBczwP53uM\n"
        "SLSzx+FT85ZvTBNE47ZrZcezq0ClSJWiSioFILXS51UN8WOfGaRAah+vefnnmb8TA4QtgHBgDy/8\n"
        "UDzjbvovpSqRlfISkCY4nO/p1TinpZR3f+LhXmw1UnM1i7Ci2ejDecphQjR5OrQF93lY0I97qpye\n"
        "2TmxqYeJcPgYnGEkhFkPCsu/xScHk/uDi4RcnSroP4e0ugJ7QlcVTKK9XYLELbktbvGw9tDVX0Kq\n"
        "RJ7EcwZV/rNIM8e+vLjEVAjqAMJKfRgge762rbdO+WNf0wP4CJjKxAje0s5foM0AHZdIwUqP74EX\n"
        "0JWhm3zHg9gu902dhuI9llkJS8LUmuirXK8JBK/HosssXg+dAymcWE8cKTaFFca7kpfNacKpJtEX\n"
        "juHnejHcGjVEmO23rDexqlDmPpQmysCROcWOFhjKiztVCTVsOb95nlG2sObI0SJqVFlCN0X8whkd\n"
        "2bhEa2we6iKuUaOWJogt0miQxBibON1DEacxwQGeTEwc8D6dU1CXY0WkmyLRpMm0uYiXYFvpiZY5\n"
        "Dw0aESmzLjrCGn+kUQ2dupOFbY3bdYAJD+I+LRrwL51Ne8lht1CBJS4CryQqEm3eT5wlMizwr4jp\n"
        "WKyYsBlWxeF+YkRznUwqjGOzkfzz6TFARpcdsfySY89K82bpLqwoV+ExJSVCUZZ1RiJxSINIfCSZ\n"
        "HVKGBen0SOTtmM4j8TPa9i94VGDNXegKsWylbC5MdO4iRYrB+eM3m/kn8hkP9vAiVAZX5kl7+0Lm\n"
        "RTlAGSH76Kqc6xRcaHgGAZyRfYbnOUZEh2i6kOJwgFZdATk7dxdvaxdQhiDOos67MYE7fRrp0bRN\n"
        "mhnlU3dqArjOzeqyXMnAGbL1ZErRWNQX6qFKrZPy0cRkeuk+QIcxMTT74YSv6mkSMa4g+HiwLtaH\n"
        "mlC5h6mLuwAKm7lWEsJbHQYjIESy4eBussIR+4SD6dA1g3hL6WV4OlhSD49PPSNCdV2MVQGl+nLE\n"
        "DEERvPZ59+Vkzma8GWmBypjI6uePIvFxhBc15ipAQu5rXaSTXAy4LDtAl2kzbV0mEYjj/DmjeyKR\n"
        "pdS5pAZNU64g8/jWo3bFzCbneIS8f01GkU8u9C43nCbrIw96ovKgJq23gTAK2UjXn3ToShrwbcI5\n"
        "klyebHUybCGBG5vnU97/8OyKMbUc89lQ+7TXOJINKbB3jFgN6qsVSFMi9EGbF6VCeuFZh9BS684w\n"
        "js6llzAYHH5TWer8UN2fyxeKySPDlTVvexQjiNZxnc1AZALXmXgoqMVrX4OD15bf/wvyD472K1Gf\n"
        "h68Owv0BDjNFhYovmRv07oQTicISKKcV33eED8JbdHKgZpmkt9Cza0d4QmZ8Cwe76+Ak9d3s+Jcv\n"
        "9uVJCYtU1KOE/nuFHd/QBQi2xajcYYBUKJzcwiIDCC+UhKk627i5ZKq1YTBosB0jtZa1A5frXM44\n"
        "s3lbT9+d/H+d5E7B+jYwHSgdCvpPnqFwLBL9He7AIhOdxKLMOhrfbqUGpnYdrkZ7uYqgEmBN36Q3\n"
        "MBsL9fF/lJ/VCanXnTq6i/CeCeLqj85xJ/D6DwQUK/mumWnCIAfkQK10gMIdGlGpcgb+nzsXzVss\n"
        "hUHDVIoeQlHzSjzEK/wnYU28cdm4hXdlArNqAzhcH9bDlR087NBFjJYWsVR22g7GZ9VYnjF5QvBd\n"
        "etN31qPodTFI+987tmOLV2g+hwBKX+0cjrHthCjttRx2r/+tNcHp3v5CjDf3lqdFWdMk9G9XMlpE\n"
        "2GBX4hlgL6NC26ByPKGJpj+C3oGb20YmpI9BVVNZAfQBJ2DhKdkJIZ141BPtJ0SpZ1Qb9r6sS8Vz\n"
        "gmouSRPylrJfpldhgh9fCOxehorVTGkldP86s9CfSUGLnGOgII8WZzHtu09Q9oA43iIaLsMQestS\n"
        "bO0p+aSkn1KDVsdOf3atywp/IJbc0u0ZQxsqeKldR7DZ6HHcLLwS1OQDf0/pR3cluhTN6GEweQil\n"
        "kijVKOAp7p8tonkMpEyYbjH1YmEgZO/HyT+4YdwDilOnmGz+yNWTSbx4GK02RJYura9LyLpjlPWR\n"
        "QPwoJ8dxOnlIlTafuEkB/N0fiZArkb3sTQRTR7puAN4rBGuEeKMPfXl9LP2hnr+3n19KTDIN2taH\n"
        "4n7NEKbc9mxKtCiHbb0Y7buTOEPuGAehl6I1eb6lmynRUBbnaJ3bfXbGBAmPqGXHsPuPDTSlnDZN\n"
        "YdBlJDmGRS2MPasbdJ4VyBNqasc9r1hv2aSP7NJ/DV34yEw9k6BiOwcE6bs4nu7mz4Elb0z2nKio\n"
        "jA1Nc96/UYVcp5eLUwtZRZ9+x9BRO+ck1ys2/mwSqpvCYyANmM5sBLYzxRjwE5+HPGj24FqyTUQW\n"
        "l4CjsliGfDbLyMOGkbN2kUNspAqfTC484PFDpWtSTJ/CggKY93aRYdJapN2leM/pZZlEqY6mVMXl\n"
        "plo5x5AN5cY4dQ9H744TyM89+ZXnec/6uYq+hbdLWTk9/QcXgam8SX7tBtyE39yWmPRERbBBq33E\n"
        "0TrOBwC6uchP38jzXhTZt+n+h4lA3kCj9WGDTGqxs8pLfwaIuHUisLcNz/ADHBZFmXqttv1ETg5L\n"
        "BX3YQG8gy1SnWef0Hp5didmqvAjSM9RkKw3JvPTzxd+vsZj4Re940OylWFHCGeoWRkwu6PZh/Kw5\n"
        "Z8qmarA7mrQWS60IItouQOGfRljTB5y/EL2OQMX06nXdK1s0Y3F0lAo9aFD6BfkOuctgCsNtIBkD\n"
        "AiMjnXROS6ETWEBxdWE/cdOx1ITCeBnR8ZsffH1Joa4gjB7EJ0uCimfZyfIcLl0JSbpJ1XIi38NS\n"
        "6aH5q6gmmcoFlsn8dqGpferlJGi9VO084oPn9Mttt/y0AA9OhBUxU+cx06FruXZ3+loBVZjXFvVj\n"
        "5+/7GrUatFLXVgVW7MZMZwAOB8xwyE60oLkZA1yrOLRG1+EhWbn7khsBaIcYahqyuu9ac2t16/dB\n"
        "1WszYHMGnYoYn3kofd+MWEdvJQVDlJylW0u6nsyFWUjK16JX+cjOeRM61XWCrILBuDu7mDp2zL/j\n"
        "S0jLJceSnyuDCv4BN9qmXVZYkrxMZn7uoKMPCWxZlViLTjsCHoTrfU0xsy6SMhkP9HZWQlN8C2Ge\n"
        "kccS7V6R6GAnRiWd0mLdBckx6B8ucZF+ynZakdOZXnIDSRfe2mIsR6RIKNnh58t5B5DqyCvler10\n"
        "bQhcrBz1KY8ZXur0noZKadzGQ2qkH1nJqt3CkMfcxuJJTjEZPPUsNd4Hl/n8bmTZ70ZMN3YKiIw9\n"
        "FXz1mo5/DKzLyo1xPhO6Sq4NO1YoOnFf5+jCgHlfnAuI3M7IRqhX4RwVs5BFQWFzk38qG/ZcQs8d\n"
        "6Z8C55VpzVE3h9gC9vHc+E+qw6wQ0k4hYSe8zWNRBwSgHvEPdXWpCpIzcJGSuA+X5Kk/8/Msa1iX\n"
        "S9q+1hSB2gT5oIMHuzUWe4GA6NgOCclkTj76vVwhBCo0DNW6hAAul749F0833S2sXD8uf9AOr5E9\n"
        "UqEjurzChnaP7xWjcHzSIR2oL4/lzuV19pJCowWYxD5BXAFAHSzCqw7VVRyzogR9LM30CzkQnSYG\n"
        "Px5GIQR1MPDMcc9VVrgEB6euF7LCt+DtXeXtDurADZIuac0nq0B3X1PGKFLRaznB0CRSVZN/pXUp\n"
        "3rCx7pv/LPrzCy11xIRi1ihU/WRXaQt85kuLFSLk3P/NZJyVH5kBRTlfs/gmux9gDi1ivFDfVHoF\n"
        "pYCcJn80/CDL9PWSz73WvRqn0LzcLbHyJk/Zu4KWPLzcmOopoVQeaZ3axBn6GbJS6+oVcXEBa6hs\n"
        "LO5W6xDHq/f5XJMYrqhbyX/YgyMXG9ZrYQQS/KVWEZfkAyMk7WKNE5T1VqJiPAoKma9EAPA79sof\n"
        "YC4vFD5zNlJhkHyyfwWyeqqL4FjMQoqMkUs6xiWPkhTJRWyqEw+9ZcfqPF20xH8SiKv0PLX4q5gJ\n"
        "ctZxeRo5AajrmP9kHd7V92fLZVEtVIAJr6JLph93yI8a0vt32A4rFjjQELnJPKf25+hMMsnYHYmE\n"
        "LxtYK7ULuuZNdhd1MoNL7XJ9OqSnoBS+H4UhbS5fCL9R01US/a/oPgaOFbSW/sBgqLn27j+sv3Jp\n"
        "d5RECpdGKUfJ12m1Cel9yWdAgLj+jwNSMHuOqi4ZFyLrfy05Y8zN+xUFxfdvPwQhyOm7WpUt1Rh5\n"
        "3QXp9I+gVKxtKH3dFpSoeT4Gd6LSgRQwK/uisvVqZaI2JpeFfebfP0ex6jNkTWOTRtACjhiVIJmt\n"
        "S0N+QUeSk5utxzE4KBYHFNUY9bKKPGnaDjghxc4sQFuzcho8N1HYVbGkDGsxbXRw8MD/7g7MqMf1\n"
        "BByhA7VBIhtTLeiSdtWuJRYYhuT4Izw1WgvWIMf3er37WKjrCibam2zxmLEHh4ezZzJ7lFyyq3O1\n"
        "F9+QBr2Eqz7zBW7xQLRSgmseEp0HOlhEmJyHJbNv4B7OPLkAbXHUxyOoEiqfxax1IgfHdleqPBAP\n"
        "YYwRNYa7iPIEcYYPPS4EmE2LQ+E1Db3/JLtx66Xjm/Q1xIt5QLD0JcdrjeUDFhFEXMyqLJ83Nlvp\n"
        "QGGQhGRxA3E5+YVPBFggZ9b+Lz5aBlAsraSa2TMOUc+p78gNEMtwUy0ioFwKIUdKXreub6XGt5tV\n"
        "okAWn31sVzPyH3usUMsXyHqanNT5RqY8EqHk5WhbFCO9FprLU6PwraN+oBw6wZXqN4LxfxWY33PR\n"
        "5ROD51cHgUb5K4AEUNAOd4o3OmsvQvwv4dGCkmXLRlOhZQg2Yp7eGDXusZI4uN9OYD49AfyxI135\n"
        "D1DAcdtFlRtlYvMeSbZmJOp2ZUV0260VrGmndARNz8lXqB5hZ/jRxewvXnRJ5YFctQujfZ7KPkIv\n"
        "w8jjgbGnPBqyfd9JKBEckOBCBchWSF8ezYoKbKOaLhFiJo53cL5gtaL5HtwLcGRPmPBVjpIVfFjp\n"
        "IJjogThV+0SYTBZZyGVBqhKaoM4WUwNa02zAWlML5bwj8yoc6Q/3hqseLGiBeSBHFfBD+wqKnPJt\n"
        "oQXnw8Ehs6rqm9HG2VZTKGT/eDZac98RZ7aPfIMW7jtQdu2ZoO2P+G3/fIGjqkyl2nEpOqZOor49\n"
        "71P6UaMqvEcNabrkwMb/9mSPp6RFMF//9LqJXKg5W7AAhcFLzYcsiJBUkf9NB0BTAMu6NqZhp3zK\n"
        "O+mMjLyqF/3HNwA7rol2f9a86AM4/d0eUTXd7IS/x+0uHPiwEQ3gqjfQFDwV5H3jj8idP0171+Wv\n"
        "NwJhHSyUMKHmgRKGZItAhnEc0QKkCvtvi/f/0pRnGl9ST5JJthCEGJaInR+53B8WPh74xLTnajwW\n"
        "ljs9WCQP639RdB3t04giVDfH3cSKMWuauE92qlvcef4Ic7/lZq1d7l5bIrvCkIyi+nsq2UpmDZd1\n"
        "3GN4cxyLH/jd54M1fTaQl6pmgY26DEdaRdVZPKxalNFhJLHLaqjMC+fsvimxZ3gMwTmkOta89ai5\n"
        "nyi5e1xSKDjVTsv8Z+SahXA0ZjELbPEYgA1kfva5xjuBzI9YIrBDgtmjV5kRg4J77jkCzy6+/JkN\n"
        "TV0lvTJt5ra/iG+qGLrfBY1731qPhyddUhXlAM6lJ0TwGskRzHmbcWKied/9nCPC3EgKos6+TVJp\n"
        "E8jQuAm4v/7/3ysZeOgdZhMSw3FbQulmhwGq1Y5eYus8XFdKE0hNV/dMe/GdPCYj5J/ExkUAebdg\n"
        "hN4v2G8ms/VuNIEOawsrCNhwkUsT2W7Pwi4e3G+4T+/NBpHsxN0VAuokbWXXkoed+Xf613EaJZG4\n"
        "MApp96S2IHLnRZooKHySomXRfZNMVeWGJv7R2TorStNUTLKv7sMnvSK+6TjwjCqVVVWHAQGIAQCK\n"
        "ggEAXR0tgja8H0yUJvyoSXUgW+9CC5UO0FQUU7OWsbVTkhkC9epo9dGC+CSeIoKsYk1GPr8Sq8eO\n"
        "qKOQy3Q+CffcTPXzjTV4UEnf3uPIQkrmCfVMtOSjA5EsnDcxh9pAXclJ3vtvEtJV78Q0BEycerFY\n"
        "Bc2DIoJGhSpEXVFV2ebNQDYDzAnQIrN/hcG/8b4ame87vIFoBrCXyEHaOSLYWsBVoy0ABXlmS7LZ\n"
        "1Rfs7zvitcq9qnQpWIIqq7Rl2wj+PxZ0IVbxU4Mt9/BFGzdnw/rTdyMoGhMIEK851JQ6j3h/IzCH\n"
        "gRryGLIA/t6NyiS5EWYEX11kYfti5QemvFk0YTA35oQBBKWCAdCgBYADAY7ToQmAAQaBBFNERwCi\n"
        "A4ABAKMWgAFfgRFTREcgT1BDVUEgU2VydmVyAKMRgAEqgQxTREcgT1BDIEFFUwCjD4ABH4EKU0RH\n"
        "IE1ETlAzAKMQgAEigQtTREcgUzYxODUwAKMQgAEjgQtTREcgQzYxODUwAKMOgAEZgQlTREcgTTEw\n"
        "NACjD4ABHoEKU0RHIFNETlAzAKMRgAEpgQxTREcgT1BDIEFFQwCjDoABGIEJU0RHIE0xMDMAoxGA\n"
        "ASeBDFNERyBPUEMgREFTAKMVgAEsgRBTREcgT0RCQyBDbGllbnQAow6AAR2BCVNERyBTMTA0AKMW\n"
        "gAFggRFTREcgT1BDVUEgQ2xpZW50AKMagAEtgRVTREcgVW5saW1pdGVkIFBvaW50cwCjE4ABE4EO\n"
        "U0RHIEJhc2UgU2VhdACjD4ABL4EKRGVtbyBCYXNlAKMPgAEkgQpTREcgVEFTRTIAoxGAASiBDFNE\n"
        "RyBPUEMgREFDAKMRgAElgQxTREcgTU1vZGJ1cwCjDoABFoEJU0RHIE0xMDEAow6AARqBCVNERyBT\n"
        "MTAxAKMVgAErgRBTREcgT1BDIFhNTCBEQVMAoxGAASaBDFNERyBTTW9kYnVzAKEDgAEA</v2c>\n"
        "\n"
        "</hasp_info>";
}

void GtwSysConfig::decodeFilePath(std::string& input) {
    CStdString s = input;
    s.Replace("%3A", ":");
    s.Replace("%5C", "\\");
    s.Replace("%2F", "/");
    input = s;
}

// Certificate paths
std::string GtwSysConfig::getPrivateKeyPath() {
    std::filesystem::path p_intermediate_dir = std::filesystem::path(getConfigPath()) / "ssl";
    std::filesystem::path p = p_intermediate_dir / "private";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getPublicKeyPath() {
    std::filesystem::path p_intermediate_dir = std::filesystem::path(getConfigPath()) / "ssl";
    std::filesystem::path p = p_intermediate_dir / "certs";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getOPCUAServerTrustedCertificatePath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "opcua_certs" / "trusted";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getOPCUAServerRejectedCertificatePath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "opcua_certs" / "rejected";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getOPCUAClientTrustedCertificatePath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "opcua_client_certs" / "trusted";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getLocalOPCUAClientTrustedCertificatePath() {
  std::filesystem::path p = std::filesystem::path(getLocalWorkSpacePath()) / "opcua_client_certs" / "trusted";
  std::filesystem::create_directories(p);
  return p.string();
}

std::string GtwSysConfig::getOPCUAClientRejectedCertificatePath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "opcua_client_certs" / "rejected";
    std::filesystem::create_directories(p);
    return p.string();
}

std::string GtwSysConfig::getChanTLSCertificateAuthorityDirPath() {
    std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath());
    std::filesystem::create_directories(p);
    return p.string();
}

double GtwSysConfig::gtwAuthRoleExp(USER_ROLE_ENUM role) {
    auto& config = GtwConfiguration::instance();
    switch (role) {
    case VIEWER_ROLE: return config.getAuthExpViewer();
    case OPERATOR_ROLE: return config.getAuthExpOperator();
    case CONFIGURATOR_ROLE: return config.getAuthExpConfigurator();
    case SU_ROLE: return config.getAuthExpSU();
    default: return 600;  // 10 minutes default
    }
}

// Runtime state management
void GtwSysConfig::SetRunningAsMonitor(bool b) {
    auto& config = GtwConfiguration::instance();
    config.setRunningAsMonitor(b);
}

void GtwSysConfig::SetRunningAsService(bool b) {
    auto& config = GtwConfiguration::instance();
    config.setRunningAsService(b);
}

void GtwSysConfig::SetRunningAsEngine(bool b) {
    auto& config = GtwConfiguration::instance();
    config.setRunningAsEngine(b);
}

void GtwSysConfig::SetRunningAsServiceFromConsole(bool b) {
    auto& config = GtwConfiguration::instance();
    config.setRunningAsServiceFromConsole(b);
}

bool GtwSysConfig::GetRunningAsMonitor() {
    return GtwConfiguration::instance().isRunningAsMonitor();
}

bool GtwSysConfig::GetRunningAsService() {
    return GtwConfiguration::instance().isRunningAsService();
}

bool GtwSysConfig::GetRunningAsEngine() {
    return GtwConfiguration::instance().isRunningAsEngine();
}

bool GtwSysConfig::GetRunningAsServiceFromConsole() {
    return GtwConfiguration::instance().isRunningAsServiceFromConsole();
}

// Network address formatting helpers
std::string GtwSysConfig::GetREDHostAddrOfPeer() {
    auto& config = GtwConfiguration::instance();
    return config.formatHostAddr(config.getRedPeerHost(), config.getRedPort());
}

std::string GtwSysConfig::GetMONHostAddrOfPeer() {
    auto& config = GtwConfiguration::instance();
    return config.formatHostAddr(config.getRedPeerHost(), config.getMonPort());
}

std::string GtwSysConfig::getTimeZonePath() {
    auto& config = GtwConfiguration::instance();
    std::string& tzPath = config.getTzPath();

    if (tzPath.empty()) {
#ifdef _WIN32
        tzPath = (getExecutablePath() / "zoneinfo").string();
        _putenv(("TZDIR=" + tzPath).c_str());
#else
        tzPath = "/usr/share/zoneinfo";
#endif
    }
    return tzPath;
}

std::string GtwSysConfig::GetEngineINIFullName() {
    return (std::filesystem::path(getCurrentWorkSpacePath()) /
        (GtwConfiguration::instance().getCurrentWorkspaceName() + ".ini")).string();
}

// WebDirConfig implementations
std::string WebDirConfig::getDefault() const {
    std::filesystem::path defaultPath;
#ifdef _WIN32
    wchar_t path[MAX_PATH] = { 0 };
    GetModuleFileNameW(NULL, path, MAX_PATH);
    defaultPath = std::filesystem::path(path).parent_path() / "web";
#else
    defaultPath = "/usr/local/share/sdg/web";
#endif
    return defaultPath.string();
}

void WebDirConfig::onChanged(const std::string& oldValue, const std::string& newValue) {
    // Create directory if it doesn't exist
    try {
        std::filesystem::create_directories(newValue);
    }
    catch (const std::filesystem::filesystem_error&) {
        // Log error or handle as needed
    }
}

// TzPathConfig implementations
std::string TzPathConfig::getDefault() const {
#ifdef _WIN32
    std::filesystem::path execPath;
    wchar_t path[MAX_PATH] = { 0 };
    GetModuleFileNameW(NULL, path, MAX_PATH);
    execPath = std::filesystem::path(path).parent_path() / "zoneinfo";
    return execPath.string();
#else
    return "/usr/share/zoneinfo";
#endif
}

void TzPathConfig::onChanged(const std::string& oldValue, const std::string& newValue) {
    // Create directory if it doesn't exist
    try {
        std::filesystem::create_directories(newValue);
    }
    catch (const std::filesystem::filesystem_error&) {
        // Log error or handle as needed
    }
}

// MaxLogFilesConfig implementations
void MaxLogFilesConfig::onChanged(const int& oldValue, const int& newValue) {
    // Implement any validation or cleanup logic needed when max log files changes
    // For example, might want to clean up old logs if the new value is lower
}

// GtwSysConfig static method implementations
std::string GtwSysConfig::getWebPath()
{
  if (GtwSysConfig::gtwWebDir() == "")
  {
#ifdef _WIN32
    GtwSysConfig::gtwWebDir() = (getExecutablePath() / "web").string();
    std::string webpath = GtwSysConfig::gtwWebDir();
    if (std::filesystem::exists(webpath) == false)
    {
      GtwSysConfig::gtwWebDir() = (getExecutablePath() / "../gateway/GTWWebApp").string();
    }
#else
    GtwSysConfig::gtwWebDir() = "/usr/share/tmw/sdg/web";
#endif
  }

  if (std::filesystem::exists(GtwSysConfig::gtwWebDir()) == false)
  {
    std::filesystem::create_directories(GtwSysConfig::gtwWebDir().c_str());
    sdg_os_utils::gtw_ofstream outfile(GtwSysConfig::gtwWebDir() + "/" + "index.html");

    outfile << "<!DOCTYPE html>" << std::endl;
    outfile << "  <html>" << std::endl;
    outfile << "  <body>" << std::endl;

    outfile << "    <h1>TMW SCADA Data Gateway </h1>" << std::endl;

    outfile << "    <p>Website is missing at:" << GtwSysConfig::gtwWebDir() << "</p>" << std::endl;
    outfile << "    <p>Please install it. </p>" << std::endl;

    outfile << "  </body>" << std::endl;
    outfile << "</html>" << std::endl;

    outfile.close();
  }

  return GtwSysConfig::gtwWebDir();
}


std::string GtwSysConfig::getSDGLogPath()
{
  std::filesystem::path p = std::filesystem::path(getCurrentWorkSpacePath()) / "SDGLogs";
  return p.string();
}

VALIDATE_RESULT_ENUM GtwSysConfig::ValidateGtwConfig(std::string& message) {
    if (!GtwSysConfig::gtwDoValidateConfig()) {
        return VALIDATE_RESULT_NOT_DONE;
    }

    message = "";
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "verifying-https-certificates");

    if (GtwSysConfig::gtwUseWebSSL() == true &&
        GtwSysConfig::isMonitorProc() &&
        (httpsPrivateKeyFile() == "" || httpsCertificateFile() == "")) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "https-certificates-validation-failed: missing required certificates");
        message += "The HTTPS private and/or public certificates were not specified in " +
            std::string(GtwSysConfig::getConfigFileName()) +
            " (obtain certificates from a certificate authority or use the GTWSettings tool to create them as self signed certificates)";
        return VALIDATE_RESULT_FAIL;
    }

    try {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "checking-sdg-config-directory");
        if (!std::filesystem::is_directory(getConfigPath())) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "sdg-config-directory-validation-failed: directory not found");
            message += "SDG config directory not found at: " + getConfigPath();
            return VALIDATE_RESULT_FAIL;
        }
    }
    catch (const std::exception&) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "sdg-config-directory-validation-failed: directory access error");
        message += "SDG config directory not found at: " + getConfigPath();
        return VALIDATE_RESULT_FAIL;
    }

    try {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "checking-web-directory");
        if (!std::filesystem::is_directory(getWebPath())) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "web-directory-validation-failed: directory not found");
            message += "web directory not found at: " + getWebPath();
            return VALIDATE_RESULT_FAIL;
        }
    }
    catch (const std::exception&) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "web-directory-validation-failed: directory access error");
        message += "web directory not found at: " + getWebPath();
        return VALIDATE_RESULT_FAIL;
    }

#ifdef _WIN32
    try {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "checking-timezone-directory");
        if (!std::filesystem::is_directory(gtwTzPath())) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "timezone-directory-validation-failed: directory not found");
            message += "zoneinfo directory not found at: " + gtwTzPath();
            return VALIDATE_RESULT_FAIL;
        }
    }
    catch (const std::exception&) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "timezone-directory-validation-failed: directory access error");
        message += "zoneinfo directory not found at: " + gtwTzPath();
        return VALIDATE_RESULT_FAIL;
    }
#endif

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-engine-host");
    if (gtwHost() == "localhost") {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "engine-host-validation-failed: localhost not allowed");
        message += "Engine host can not be: " + gtwHost();
        gtwHost() = "127.0.0.1";
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-monitor-host");
    if (monHost() == "localhost") {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "monitor-host-validation-failed: localhost not allowed");
        message += "Monitor host can not be: " + monHost();
        monHost() = "127.0.0.1";
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-redundancy-host");
    if (redHost() == "localhost") {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "redundancy-host-validation-failed: localhost not allowed");
        message += "Redundancy host can not be: " + redHost();
        redHost() = "127.0.0.1";
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-engine-host-connectivity");
    if (GtwSysConfig::isMonitorProc() && ping(gtwHost().c_str(), 2) == 0) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "engine-host-connectivity-failed: host not responding");
        message += "Engine host ip addr not found at: " + gtwHost();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-redundancy-peer-host");
    if (GtwSysConfig::isRedundancyProc() && redEnable() == true && redPeerHost() == redHost()) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "redundancy-peer-host-validation-failed: peer host matches local host");
        message += "Redundancy peer host ip addr can not be the same as the local machine ip addr: " + redPeerHost();
        return VALIDATE_RESULT_ERROR;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-redundancy-host-connectivity");
    if (GtwSysConfig::isRedundancyProc() && ping(redHost().c_str(), 2) == 0) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "redundancy-host-connectivity-failed: host not responding");
        message += "Redundancy host ip addr not found at: " + redHost();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-redundancy-peer-connectivity");
    if (GtwSysConfig::isRedundancyProc() && ping(redPeerHost().c_str(), 2) == 0) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "redundancy-peer-connectivity-failed: peer not responding");
        message += "Redundancy peer host ip addr not found at: " + redPeerHost();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-monitor-host-connectivity");
    if (GtwSysConfig::isEngineProc() && ping(monHost().c_str(), 2) == 0) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "monitor-host-connectivity-failed: host not responding");
        message += "Monitor host ip addr not found at: " + monHost();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-monitor-port-availability");
    if (GtwSysConfig::isMonitorProc() && port_in_use(monHttpPort(), 0) == true) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "monitor-port-validation-failed: port already in use");
        message += "Monitor ip port is in use at port: " + GetMONHostAddr();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-redundancy-port-availability");
    if (GtwSysConfig::isRedundancyProc() && port_in_use(redHttpPort(), 0) == true) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "redundancy-port-validation-failed: port already in use");
        message += "Monitor ip port is in use at port: " + GetREDHostAddr();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-engine-port-availability");
    if (GtwSysConfig::isEngineProc() && port_in_use(gtwHttpPort(), 0) == true) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "engine-port-validation-failed: port already in use");
        message += "SDG Engine ip port is in use at port: " + GetSDGHostAddr();
        return VALIDATE_RESULT_WARNING;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "checking-license-server-availability");
    if (GtwSysConfig::isEngineProc() && port_in_use(1947, 0) == false) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "license-server-validation-failed: server not available");
        message += "License server not available at port 1947. Is the firewall blocking it or is it running?";
        return VALIDATE_RESULT_ERROR;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-workspace-name");
    if (sCurrentWorkSpaceName() == "" && GtwSysConfig::isEngineProc()) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "workspace-name-validation-failed: name not specified");
        message += "A file name (ex tmwgtway.ini) must be specified for 'currentWorkSpaceName' in " +
            std::string(GtwSysConfig::getConfigFileName());
        return VALIDATE_RESULT_ERROR;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-workspace-path");
    if (sCurrentWorkSpaceName() != "" && GtwSysConfig::isEngineProc()) {
        std::string workspacePath = getCurrentWorkSpacePath();
        if (!std::filesystem::exists(workspacePath)) {
            LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "workspace directory does not exist, creating: %s", workspacePath.c_str());

            // Try to create the workspace directory
            try {
                if (!std::filesystem::create_directories(workspacePath)) {
                    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                        "workspace-path-validation-failed: failed to create workspace directory: %s", workspacePath.c_str());
                    message += "Failed to create workspace directory: " + workspacePath + ". Please check permissions or update 'currentWorkSpaceName' in " +
                        std::string(GtwSysConfig::getConfigFileName());
                    return VALIDATE_RESULT_ERROR;
                }

                LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
                    "workspace directory created successfully: %s", workspacePath.c_str());
            } catch (const std::exception& e) {
                LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                    "workspace-path-validation-failed: exception creating workspace directory: %s - %s", workspacePath.c_str(), e.what());
                message += "Failed to create workspace directory: " + workspacePath + " - " + e.what() + ". Please check permissions or update 'currentWorkSpaceName' in " +
                    std::string(GtwSysConfig::getConfigFileName());
                return VALIDATE_RESULT_ERROR;
            }
        }
    }

    {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "validating-https-certificate-path");
        std::filesystem::path path(GtwSysConfig::httpsCertificateFile());
        if (path.has_parent_path()) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "https-certificate-path-validation-failed: path not allowed");
            message += "httpsCertificateFile must not contain a path in " +
                std::string(GtwSysConfig::getConfigFileName());
            return VALIDATE_RESULT_FAIL;
        }
    }

    {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "validating-https-private-key-path");
        std::filesystem::path path(GtwSysConfig::httpsPrivateKeyFile());
        if (path.has_parent_path()) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "https-private-key-path-validation-failed: path not allowed");
            message += "httpsPrivateKeyFile must not contain a path in " +
                std::string(GtwSysConfig::getConfigFileName());
            return VALIDATE_RESULT_FAIL;
        }
    }

#if USE_CA_CERT
    {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "validating-https-cert-auth-path");
        std::filesystem::path path(GtwSysConfig::httpsCertAuthFile());
        if (path.has_parent_path()) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "https-cert-auth-path-validation-failed: path not allowed");
            message += "httpsCertAuthFile must not contain a path in " +
                std::string(GtwSysConfig::getConfigFileName());
            return VALIDATE_RESULT_FAIL;
        }
    }
#endif

#if USE_CA_PW
    {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "validating-https-private-key-passphrase");
        if (GtwSysConfig::httpsPrivateKeyPassPhrase() != "" &&
            GtwSysConfig::httpsPrivateKeyFile() == "") {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                "%s", "https-private-key-passphrase-validation-failed: missing key file");
            message += "if httpsPrivateKeyPassPhrase is specified httpsPrivateKeyFile must be specified in " +
                std::string(GtwSysConfig::getConfigFileName());
            return VALIDATE_RESULT_FAIL;
        }
    }
#endif

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "%s", "validating-timezone");
    if (!GtwTimeZone::IsValidTimeZone(gtwLogTimeZone())) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "%s", "timezone-validation-failed: invalid timezone specified");
        message += "TimeZone " + gtwLogTimeZone() + " is invalid.";
        return VALIDATE_RESULT_FAIL;
    }

    return VALIDATE_RESULT_SUCCESS;
}

void GtwSysConfig::SaveGtwConfig(bool saveWSconfig) {
  nlohmann::json global;
  nlohmann::json workspace;
  std::filesystem::path globalFile = std::filesystem::path(getConfigPath()) /
        std::filesystem::path(getConfigFileName());
  std::filesystem::path workspaceFile =
    std::filesystem::path(GtwSysConfig::getCurrentWorkSpacePath()) / "ws_config.json";

    try {
        auto& config = GtwConfiguration::instance();

        // Ensure defaults for critical values
        if (config.httpPort().get() == 0) {
            config.httpPort().set(58080);
        }
        if (config.gtwHost().get().empty()) {
            config.gtwHost().set("127.0.0.1");
        }
        if (config.monPort().get() == 0) {
            config.monPort().set(58090);
        }
        if (config.monHost().get().empty()) {
            config.monHost().set("127.0.0.1");
        }
        if (config.redPort().get() == 0) {
            config.redPort().set(58070);
        }
        if (config.redHost().get().empty()) {
            config.redHost().set("127.0.0.1");
        }
        if (config.redPeerHost().get().empty()) {
            config.redPeerHost().set("127.0.0.1");
        }

        // Initialize web directory if needed
        if (config.webDir().get().empty()) {
            config.initializeWebDir();
        }

        // Initialize timezone path if needed
        if (config.tzPath().get().empty()) {
            config.initializeTzPath();
        }

        // Ensure other values are within bounds
        if (config.wsUpdateRate().get() == 0) {
            config.wsUpdateRate().set(5);
        }
        if (config.licGracePeriodInMinutes().get() > 1440) {
            config.licGracePeriodInMinutes().set(1440);
        }
        if (config.wsUpdateBlockSize().get() == 0) {
            config.wsUpdateBlockSize().set(100);
        }

        // Save configuration to JSON
        config.saveToJson(true, global, workspace);
        //pt["gtwPasswordComplexity"] = gtwPasswordComplexity();

        // Write to global file
        sdg_os_utils::gtw_ofstream configStreamG(globalFile.string());
        configStreamG << global.dump(2);
        configStreamG.close();

        if (saveWSconfig)
        {
	        // Write to work space file
	        sdg_os_utils::gtw_ofstream configStreamWS(workspaceFile.string());
	        configStreamWS << workspace.dump(2);
	        configStreamWS.close();
        }
    }
    catch (std::exception& e) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr,
            "Failed to save configuration: %s", e.what());
    }

    // Post-save processing
    decodeFilePath(GtwConfiguration::instance().webDir().get());
    decodeFilePath(GtwConfiguration::instance().tzPath().get());
}

void GtwSysConfig::LoadGtwConfigCB(GetConfigCallback getConfigCb)
{
    char primaryIpAddr[256] = "127.0.0.1";
    bool isTmwSignedCert = true;

    // Check if config file exists and get primary IP if needed
    if (std::filesystem::exists((std::filesystem::path(getConfigPath()) /
        std::filesystem::path(getConfigFileName()))) == false
        && GetPrimaryIp(primaryIpAddr, 256) == false)
    {
        LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_General, nullptr,
            "%s: Could not get primary adapter ip address, using localhost as primary ip address",
            GtwSysConfig::AppTypeString().c_str());
    }

    nlohmann::json global;
    nlohmann::json workspace;

    bool bGotConfig = false;

    // Try to get config through callback first if provided
    if (getConfigCb) {
        try {
            bGotConfig = getConfigCb(global, workspace);
        }
        catch (const std::exception& e) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr,
                "Error getting config through callback: %s", e.what());
            bGotConfig = false;
        }
    }

    // If callback failed or wasn't provided, try loading from local files
    if (!bGotConfig) {
        std::filesystem::path globalFile = std::filesystem::path(getConfigPath()) /
            std::filesystem::path(getConfigFileName());

        if (std::filesystem::exists(globalFile.string().c_str())) {
            sdg_os_utils::gtw_ifstream configStream(globalFile.string());
            try {
                global = nlohmann::json::parse(configStream);
            }
            catch (std::exception& e) {
                LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
                    "Error parsing global config file %s: %s", globalFile.string().c_str(), e.what());
            }
            configStream.close();
        }

        std::filesystem::path workspaceFile =
          std::filesystem::path(GtwSysConfig::getCurrentWorkSpacePath()) / "ws_config.json";


        if (std::filesystem::exists(workspaceFile.string().c_str())) {
          sdg_os_utils::gtw_ifstream configStream(workspaceFile.string());
          try {
            workspace = nlohmann::json::parse(configStream);
          }
          catch (std::exception& e) {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
              "Error parsing workspace config file %s: %s", workspaceFile.string().c_str(), e.what());
          }
          configStream.close();
        }
    }

    try {
        auto& config = GtwConfiguration::instance();

        // Load all configuration values
        config.loadFromJson(global, workspace);

        config.initializeWebDir();
        config.initializeTzPath();

        // Post-load processing for hosts
        if (config.gtwHost().get() == "127.0.0.1" || config.gtwHost().get() == "localhost") {
            config.gtwHost().set(primaryIpAddr);
        }

        if (config.redHost().get() == "127.0.0.1" || config.redHost().get() == "localhost") {
            config.redHost().set(primaryIpAddr);
        }

        if (config.redPeerHost().get() == "127.0.0.1" || config.redPeerHost().get() == "localhost") {
            config.redPeerHost().set(primaryIpAddr);
        }

        if (!config.monOverride().get()) {
            if (config.monHost().get() == "127.0.0.1" || config.monHost().get() == "localhost") {
                config.monHost().set(primaryIpAddr);
            }

            if (config.monHost().get() != config.gtwHost().get()) {
                config.gtwHost().set(config.monHost().get());
            }
        }
        gtwPasswordComplexity() = jsonTryPasswordComplexity(global, "gtwPasswordComplexity", PASSWORD_COMPLEXITY_TYPE::PASSWORD_COMPLEXITY_LEVEL_0);

        // Check SSL certificate
        std::filesystem::path crtPath = std::filesystem::path(getPublicKeyPath()) /
            config.httpsCertificateFile().get();
        isTmwSignedCert = IsTmwSignedCert(crtPath.string());
        config.httpsCertIsTmwSigned().set(isTmwSignedCert);

#ifdef _WIN32
        config.dataFilePath().set(getenv("ProgramData"));
#else
        config.dataFilePath().set("/etc");
#endif

        config.setConfigLoaded(true);
    }
    catch (std::exception& e) {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr,
            "Error loading configuration: %s", e.what());

        // Initialize with safe defaults
        auto& config = GtwConfiguration::instance();

        config.maxPathLength().set(2048);
        config.maxLogFiles().set(10);
        config.maxBackupFiles().set(10);
        config.doFastShutdown().set(true);
        config.doValidateConfig().set(true);
        config.doValidateIntegrity().set(false);
        config.doWorkspaceMD5Verification().set(false);
        config.doAuth().set(true);
        config.doAudit().set(false);
        config.fullLogOnRestart().set(false);
        config.mirrorAllToLog().set(false);
        config.updateEncryptionComplete().set(false);
        config.useWebSSL().set(true);
        config.useLocalHostForComms().set(false);
        config.enableHttpDeflate().set(true);
        config.httpsCertIsTmwSigned().set(isTmwSignedCert);

        if (config.httpPort().get() == 0) {
            config.httpPort().set(58080);
        }
        if (config.gtwHost().get().empty()) {
            config.gtwHost().set(primaryIpAddr);
        }

        if (config.redPort().get() == 0) {
            config.redPort().set(58070);
        }
        if (config.redHost().get().empty()) {
            config.redHost().set(primaryIpAddr);
        }
        if (config.redPeerHost().get().empty()) {
            config.redPeerHost().set(primaryIpAddr);
        }

        config.redPrimary().set(true);
        config.redEnable().set(false);
        config.redFavorPrimary().set(false);
        config.redEnableSyncConfig().set(true);
        config.redEnableAutoFailover().set(true);
        config.redSlaveTolerancePercentage().set(0.0);
        config.redMasterTolerancePercentage().set(0.0);
        config.redCheckLocalEngineOnlineTimeout().set(60);
        config.redCheckRemoteEngineOnlineTimeout().set(60);
        config.redMonitorReStartRetryLimit().set(0);
        config.redEngineReStartRetryLimit().set(0);

        if (config.monPort().get() == 0) {
            config.monPort().set(58090);
        }
        if (config.monHost().get().empty()) {
            config.monHost().set(primaryIpAddr);
        }

        config.initializeWebDir();
        config.initializeTzPath();

        if (config.wsUpdateRate().get() == 0) {
            config.wsUpdateRate().set(5);
        }
        if (config.licGracePeriodInMinutes().get() > 1440) {
            config.licGracePeriodInMinutes().set(1440);
        }
        if (config.wsUpdateBlockSize().get() == 0) {
            config.wsUpdateBlockSize().set(100);
        }

        config.logTimeZone().set("UTC");
        config.ignoreTimeZoneDST().set(true);
        gtwPasswordComplexity() = PASSWORD_COMPLEXITY_TYPE::PASSWORD_COMPLEXITY_LEVEL_0;

        config.setConfigLoaded(true);
    }
}

std::string GtwSysConfig::AppTypeString() {
    switch (AppType()) {
    case APP_TYPE::APP_MON:
        return "GTWMonitor";
    case APP_TYPE::APP_RED:
        return "GTWRedundancy";
    case APP_TYPE::APP_ENGINE:
        return "GTWEngine";
    default:
        return "Unknown";
    }
}

std::string GtwSysConfig::GetSDGHostAddr() {
    CStdString name;
    name.Format("%s:%d",
        gtwUseLocalHostForEngineAndMonitorComms() ? localHost() : gtwHost(),
        gtwHttpPort());
    return std::move(name);
}

std::string GtwSysConfig::GetMONHostAddr() {
    CStdString name;
    name.Format("%s:%d",
        gtwUseLocalHostForEngineAndMonitorComms() ? localHost() : monHost(),
        monHttpPort());
    return std::move(name);
}

std::string GtwSysConfig::GetREDHostAddr() {
    CStdString name;
    name.Format("%s:%d",
        gtwUseLocalHostForEngineAndMonitorComms() ? localHost() : redHost(),
        redHttpPort());
    return std::move(name);
}


std::string GtwSysConfig::jsonTryGetString(nlohmann::json& pt, std::string field, std::string _default) {
    try {
        std::string result;
        if (pt.find(field) != pt.end()) {
            result = pt[field].get<std::string>();
        }
        else {
            return _default;
        }
        return result;
    }
    catch (...) {
        return _default;
    }
}

bool GtwSysConfig::jsonTryGetBool(nlohmann::json& pt, std::string field, bool _default) {
    try {
        bool result;
        if (pt.find(field) != pt.end()) {
            result = pt[field].get<bool>();
        }
        else {
            return _default;
        }
        return result;
    }
    catch (...) {
        return _default;
    }
}

double GtwSysConfig::jsonTryGetDouble(nlohmann::json& pt, std::string field, double _default) {
    try {
        double result;
        if (pt.find(field) != pt.end()) {
            result = pt[field].get<double>();
        }
        else {
            return _default;
        }
        return result;
    }
    catch (...) {
        return _default;
    }
}

int GtwSysConfig::jsonTryGetInt(nlohmann::json& pt, std::string field, int _default) {
    try {
        int result;
        if (pt.find(field) != pt.end()) {
            result = pt[field].get<int>();
        }
        else {
            return _default;
        }
        return result;
    }
    catch (...) {
        return _default;
    }
}

PASSWORD_COMPLEXITY_TYPE GtwSysConfig::jsonTryPasswordComplexity(nlohmann::json& pt, std::string field, PASSWORD_COMPLEXITY_TYPE _default)
{
  try
  {
    int passwordComplexity;
    if (pt.find(field) != pt.end())
    {
      passwordComplexity = pt[field].get<int>();
      return static_cast<PASSWORD_COMPLEXITY_TYPE>(passwordComplexity);
    }
    else
    {
      return _default;
    }
  }
  catch (...)
  {
    return _default;
  }
}

bool GtwSysConfig::IsTmwSignedCert(const CStdString& cert) {
    bool isTmwCert = false;

    sdg_os_utils::gtw_ifstream t(cert);
    std::stringstream buffer;
    buffer << t.rdbuf();
    t.close();

    BIO* bio_mem = BIO_new(BIO_s_mem());
    BIO_puts(bio_mem, buffer.str().c_str());
    X509* x509 = PEM_read_bio_X509(bio_mem, NULL, NULL, NULL);
    if (x509 == nullptr) {
        BIO_free(bio_mem);
        return false;
    }

    CStdString issuerM;
    char* name_ptr = X509_NAME_oneline(X509_get_issuer_name(x509), NULL, 0);
    if (name_ptr != NULL) {
        issuerM = name_ptr;
        OPENSSL_free(name_ptr);
    }
    else {
        issuerM = "";
    }

    if (issuerM != "") {
        if (issuerM.Find("Triangle MicroWorks") != -1 &&
            issuerM.Find("SDG") != -1 &&
            issuerM.Find("<EMAIL>") != -1) {
            isTmwCert = true;
        }
    }

    BIO_free(bio_mem);
    X509_free(x509);
    return isTmwCert;
}

bool GtwSysConfig::SetMonitorLocation(const CStdString& host, int port) {
    monOverride() = false;
    if (ping(host.c_str(), 4) == 0) {
        return false;
    }

    monOverride() = true;
    monHost() = host;
    monHttpPort() = port;

    return true;
}