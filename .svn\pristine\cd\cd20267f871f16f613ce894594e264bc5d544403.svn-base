<?xml version="1.0"?>
<VisualGDBProjectSettings2 xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ConfigurationName>UB22x64_Debug</ConfigurationName>
  <Project xsi:type="com.visualgdb.project.linux">
    <CustomSourceDirectories>
      <Directories />
      <PathStyle>MinGWUnixSlash</PathStyle>
    </CustomSourceDirectories>
    <AutoProgramSPIFFSPartition>true</AutoProgramSPIFFSPartition>
    <BuildHost>
      <HostName>sdgU22build</HostName>
      <Transport>Alias</Transport>
      <UserName />
    </BuildHost>
    <MainSourceTransferCommand>
      <SkipWhenRunningCommandList>false</SkipWhenRunningCommandList>
      <RemoteHost>
        <HostName>sdgBuild</HostName>
        <Transport>Alias</Transport>
        <UserName />
      </RemoteHost>
      <LocalDirectory>$(ProjectDir)</LocalDirectory>
      <RemoteDirectory>/tmp/gateway/GTWWebMonitor</RemoteDirectory>
      <FileMasks>
        <string>*.cpp</string>
        <string>*.h</string>
        <string>*.hpp</string>
        <string>*.c</string>
        <string>*.cc</string>
        <string>*.cxx</string>
        <string>*.mak</string>
        <string>Makefile</string>
        <string>*.txt</string>
        <string>*.cmake</string>
      </FileMasks>
      <TransferNewFilesOnly>true</TransferNewFilesOnly>
      <IncludeSubdirectories>true</IncludeSubdirectories>
      <SelectedDirectories />
      <DeleteDisappearedFiles>false</DeleteDisappearedFiles>
      <ApplyGlobalExclusionList>true</ApplyGlobalExclusionList>
    </MainSourceTransferCommand>
    <MountInfo>
      <Mode>UserProvidedMount</Mode>
      <RemoteHost>
        <HostName>sdgBuild</HostName>
        <Transport>Alias</Transport>
        <UserName />
      </RemoteHost>
      <LocalDirectory>$(ProjectDir)</LocalDirectory>
      <RemoteDirectory>$(RemoteBuildDir)/gateway/GTWWebMonitor</RemoteDirectory>
      <SuppressPathChangeWarnings>false</SuppressPathChangeWarnings>
    </MountInfo>
    <AllowChangingHostForMainCommands>false</AllowChangingHostForMainCommands>
    <SkipBuildIfNoSourceFilesChanged>false</SkipBuildIfNoSourceFilesChanged>
    <IgnoreFileTransferErrors>false</IgnoreFileTransferErrors>
    <RemoveRemoteDirectoryOnClean>false</RemoveRemoteDirectoryOnClean>
    <SkipDeploymentTests>false</SkipDeploymentTests>
    <MainSourceDirectoryForLocalBuilds>$(ProjectDir)</MainSourceDirectoryForLocalBuilds>
  </Project>
  <Build xsi:type="com.visualgdb.build.msbuild">
    <BuildLogMode xsi:nil="true" />
    <ToolchainID>
      <ID>com.sysprogs.toolchain.default-gcc</ID>
      <Version>
        <Revision>0</Revision>
      </Version>
    </ToolchainID>
    <Toolchain>
      <UnixSystem>true</UnixSystem>
      <RequireCtrlBreak>false</RequireCtrlBreak>
      <ArgumentEscapingMode>Normal</ArgumentEscapingMode>
      <RequiredLinuxPackages>
        <TestableLinuxPackage>
          <UserFriendlyName>GCC</UserFriendlyName>
          <DefaultPackageName>g++</DefaultPackageName>
          <AlternativeRPMPackageName>gcc-c++</AlternativeRPMPackageName>
          <BinaryToCheck>g++</BinaryToCheck>
        </TestableLinuxPackage>
        <TestableLinuxPackage>
          <ID>gdb</ID>
          <UserFriendlyName>GDB</UserFriendlyName>
          <DefaultPackageName>gdb</DefaultPackageName>
          <BinaryToCheck>gdb</BinaryToCheck>
          <AlternativeBinaries>
            <string>ggdb</string>
            <string>gdb-apple</string>
          </AlternativeBinaries>
          <AlternativeDirectories>
            <string>/opt/local/bin</string>
          </AlternativeDirectories>
        </TestableLinuxPackage>
        <TestableLinuxPackage>
          <UserFriendlyName>GNU Make</UserFriendlyName>
          <DefaultPackageName>make</DefaultPackageName>
          <BinaryToCheck>make</BinaryToCheck>
        </TestableLinuxPackage>
      </RequiredLinuxPackages>
      <Name>Default GCC toolchain on sdg-ubuntu-32</Name>
      <UniqueID>com.sysprogs.toolchain.default-gcc</UniqueID>
      <GCC>gcc</GCC>
      <GXX>g++</GXX>
      <GDB>gdb</GDB>
      <AR>ar</AR>
      <OBJCOPY>objcopy</OBJCOPY>
      <Make>make</Make>
      <ShellMode>Direct</ShellMode>
    </Toolchain>
    <ProjectFile>GTWWebMonitor.vcxproj</ProjectFile>
    <RemoteBuildEnvironment>
      <Records />
    </RemoteBuildEnvironment>
    <ParallelJobCount>4</ParallelJobCount>
    <SuppressDirectoryChangeMessages>true</SuppressDirectoryChangeMessages>
    <BuildAsRoot>false</BuildAsRoot>
  </Build>
  <CustomBuild>
    <PreSyncActions />
    <PreBuildActions />
    <PostBuildActions>
      <CustomActionBase xsi:type="CommandLineAction">
        <SkipWhenRunningCommandList>true</SkipWhenRunningCommandList>
        <RemoteHost>
          <HostName>sdgBuild</HostName>
          <Transport>Alias</Transport>
          <UserName />
        </RemoteHost>
        <Command>pwd</Command>
        <Arguments />
        <WorkingDirectory>$(SourceDir)</WorkingDirectory>
        <Environment>
          <Records />
          <EnvironmentSetupFiles />
        </Environment>
        <BackgroundMode>false</BackgroundMode>
      </CustomActionBase>
      <CustomActionBase xsi:type="CommandLineAction">
        <SkipWhenRunningCommandList>true</SkipWhenRunningCommandList>
        <RemoteHost>
          <HostName>sdgBuild</HostName>
          <Transport>Alias</Transport>
          <UserName />
        </RemoteHost>
        <Command>cp</Command>
        <Arguments>/tmp/gateway/VisualGDB/LinDebug/GTWWebMonitor /tmp/bind</Arguments>
        <WorkingDirectory>$(SourceDir)</WorkingDirectory>
        <Environment>
          <Records />
          <EnvironmentSetupFiles />
        </Environment>
        <BackgroundMode>false</BackgroundMode>
      </CustomActionBase>
    </PostBuildActions>
    <PreCleanActions />
    <PostCleanActions />
  </CustomBuild>
  <CustomDebug>
    <PreDebugActions />
    <PostDebugActions />
    <DebugStopActions />
    <BreakMode>Default</BreakMode>
  </CustomDebug>
  <CustomShortcuts>
    <Shortcuts />
    <ShowMessageAfterExecuting>true</ShowMessageAfterExecuting>
  </CustomShortcuts>
  <UserDefinedVariables />
  <ImportedPropertySheets />
  <CodeSense>
    <Enabled>Unknown</Enabled>
    <ExtraSettings>
      <HideErrorsInSystemHeaders>true</HideErrorsInSystemHeaders>
      <SupportLightweightReferenceAnalysis>false</SupportLightweightReferenceAnalysis>
      <CheckForClangFormatFiles xsi:nil="true" />
      <FormattingEngine xsi:nil="true" />
    </ExtraSettings>
    <CodeAnalyzerSettings>
      <Enabled>false</Enabled>
    </CodeAnalyzerSettings>
  </CodeSense>
  <Configurations />
  <ProgramArgumentsSuggestions />
  <Debug xsi:type="com.visualgdb.debug.remote">
    <AdditionalStartupCommands />
    <AdditionalGDBSettings>
      <Features>
        <DisableAutoDetection>false</DisableAutoDetection>
        <UseFrameParameter>true</UseFrameParameter>
        <SimpleValuesFlagSupported>true</SimpleValuesFlagSupported>
        <ListLocalsSupported>true</ListLocalsSupported>
        <ByteLevelMemoryCommandsAvailable>true</ByteLevelMemoryCommandsAvailable>
        <ThreadInfoSupported>true</ThreadInfoSupported>
        <PendingBreakpointsSupported>true</PendingBreakpointsSupported>
        <SupportTargetCommand>true</SupportTargetCommand>
        <ReliableBreakpointNotifications>true</ReliableBreakpointNotifications>
      </Features>
      <EnableSmartStepping>false</EnableSmartStepping>
      <FilterSpuriousStoppedNotifications>false</FilterSpuriousStoppedNotifications>
      <ForceSingleThreadedMode>false</ForceSingleThreadedMode>
      <UseAppleExtensions>false</UseAppleExtensions>
      <CanAcceptCommandsWhileRunning>false</CanAcceptCommandsWhileRunning>
      <MakeLogFile>false</MakeLogFile>
      <IgnoreModuleEventsWhileStepping>true</IgnoreModuleEventsWhileStepping>
      <UseRelativePathsOnly>false</UseRelativePathsOnly>
      <ExitAction>None</ExitAction>
      <DisableDisassembly>false</DisableDisassembly>
      <ExamineMemoryWithXCommand>false</ExamineMemoryWithXCommand>
      <StepIntoNewInstanceEntry>main</StepIntoNewInstanceEntry>
      <ExamineRegistersInRawFormat>true</ExamineRegistersInRawFormat>
      <DisableSignals>false</DisableSignals>
      <EnableAsyncExecutionMode>false</EnableAsyncExecutionMode>
      <AsyncModeSupportsBreakpoints>true</AsyncModeSupportsBreakpoints>
      <TemporaryBreakConsolidationTimeout>0</TemporaryBreakConsolidationTimeout>
      <BacktraceFrameLimit>0</BacktraceFrameLimit>
      <EnableNonStopMode>false</EnableNonStopMode>
      <MaxBreakpointLimit>0</MaxBreakpointLimit>
      <EnableVerboseMode>true</EnableVerboseMode>
      <EnablePrettyPrinters>false</EnablePrettyPrinters>
      <EnableAbsolutePathReporting>true</EnableAbsolutePathReporting>
    </AdditionalGDBSettings>
    <LaunchGDBSettings xsi:type="GDBLaunchParametersNewInstance">
      <DebuggedProgram>$(TargetPath)</DebuggedProgram>
      <GDBServerPort>2000</GDBServerPort>
      <ProgramArguments />
      <ArgumentEscapingMode>Auto</ArgumentEscapingMode>
    </LaunchGDBSettings>
    <GenerateCtrlBreakInsteadOfCtrlC>false</GenerateCtrlBreakInsteadOfCtrlC>
    <SuppressArgumentVariablesCheck>false</SuppressArgumentVariablesCheck>
    <DeploymentTargetPath />
    <X11WindowMode>Local</X11WindowMode>
    <KeepConsoleAfterExit>false</KeepConsoleAfterExit>
    <RunGDBUnderSudo>true</RunGDBUnderSudo>
    <DeploymentMode>Auto</DeploymentMode>
    <LdLibraryPath>/var/hasplm</LdLibraryPath>
    <DeployWhenLaunchedWithoutDebugging>true</DeployWhenLaunchedWithoutDebugging>
    <StripDebugSymbolsDuringDeployment>false</StripDebugSymbolsDuringDeployment>
    <SuppressTTYCreation>false</SuppressTTYCreation>
    <IndexDebugSymbols>false</IndexDebugSymbols>
    <RunLiveMemoryAgentAsRoot>true</RunLiveMemoryAgentAsRoot>
  </Debug>
</VisualGDBProjectSettings2>