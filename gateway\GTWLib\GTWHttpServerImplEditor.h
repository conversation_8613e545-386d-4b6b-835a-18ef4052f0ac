#pragma once
#include "../GTWWebLib/HttpServerCommon.h"
#include "../GTWWebLib/HttpServerBase.h"
#include "gateway/GTWLib/TMWParam.h"
#include <memory>
#include "gateway/GTWOsUtils/filesystem.hpp"

#include "GTWHttpServerImplCommon.h"
#include "GTWInternalUserMDOEditor.h"
#include "GTWmdoEquationEditor.h"
#include "GTWODBCClientEditor.h"
#include "GTWODBCQueryEditor.h"
#include "GTWModemEditor.h"
#include "GTWModemPoolEditor.h"
#include "GTWChannelEditor.h"
#include "GTWSessionEditor.h"
#include "GTWSectorEditor.h"
#include "GTWdataTypeEditor.h"
#include "GTWmdoEditor.h"
#include "GTWSdoMappingEditor.h"
#include "GTWSdosMappingEditor.h"
#include "GTWMdoMappingEditor.h"
#include "GTWMdosMappingEditor.h"
#include "GTWModbusSessionActionEditor.h"
#include "GTW61850SlaveDataObject.h"
#include "GTWS14MonitorSecurityStatisticsSdo.h"
#include "GTWDnpSecurityStatisticsSdo.h"
#include "GTWDNPDsPrototype.h"
#include "GTWDNPDsDescriptor.h"
#include "GTWDNPDsData.h"
#include "GTWDNPDsElement.h"
#include "GTWOpcUaMasterDataObject.h"
#include "GTWOpcMasterDataObject.h"
#include "GTWTASE2DataAttributeMDO.h"
#include "GTWOPCClientEditor.h"
#include "GTWOPCClientItemEditor.h"
#include "GTWOPCClientNode.h"
#include "GTWOPCAEClientEditor.h"
#include "GTWOPCAEClientItemEditor.h"
#include "GTWOPCAEClientAttrEditor.h"
#include "GTWOpcAEMasterDataObject.h"
#include "GTWOPCUaClientEditor.h"
#include "GTWOPCUACertificateEditor.h"
#include "GTWOPCUAUserSecurityEditor.h"
#include "GTWOPCUAServerEditor.h"
#include "GTWOPCUaClientItemEditor.h"
#include "GTW61850ChangeDataSetEditor.h"
#include "GTWOPC61850ServerControlEditor.h"
#include "GTWOPC61850ServerControlItemEditor.h"
#include "GTW61850ServerEditor.h"
#include "GTW61850ClientEditor.h"
#include "GTW61850ReportEditor.h"
#include "GTW61850GOOSEEditor.h"
#include "GTW61850PolledDataSetEditor.h"
#include "GTW61850DataAttributeMDO.h"
#include "GTW61850DataAttributeMDOEditor.h"
#include "GTW61850DatasetEditor.h"
#include "GTW61850CommandPointEditor.h"
#include "GTW61850CommandPointSetEditor.h"
#include "GTWTASE2CommandPointSetEditor.h"
#include "GTW61850WritablePointSetEditor.h"
#include "GTW61850WriteablePointEditor.h"
#include "GTWMMBMultiPointEditor.h"
#include "GTW61850PolledPointSetEditor.h"
#include "GTW61850SdoEditor.h"
#include "GTWGooseMonitorEditor.h"
#include "GTWGooseSubscriptionEditor.h"
#include "GTW62351SecurityEditor.h"
#include "GTW61400AlarmsEditor.h"
#include "GTW61400AlarmMDOEditor.h"
#include "GTWTASE2DataAttributeMDOEditor.h"
#include "GTWTASE2ClientEditor.h"
#include "GTWTase2ServerEditor.h"
#include "GTWTASE2CltReportedDataSetEditor.h"
#include "GTWTASE2PolledPointSetEditor.h"
#include "GTWTASE2PolledDataSetEditor.h"
#include "GTWTASE2PolledDataSet.h"
#include "GTWTASE2CommandPointEditor.h"
#include "GTWTase2ServerLogicalDeviceEditor.h"
#include "GTWTASE2DatasetEditor.h"
#include "GTWTASE2DomainEditor.h"
#include "GTWTASE2DataAttributeEditor.h"
#include "GTWTase2ClientDomain.h"
#include "GTWTASE2SdoEditor.h"
#include "GTWTASE2MAppingDataAttributeEditor.h"
#include "GTWBaseEditor.h"
#include "GTWRootEditor.h"
#include "GTWconfigres.h"
#include "GTWTase2Server.h"
#include "TMW61850/Common/tmwODBC/DatabaseConnection.h"
#include "GTWUserDefinedFolderEditor.h"
#include "GTWOpcUaServer.h"

template<typename SOCK_TYP>
using CreateOrUpdateMethodTyp = void(*)(EditorCommandDTO &dto,
  HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request);

template<typename SOCK_TYP>
using GetDataMethodTyp = void(*)(EditorCommandDTO &dto,
  HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request);

template<typename SOCK_TYP>
using DoActionMethodTyp = void(*)(EditorCommandDTO &dto,
  HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request);

template<typename SOCK_TYP>
class EditorCommandDispatchItem
{
public:
  EditorCommandDispatchItem(CreateOrUpdateMethodTyp<SOCK_TYP> createOrUpdateMethod, GetDataMethodTyp<SOCK_TYP> getDataMethod)
  {
    pCreateOrUpdateMethod = createOrUpdateMethod;
    pGetDataMethod = getDataMethod;
  }
  CreateOrUpdateMethodTyp<SOCK_TYP> pCreateOrUpdateMethod;
  GetDataMethodTyp<SOCK_TYP> pGetDataMethod;

};

template<typename SOCK_TYP>
class EditorActionDispatchItem
{
public:
  EditorActionDispatchItem(DoActionMethodTyp<SOCK_TYP> actionMethod)
  {
    pActionMethod = actionMethod;
  }
  DoActionMethodTyp<SOCK_TYP> pActionMethod;

};


template <typename SOCK_TYP>
class GTWLIB_API GTWHttpServerImplEditor
{
public:
  GTWHttpServerImplEditor()
  {
    //
    //----------------- COMMANDS ------------------
    //
    EditorCommandDispatchMap[MENU_CMD_NONE] = new EditorCommandDispatchItem<SOCK_TYP>(NoOp, NoOp);
    EditorCommandDispatchMap[MENU_CMD_ADD_INTERNAL_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddInternalMdoCreateOrUpdate, AddInternalMdoGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_USER_DEFINED_FOLDER] = new EditorCommandDispatchItem<SOCK_TYP>(AddUserFolderCreateOrUpdate, AddUserFolderGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddMdoCreateOrUpdate, AddMdoGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MULTIPLE_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddMultipleMdoCreateOrUpdate, AddMultipleMdoGetData);
    EditorCommandDispatchMap[MENU_CMD_EDIT] = new EditorCommandDispatchItem<SOCK_TYP>(EditCreateOrUpdate, EditGetData);
    EditorCommandDispatchMap[MENU_CMD_EDIT_WORKSPACE_PARAMETERS] = new EditorCommandDispatchItem<SOCK_TYP>(EditCreateOrUpdate, EditGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MAPPING_SDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddMappingSdoCreateOrUpdate, AddMappingSdoGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MAPPING_SDOS] = new EditorCommandDispatchItem<SOCK_TYP>(AddMappingSdosCreateOrUpdate, AddMappingSdosGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MAPPING_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddMappingMdoCreateOrUpdate, AddMappingMdoGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MAPPING_MDOS] = new EditorCommandDispatchItem<SOCK_TYP>(AddMappingMdosCreateOrUpdate, AddMappingMdosGetData);
    //EditorCommandDispatchMap[MENU_CMD_DROP_ON_FOLDER] = new EditorCommandDispatchItem<SOCK_TYP>(NoOp, ActionDropOnFolder);
    EditorCommandDispatchMap[MENU_CMD_ADD_EQ_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(AddEquationMdoCreateOrUpdate, AddEquationMdoGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_ODBC_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(AddOdbcClientCreateOrUpdate, AddOdbcClientGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_ODBC_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(AddOdbcItemCreateOrUpdate, AddOdbcItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TCP_CHANNEL_MASTER] = new EditorCommandDispatchItem<SOCK_TYP>(AddMasterChannelCreateOrUpdate, AddTcpMasterChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE] = new EditorCommandDispatchItem<SOCK_TYP>(AddSlaveChannelCreateOrUpdate, AddTcpSlaveChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL] = new EditorCommandDispatchItem<SOCK_TYP>(AddSlaveRedundantChannelCreateOrUpdate, AddTcpSlaveChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL] = new EditorCommandDispatchItem<SOCK_TYP>(AddMasterRedundantChannelCreateOrUpdate, AddTcpMasterChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_SERIAL_CHANNEL_MASTER] = new EditorCommandDispatchItem<SOCK_TYP>(AddMasterChannelCreateOrUpdate, AddSerialMasterChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE] = new EditorCommandDispatchItem<SOCK_TYP>(AddSlaveChannelCreateOrUpdate, AddSerialSlaveChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_SESSION] = new EditorCommandDispatchItem<SOCK_TYP>(AddSessionCreateOrUpdate, AddSessionGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_SECTOR] = new EditorCommandDispatchItem<SOCK_TYP>(AddSectorCreateOrUpdate, AddSectorGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_DATA_TYPE] = new EditorCommandDispatchItem<SOCK_TYP>(AddDataTypeCreateOrUpdate, AddDataTypeGetData);

    // Add channel session wizard cancellation handler
    EditorCommandDispatchMap[MENU_CMD_CHANNEL_SESSION_WIZARD_CANCELLED] = new EditorCommandDispatchItem<SOCK_TYP>(ChannelSessionWizardCancelledHandler, NoOp);

    EditorCommandDispatchMap[MENU_CMD_ADD_MULTI_POINT] = new EditorCommandDispatchItem<SOCK_TYP>(AddMultiPointCreateOrUpdate, AddMultiPointGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER] = new EditorCommandDispatchItem<SOCK_TYP>(AddMasterChannelCreateOrUpdate, AddDNP3_UDP_TCPMasterChannelGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE] = new EditorCommandDispatchItem<SOCK_TYP>(AddSlaveChannelCreateOrUpdate, AddDNP3_UDP_TCPSlaveChannelGetData);
#if USE_OPC_44
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcClientCreateOrUpdate, AddOpcClientGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcItemCreateOrUpdate, AddOpcItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MULTIPLE_OPC_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcItemCreateOrUpdate, AddOpcMultipleItemsGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_AE_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcAeClientCreateOrUpdate, AddOpcAeClientGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_AE_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcAeItemCreateOrUpdate, AddOpcAeItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_AE_ATTR] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcAeAttrCreateOrUpdate, AddOpcAeAttrGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ControlOpcMappingCreateOrUpdate, Add61850ControlOpcMappingGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ControlOpcMappingItemCreateOrUpdate, Add61850ControlOpcMappingItemGetData);
#endif // USE_OPC_44
#if USE_OPC_UA
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_UA_CERTIFICATE] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcUaCertificateCreateOrUpdate, AddOpcUaCertificateGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_UA_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(AddOpcUaClientCreateOrUpdate, AddOpcUaClientGetData);
    EditorCommandDispatchMap[MENU_CMD_EDIT_OPC_UA_SERVER] = new EditorCommandDispatchItem<SOCK_TYP>(EditOpcUaServerCreateOrUpdate, EditOpcUaServerGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_OPC_UA_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(CloseOpcUaItem, AddOpcUaItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(CloseOpcUaItem, AddOpcUaMultipleItemsGetData);
#endif
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_SERVER] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ServerCreateOrUpdate, Add61850ServerGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ClientCreateOrUpdate, Add61850ClientGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_CLIENTS_FROM_FILE] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ClientsFromFileCreate, Add61850ClientsFromFileGetData);
    
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_GOOSE] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850GooseCreateOrUpdate, Add61850GooseGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_REPORT] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ReportCreateOrUpdate, Add61850ReportGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_POLLED_DATA_SET] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850PolledDataSetCreateOrUpdate, Add61850PolledDataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_POLLED_POINT_SET] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850PolledPointSetCreateOrUpdate, Add61850PolledPointSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850ItemCreateOrUpdate, Add61850ItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_WRITABLE_POINT] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850WritablePointCreateOrUpdate, Add61850WritablePointGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_WRITABLE_POINT_SET] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850WritablePointSetCreateOrUpdate, Add61850WritablePointSetGetData);
    EditorCommandDispatchMap[MENU_CMD_SELECT_DATA_ATTRIBUTE] = new EditorCommandDispatchItem<SOCK_TYP>(SelectDataAttributeCreateOrUpdate, SelectDataAttributeGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_DATASET] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850DataSetCreateOrUpdate, Add61850DataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_CHANGE_61850_DATASET] = new EditorCommandDispatchItem<SOCK_TYP>(Change61850DataSet, Change61850DataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_COMMAND_POINT] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850CommandPointCreateOrUpdate, Add61850CommandPointGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61850_COMMAND_POINT_SET] = new EditorCommandDispatchItem<SOCK_TYP>(Add61850CommandPointSetCreateOrUpdate, Add61850CommandPointSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61400_ALARMS_NODE] = new EditorCommandDispatchItem<SOCK_TYP>(Add61400AlarmsNodeCreateOrUpdate, Add61400AlarmsNodeGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_61400_ALARM_MDO] = new EditorCommandDispatchItem<SOCK_TYP>(Add61400AlarmMDOCreateOrUpdate, Add61400AlarmMDOGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2CreateOrUpdate, AddTase2GetDataClient);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_SERVER] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2CreateOrUpdate, AddTase2GetDataServer);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_CLIENT_SERVER] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2CreateOrUpdate, AddTase2GetDataClientServer);
    EditorCommandDispatchMap[MENU_CMD_EDIT_TASE2_EDIT_MODEL] = new EditorCommandDispatchItem<SOCK_TYP>(EditTase2EditModel, EditTase2EditModelGetData);
    EditorCommandDispatchMap[MENU_CMD_EDIT_TASE2_DATA_POINTS] = new EditorCommandDispatchItem<SOCK_TYP>(EditTase2DataPointsCreateOrUpdate, EditTase2DataPointsCreateOrUpdate);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_LOGICAL_DEVICE] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2LogicalDeviceCreateOrUpdate, AddTase2LogicalDeviceGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_POLLED_DATA_SET] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2PolledDataSetCreateOrUpdate, AddTase2PolledDataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_POLLED_POINT_SET] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2PolledPointSetCreateOrUpdate, AddTase2PolledPointSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_DSTS] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2DstsCreateOrUpdate, AddTase2DstsGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_DATASET] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2DataSetCreateOrUpdate, AddTase2DataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_MANAGE_TASE2_DATASET] = new EditorCommandDispatchItem<SOCK_TYP>(ManageTase2DataSetCreateOrUpdate, ManageTase2DataSetGetData);
    EditorCommandDispatchMap[MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT] = new EditorCommandDispatchItem<SOCK_TYP>(ManageTase2DataSetCreateOrUpdate, ManageTase2DataSetGetDataFullEdit);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_COMMAND_POINT] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2CommandPointCreateOrUpdate, AddTase2CommandPointGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_COMMAND_POINT_SET] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2CommandPointSetCreateOrUpdate, AddTase2CommandPointSetGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_ITEM] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2ItemCreateOrUpdate, AddTase2ItemGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_DOMAIN] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2DomainCreateOrUpdate, AddTase2DomainGetData);
    EditorCommandDispatchMap[MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE] = new EditorCommandDispatchItem<SOCK_TYP>(AddTase2DataAttributeCreateOrUpdate, AddTase2DataAttributeGetData);
    EditorCommandDispatchMap[MENU_CMD_SHOW_CONFIG_TASE2_SERVER] = new EditorCommandDispatchItem<SOCK_TYP>(ShowTase2ServerConfig, ShowTase2ServerConfig);
    EditorCommandDispatchMap[MENU_CMD_SHOW_CONFIG_TASE2_CLIENT] = new EditorCommandDispatchItem<SOCK_TYP>(ShowTase2ClientConfig, ShowTase2ClientConfig);
#if USE_GOOSE_MONITOR
    EditorCommandDispatchMap[MENU_CMD_ADD_GOOSE_MONITOR] = new EditorCommandDispatchItem<SOCK_TYP>(AddGooseMonitorCreateOrUpdate, AddGooseMonitorGetData);
#endif
    EditorCommandDispatchMap[MENU_CMD_ADD_WRITE_ACTION] = new EditorCommandDispatchItem<SOCK_TYP>(AddWriteActionCreateOrUpdate, AddWriteActionGetData);

    //
    //----------------- ACTIONS ------------------
    //
    EditorActionDispatchMap[MENU_ACTION_NONE] = new EditorActionDispatchItem<SOCK_TYP>(NoOp);

    EditorActionDispatchMap[GetMissingLocalINIParam] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetMissingLocalINIParam);
    EditorActionDispatchMap[GetCertificateInfo] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetCertificateInfo);

    EditorActionDispatchMap[GetAvailableIEDFromFile] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetAvailableIEDFromFile);
    EditorActionDispatchMap[GetServerFromSCLFileName] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetServerFromSCLFileName);
    EditorActionDispatchMap[LoadConfigFromServer] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadConfigFromServer);
    EditorActionDispatchMap[TestODBCConnectionString] = new EditorActionDispatchItem<SOCK_TYP>(ActionTestODBCConnectionString);
    EditorActionDispatchMap[GetDatasetMember] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetDatasetMember);
    EditorActionDispatchMap[ChangeDataSetName] = new EditorActionDispatchItem<SOCK_TYP>(ActionChangeDataSetName);
    EditorActionDispatchMap[GetDataSet] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetDataSet);
    EditorActionDispatchMap[ReadConfigFromServer] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadConfigFromServer);
    EditorActionDispatchMap[DeleteDS] = new EditorActionDispatchItem<SOCK_TYP>(ActionDeleteDS);
    EditorActionDispatchMap[AddTASE2DSTS] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddTASE2DSTS);
    EditorActionDispatchMap[AddTASE2DSPD] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddTASE2DSPD);
    EditorActionDispatchMap[AddTASE2CommandPoint] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddTASE2CommandPoint);
    EditorActionDispatchMap[ChangeFCDS] = new EditorActionDispatchItem<SOCK_TYP>(ActionChangeFCDS);
    EditorActionDispatchMap[ChangeDAPointList] = new EditorActionDispatchItem<SOCK_TYP>(ActionChangeDAPointList);
    EditorActionDispatchMap[ValidateEquation] = new EditorActionDispatchItem<SOCK_TYP>(ActionValidateEquation);
    EditorActionDispatchMap[ChangeSelectedDomain] = new EditorActionDispatchItem<SOCK_TYP>(ActionChangeSelectedDomain);
    EditorActionDispatchMap[LoadEditorFiles] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadEditorFiles);

    EditorActionDispatchMap[LoadModel] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadModel);
    EditorActionDispatchMap[ExportModel] = new EditorActionDispatchItem<SOCK_TYP>(ActionExportModel);
    EditorActionDispatchMap[ClearModel] = new EditorActionDispatchItem<SOCK_TYP>(ActionClearModel);
    EditorActionDispatchMap[ChangeTable] = new EditorActionDispatchItem<SOCK_TYP>(ActionChangeTable);
    EditorActionDispatchMap[ExecuteSql] = new EditorActionDispatchItem<SOCK_TYP>(ActionExecuteSql);
    EditorActionDispatchMap[Read61850Tag] = new EditorActionDispatchItem<SOCK_TYP>(ActionRead61850Tag);
    EditorActionDispatchMap[ReadICCPTag] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadICCPTag);
    EditorActionDispatchMap[OperateTase2Control] = new EditorActionDispatchItem<SOCK_TYP>(ActionOperateTase2Control);
    EditorActionDispatchMap[SaveTase2ServerToFile] = new EditorActionDispatchItem<SOCK_TYP>(ActionSaveTase2ServerToFile);
    EditorActionDispatchMap[DeleteTase2Domain] = new EditorActionDispatchItem<SOCK_TYP>(ActionDeleteTase2Domain);
    EditorActionDispatchMap[ShowConfigInfoTase2] = new EditorActionDispatchItem<SOCK_TYP>(ActionShowConfigInfoTase2);
    EditorActionDispatchMap[DeleteTase2DataAttribute] = new EditorActionDispatchItem<SOCK_TYP>(ActionDeleteTase2DataAttribute);
    EditorActionDispatchMap[ChangeDataTypeTase2] = new EditorActionDispatchItem<SOCK_TYP>(ActionOnChangeDataTypeTase2);
    EditorActionDispatchMap[EnableDisableDSTS] = new EditorActionDispatchItem<SOCK_TYP>(ActionEnableDisableDSTS);
    EditorActionDispatchMap[DisconnectConnectFrom61850Server] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectConnectFrom61850Server);
    EditorActionDispatchMap[Restart61850Server] = new EditorActionDispatchItem<SOCK_TYP>(ActionRestart61850Server);
    EditorActionDispatchMap[Stop61850Server] = new EditorActionDispatchItem<SOCK_TYP>(ActionStop61850Server);
    EditorActionDispatchMap[PerformWriteAction] = new EditorActionDispatchItem<SOCK_TYP>(ActionPerformWriteAction);
    EditorActionDispatchMap[Save61850ModelToFile] = new EditorActionDispatchItem<SOCK_TYP>(ActionSave61850ModelToFile);
    EditorActionDispatchMap[EnableDisableRCB] = new EditorActionDispatchItem<SOCK_TYP>(ActionEnableDisableRCB);
    EditorActionDispatchMap[VerifyDataset] = new EditorActionDispatchItem<SOCK_TYP>(ActionVerifyDataset);
    EditorActionDispatchMap[ReadDataset] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadDataset);
    EditorActionDispatchMap[Reset61850RetryConnectCount] = new EditorActionDispatchItem<SOCK_TYP>(ActionReset61850RetryConnectCount);
    EditorActionDispatchMap[ResetAverageMdoUpdateRate] = new EditorActionDispatchItem<SOCK_TYP>(ActionResetAverageMdoUpdateRate);
    EditorActionDispatchMap[DisconnectConnectFromTase2Server] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectConnectFromTase2Server);
    EditorActionDispatchMap[DisconnectRestartTase2Server] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectRestartTase2Server);
    EditorActionDispatchMap[AutoCreateTags] = new EditorActionDispatchItem<SOCK_TYP>(ActionAutoCreateTags);
    EditorActionDispatchMap[DropOnFolder] = new EditorActionDispatchItem<SOCK_TYP>(ActionDropOnFolder);
    EditorActionDispatchMap[CreateTHXMLPointFile] = new EditorActionDispatchItem<SOCK_TYP>(ActionCreateTHXMLPointFile);
    EditorActionDispatchMap[CreateDTMCSVPointFile] = new EditorActionDispatchItem<SOCK_TYP>(ActionCreateDTMCSVPointFile);
#if USE_GOOSE_MONITOR
    EditorActionDispatchMap[GetGOOSEMonitorStreams] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetGOOSEMonitorStreams);
    EditorActionDispatchMap[SubscribeUnsubscribeGooseStream] = new EditorActionDispatchItem<SOCK_TYP>(ActionSubscribeUnsubscribeGooseStream);
#endif
    EditorActionDispatchMap[SwitchToRChannel] = new EditorActionDispatchItem<SOCK_TYP>(ActionSwitchToRChannel);
    EditorActionDispatchMap[Add61850MultipleDAItems] = new EditorActionDispatchItem<SOCK_TYP>(ActionAdd61850MultipleDAItems);
    EditorActionDispatchMap[Add61850MultipleWPItems] = new EditorActionDispatchItem<SOCK_TYP>(ActionAdd61850MultipleWPItems);
    EditorActionDispatchMap[Add61850CommandPointItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionAdd61850CommandPointItem);
    EditorActionDispatchMap[AddTase2MultiplePPSItems] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddTase2MultiplePPSItems);
    EditorActionDispatchMap[RefreshEquationArgument] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshEquationArgument);
#if USE_OPC_44
    EditorActionDispatchMap[ValidateOPCItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionValidateOPCItem);
    EditorActionDispatchMap[AddOPCItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddOPCItem);
    EditorActionDispatchMap[AddOPCMultipleItems] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddOPCMultipleItems);
    EditorActionDispatchMap[RefreshOPCServerList] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCServerList);
    EditorActionDispatchMap[RefreshOPCAEServerList] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCServerList);
    EditorActionDispatchMap[ActivateOPCItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionActivateOPCItem);
    EditorActionDispatchMap[ReadOPCItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadOPCItem);
    EditorActionDispatchMap[LoadChildrenOPCClientItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadChildrenOPCClientItem);
    EditorActionDispatchMap[LoadChildrenOPCAEAreaApace] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadChildrenOPCAEAreaApace);
    EditorActionDispatchMap[RefreshOPCItemParent] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCItemParent);
    EditorActionDispatchMap[RefreshOPCAEAreaApace] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCAEAreaApace);
    EditorActionDispatchMap[RefreshOPCDAProperties] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCDAProperties);
    EditorActionDispatchMap[DisconnectConnectFromOPCServer] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectConnectFromOPCServer);
    EditorActionDispatchMap[DisconnectConnectFromOPCAEServer] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectConnectFromOPCAEServer);
    EditorActionDispatchMap[ReadOPCDAMDO] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadOPCDAMDO);
#endif
#if USE_OPC_UA
    EditorActionDispatchMap[RefreshOPCUAServerList] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCUAServerList);
    EditorActionDispatchMap[LoadChildrenOPCUAClientItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionLoadChildrenOPCUAClientItem);
    EditorActionDispatchMap[ValidateOPCUAItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionValidateOPCUAItem);
    EditorActionDispatchMap[RefreshOPCUAItemParent] = new EditorActionDispatchItem<SOCK_TYP>(ActionRefreshOPCUAItemParent);
    EditorActionDispatchMap[AddOPCUAItem] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddOPCUAItem);
    EditorActionDispatchMap[AddOPCUAMultipleItems] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddOPCUAMultipleItems);
    EditorActionDispatchMap[DisconnectConnectFromOPCUAServer] = new EditorActionDispatchItem<SOCK_TYP>(ActionDisconnectConnectFromOPCUAServer);
    EditorActionDispatchMap[AddDeleteOPCUaUser] = new EditorActionDispatchItem<SOCK_TYP>(ActionAddDeleteOPCUaUser);
    EditorActionDispatchMap[GetOPCUaServerStatus] = new EditorActionDispatchItem<SOCK_TYP>(ActionGetOPCUaServerStatus);
    EditorActionDispatchMap[TrustOPCUaCertificate] = new EditorActionDispatchItem<SOCK_TYP>(ActionTrustOPCUaCertificate);
    EditorActionDispatchMap[TrustOPCUaClientCertificate] = new EditorActionDispatchItem<SOCK_TYP>(ActionTrustOPCUaClientCertificate);
    EditorActionDispatchMap[ReadOPCUAMDO] = new EditorActionDispatchItem<SOCK_TYP>(ActionReadOPCUAMDO);
#endif
  }

  ~GTWHttpServerImplEditor()
  {
    for (auto iter = EditorCommandDispatchMap.begin(); iter != EditorCommandDispatchMap.end(); iter++)
      delete iter->second;

    EditorCommandDispatchMap.clear();

    for (auto iter = EditorActionDispatchMap.begin(); iter != EditorActionDispatchMap.end(); iter++)
      delete iter->second;

    EditorActionDispatchMap.clear();
  }

  void DoGetDefineLocalParameter(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    TEST_AUTH_IMPL(response, request);
    try {
      nlohmann::json pt = nlohmann::json::parse(request->content);
      std::string objectDataJson = pt["objectDataJson"].get<std::string>();
      nlohmann::json j = nlohmann::json::parse(objectDataJson);

      // Or if you want key-value pairs

      for (const auto& [key, value] : j.items()) {
        std::string localParamsPath =
          GtwSysConfig::getWorkSpacesLocalOverridesPath() + "/" +
          GtwSysConfig::sCurrentWorkSpaceName() + "/" +
          GtwSysConfig::sCurrentWorkSpaceName() + ".json";

        TMWParam* pParam = GTWConfig::GetParam(key);
        if (pParam != nullptr)
          pParam->SetPurposeMask(PARAM_PURPOSE_MASK_IS_LOCALLY_DEFINED, value.template get<std::string>() == "true" ? true : false , localParamsPath);
      }
      nlohmann::json res;
      res["result"] = true;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    catch (std::exception const& e) {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
      return;
    }
  }

  void DoGetEditorContextMenu(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    TEST_AUTH_IMPL(response, request);

    try {

      //Retrieve string:
      std::string query = request->query_string;

      // store query key/value pairs in a map
      std::map<std::string, std::string> params;

      pServer->ExtractParams(query, params);

      CStdString objectFullName;
      objectFullName = params["objectFullName"];

      CStdString objectClassName;
      objectClassName = params["objectClassName"];

      CStdString objectCollectionKind;
      objectCollectionKind = params["objectCollectionKind"];

      GTWCollectionListParent *root = nullptr;
      if (objectFullName.length() > 0)
      {
        GTWCollectionMember *pMember = nullptr;
        bool bOk = GetGTWApp()->deepFindMember(objectFullName, objectCollectionKind, &pMember);
        if (bOk == false && pMember == nullptr)
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + objectFullName);
          return;
        }

        if (pMember != NULL && pMember->GetMemberCollection() != NULL)
        { // this does a device (i.e. chan sess, sctr etc)
          root = static_cast<GTWCollectionListParent *>(pMember);
          std::string json = BuildEditorContextMenuResponse(root, objectClassName, objectCollectionKind, pServer, response, request);
          if (json == "")
          {
            pServer->BuildOkResponse(response, "application/json", "{}");
            return;
          }
          pServer->BuildOkResponse(response, "application/json", json);
          return;
        }
        if (pMember != NULL)
        { // this does a tag (i.e. mdo, sdo, equation etc.)
          std::string json = BuildEditorContextMenuResponse(pMember, objectClassName, objectCollectionKind, pServer, response, request);
          if (json == "")
          {
            pServer->BuildOkResponse(response, "application/json", "{}");
            return;
          }
          pServer->BuildOkResponse(response, "application/json", json);
          return;
        }
      }
      else if (objectFullName == "")
      { // this does the root editor
        std::string json = BuildEditorContextMenuResponse(nullptr, objectClassName, objectCollectionKind, pServer, response, request);
        if (json == "")
        {
          pServer->BuildOkResponse(response, "application/json", "{}");
          return;
        }
        pServer->BuildOkResponse(response, "application/json", json);
        return;
      }
    }
    catch (std::exception const& e) {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
      return;
    }
  }

  void DoGetEditorData(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    try
    {
	    TEST_AUTH_IMPL(response, request);
	
	    std::string command_string = request->path_match[1];
	    MENUENTRY_EDIT_CMD command = MENUENTRY_EDIT_CMD_to_enum(command_string.c_str());
	
	
	    //Retrieve string:
	    std::string query = request->query_string;
	
	    // store query key/value pairs in a map
	    std::map<std::string, std::string> params;
	
	    pServer->ExtractParams(query, params);
	
	    EditorCommandDTO dto(command);
	    dto.token = pServer->GetToken(request);
	    dto.objectName = params["objectName"];
	    dto.objectClassName = params["objectClassName"];
	    dto.parentObjectName = params["parentObjectName"];
	    dto.objectCollectionKind = params["objectCollectionKind"];
	
	    if (DispatchGetDataCommand(dto, pServer, response, request) == true)
	    {
	      return;
	    }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    }
    catch (const std::exception& e)
    {
      std::string content = "Could not process action Exception:";
      content.append(e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
      return;
    }
  }

  void DoCreateOrUpdateEditorObject(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    TEST_AUTH_IMPL(response, request);
    try
    {
      std::string command_string = request->path_match[1];
      MENUENTRY_EDIT_CMD command = MENUENTRY_EDIT_CMD_to_enum(command_string.c_str());

      // deal with body
      nlohmann::json pt = nlohmann::json::parse(request->content);
      //static std::string jsonTryGetString(nlohmann::json &pt, std::string field, std::string _default)


      //if (pt.contains("objectName"))
      //{

      //}
      EditorCommandDTO dto(command);
      dto.token = pServer->GetToken(request);
      dto.objectName = GtwSysConfig::jsonTryGetString(pt, "objectName", "");
      dto.parentObjectName = pt["parentObjectName"].get<std::string>();
      dto.objectDataJson = pt["objectDataJson"].get<std::string>();
      dto.oldObjectDataJson = pt["oldObjectDataJson"].get<std::string>();
      dto.pt_objectDataJson = nlohmann::json::parse(dto.objectDataJson);
      dto.objectCollectionKind = pt["objectCollectionKind"].get<std::string>();
      dto.objectClassName = pt["objectClassName"].get<std::string>();

      //if (
      //  dto.objectName.find(' ', 0) != string::npos
      //  || dto.objectName.find('.', 0) != string::npos
      //  || dto.objectName.find(',', 0) != string::npos
      //  )
      //{
      //  pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not created or edited");

      //  //// Test for BroadcastEvent
      //  nlohmann::json args = nlohmann::json::parse("{\"tagName\": \"" + dto.objectName + "\"}");
      //  HttpServerCommon::SendWebTextMessage(
      //    ALERT_POPUP,
      //    message_error,
      //    "TR_ILLEGAL_CHARACTER",     // messageKey
      //    "Illegal character in name {{tagName}} (, . or space not allowed).",   // messageText
      //    args                                    // arguments
      //  );

      //  return;
      //}

      //GTWMasterDataObject *pMdo = nullptr;
      //GTWDEFS_STAT status = GetGTWApp()->findMdo(dto.objectName.c_str(), &pMdo);
      //if (status == GTWDEFS_STAT_SUCCESS && pMdo != nullptr
      //  && (command != MENU_CMD_ADD_MAPPING_SDO
      //    && command != MENU_CMD_ADD_MAPPING_MDO
      //    && command != MENU_CMD_EDIT
      //    && command != MENU_CMD_ADD_OPC_AE_ATTR))
      //{
      //  pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not created");
      //  //// Test for BroadcastEvent
      //  GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_OBJECT_TAGNAME_ALREADY_EXISTS", "Object {{arg1}} already exists.", dto.objectName.c_str());
      //  return;
      //}

      if (DispatchCreateOrUpdateCommand(dto, pServer, response, request) == true)
        return;

      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Invalid command");
      return;
    }
    catch (const std::exception &e) {
      std::string content = "Could not process action Exception:";
      content.append(e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
      return;
    }
  }

  void DoRemoveEditorObject(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    //TEST_AUTH_IMPL(response, request);

    //try
    //{
    //  GTWCollectionMember *pCollectionMember = nullptr;
    //  std::string command = request->path_match[1];
    //  if (command != MENUENTRY_EDIT_CMD_to_string(MENU_CMD_DELETE))
    //  {
    //    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Command must be : MENU_CMD_DELETE");
    //    return;
    //  }

    //  //Retrieve string:
    //  std::string query = request->query_string;

    //  // store query key/value pairs in a map
    //  std::map<std::string, std::string> params;

    //  pServer->ExtractParams(query, params);

    //  EditorCommandDTO dto(MENU_CMD_NONE);
    //  dto.token = pServer->GetToken(request);
    //  dto.objectName = params["objectName"];;
    //  //dto.parentObjectName = pt["parentObjectName"].get<std::string>();
    //  //dto.objectDataJson = pt["objectDataJson"].get<std::string>();
    //  //dto.pt_objectDataJson = nlohmann::json::parse(dto.objectDataJson);
    //  dto.objectCollectionKind = params["objectCollectionKind"];

    //  bool isBadDelete = false;
    //  std::string msg;
    //  isBadDelete = GTWHttpServerImplEditor<SOCK_TYP>::DeleteSdgObject(dto, msg);
    //  if (isBadDelete)
    //  {
    //    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", msg);
    //  }

    //  return;
    //}
    //catch (const std::exception &e) {
    //  std::string content = "Could not process action Exception:";
    //  content.append(e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Not Implemented");
    //  return;
    //}
  }

  void DoEditorAction(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    TEST_AUTH_IMPL(response, request);

    try
    {
      auto query = request->query_string;
      // store query key/value pairs in a map
      std::map<std::string, std::string> params;
      pServer->ExtractParams(query, params);
      std::string actionArg = params["action"];
      MENUENTRY_ACTION_CMD actionCmd = MENUENTRY_ACTION_CMD_to_enum(actionArg.c_str()); // MENUENTRY_ACTION_CMD_to_enum(dto.action.c_str());

      EditorCommandDTO dto(actionCmd); 
      dto.token = pServer->GetToken(request);
      dto.objectName = params["objectName"];
      dto.parentObjectName = ""; //  pt["parentObjectName"].get<std::string>();
      dto.objectDataJson = ""; // pt["objectDataJson"].get<std::string>();
      dto.pt_objectDataJson = nlohmann::json::parse("{}"); // nlohmann::json::parse(dto.objectDataJson);
      dto.objectCollectionKind = params["objectCollectionKind"];
      dto.objectClassName = ""; // objectClassName;
      //dto.action = actionArg; done in constructor
      dto.params[0] = ""; // params["parameter_0"];
      dto.params[1] = params["parameter_1"];
      dto.params[2] = params["parameter_2"];
      dto.params[3] = params["parameter_3"];
      dto.params[4] = params["parameter_4"];
      dto.params[5] = params["parameter_5"];
      dto.params[6] = params["parameter_6"];

      //MENUENTRY_ACTION_CMD command = dto.actionEnum; // MENUENTRY_ACTION_CMD_to_enum(dto.action.c_str());

      USER_PERMISSION_MASK userPermissions;
      std::string userRole;
      std::string userName;
      http_audit_record audit_record = pServer->IsTokenValidRequest(request, dto.token, userName, userPermissions, userRole);

      if (GtwSysConfig::gtwDoAuth() && CheckRoleCommandAction(USER_ROLE_ENUM_to_enum(userRole.c_str()), actionCmd))
      {
        std::string content = "Could not process action";
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
        return;
      }

      if (DispatchActionCommand(dto, pServer, response, request) == true) 
      {
        return;
      }
      else
      {
        std::string content = "Could not process action";
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
        return;
      }
    }
    catch (const std::exception &e) {
      std::string content = "Could not process action Exception:";
      content.append(e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
      return;
    }
  }

  static void ActionCreateTHXMLPointFile(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWSector *pSector = nullptr;
        if (pCollectionMember->IsA("GTWSector"))
        {
          pSector = dynamic_cast<GTWSector *>(pCollectionMember);
#if defined(_DEBUG) && defined(_WIN32)
          pSector->CreateTHXMLFile("C:\\test_harness_points.xml");
#endif
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void FillJsonWithParamInfo(TMWParam *pParam, int index, nlohmann::json &result)
  {
    CStdString paramValue = "";
    CStdString sObjName = "N/A";
    CStdString sObjKind = "MDO";
    CStdString filePath = pParam->GetFullPathToFile(index);
    CStdString isFilePresent = "False";
    std::string sFileInfo = "N/A";
    CStdString sSection = pParam->TMWParam_get_section();

    paramValue = pParam->TMWParam_convert_to_string(index);
    if (paramValue.length() >= 2 && paramValue[0] == '\"' && paramValue.back() == '\"')
      paramValue = paramValue.substr(1, paramValue.length() - 2);

    if (std::filesystem::exists(filePath.c_str()))
    {
      isFilePresent = "True";
      std::string current_extension = std::filesystem::path(filePath.c_str()).filename().extension().string();
      if (findCaseInsensitive(current_extension, "pem") != std::string::npos)
        GetPEMCertDescp(filePath, sFileInfo);
      else if (findCaseInsensitive(current_extension, "der") != std::string::npos)
        GetDERCertDescp(filePath, sFileInfo);
    }
    else
    {
      isFilePresent = "False";
    }

    // Param is not a file or folder
    if ((pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_IN_WORKSPACE_DIR) != PARAM_PURPOSE_MASK_CERTIFICATE_IN_WORKSPACE_DIR
      && (pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_WORKSPACE) != PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_WORKSPACE
      && (pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_PRIVATE) != PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_PRIVATE
      && (pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_OPCUA_CLT_TRUSTED) != PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_OPCUA_CLT_TRUSTED)
    {
      isFilePresent = "N/A";
    }

    if (sSection == "Physical Layer")
    {
      sObjName = GTWConfig::PhysComChnlAliasName(index);
      if (GTWConfig::PhysComProtocol(index) == GTWTYPES_PROTOCOL_S101 || GTWConfig::PhysComProtocol(index) == GTWTYPES_PROTOCOL_S104
        || GTWConfig::PhysComProtocol(index) == GTWTYPES_PROTOCOL_SDNP || GTWConfig::PhysComProtocol(index) == GTWTYPES_PROTOCOL_SMB)
        sObjKind = "SDO";
    }
    else if (sSection == "TASE2 Client")
    {
      sObjName = GTWConfig::TASE2ClientName(index);
    }
    else if (sSection == "IEC 61850 Client")
    {
      sObjName = GTWConfig::I61850ClientName(index);
    }
    else if (sSection == "IEC 61850 Client")
    {
      sObjName = GTWConfig::I61850ClientName(index);
    }
    else if (sSection == "IEC 61850 Server")
    {
      sObjName = GTWConfig::Isrv61850ServerName(index);
    }
    else if (sSection == "OPC UA Client")
    {
      sObjName = GTWConfig::OpcUaClientName(index);
      sObjKind = "SDO";
    }
    else if (sSection == "OPC UA Security")
    {
      sObjName = "OPCUaServer";
      sObjKind = "SDO";
    }

    if (sObjName == "")
      sObjName = "N/A";

    //Verify that the device initialized correctly
    bool isFoundInCollection = false;
    if (sObjKind == "MDO" && sObjName != "")
    {
      bool isOPCClient = TMWDEFS_FALSE;
      bool isM103 = TMWDEFS_FALSE;
      GTWCollectionMember* pMember = nullptr;
      GTWCollectionBase* pCollection = GetGTWApp()->getMdoCollection();
      if (pCollection->DeepFindCollectionMemberUsingName(sObjName, &pMember, isOPCClient, isM103) == GTWDEFS_STAT_SUCCESS)
        if (pMember != nullptr)
          isFoundInCollection = true;
    }
    else if (sObjKind == "SDO" && sObjName != "")
    {
      bool isOPCClient = TMWDEFS_FALSE;
      bool isM103 = TMWDEFS_FALSE;
      GTWCollectionMember* pMember = nullptr;
      GTWCollectionBase* pCollection = GetGTWApp()->getSdoCollection();
      if (pCollection->DeepFindCollectionMemberUsingName(sObjName, &pMember, isOPCClient, isM103) == GTWDEFS_STAT_SUCCESS)
        if (pMember != nullptr)
          isFoundInCollection = true;
    }

    if (isFoundInCollection)
    {
      nlohmann::json childData;
      childData["name"] = pParam->TMWParam_get_id();
      childData["value"] = paramValue;
      childData["objName"] = sObjName;
      childData["objKind"] = sObjKind;
      childData["filePath"] = filePath;
      childData["isFilePresent"] = isFilePresent;
      childData["fileInfo"] = sFileInfo;
      result.push_back(childData);
    }
  }

  static void ActionGetMissingLocalINIParam(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    nlohmann::json result;
    nlohmann::json localSubset;
    std::string jsonFile = GtwSysConfig::getLocalParamsPath();
    try 
    {
      std::ifstream file(jsonFile);
      if (!file.good())
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      localSubset = nlohmann::json::parse(file);
      if (localSubset.contains("parameters"))
      {
        std::string objectDataJson = localSubset["parameters"].dump();
        nlohmann::json j = nlohmann::json::parse(objectDataJson);
        for (const auto& [key, value] : j.items()) 
        {
          TMWParam* pParam = GTWConfig::GetParam(key);
          if (pParam != nullptr)
          {
            CStdString sSection = pParam->TMWParam_get_section();
            if (sSection == "OPC UA Security")
            {
              FillJsonWithParamInfo(pParam, 0, result);
            }
            else
            {
              for (int index = 0; index < pParam->TMWParam_get_dimension(0); index++)
              {
                if (pParam->TMWParam_index_was_specified(index) == TMWDEFS_TRUE)
                {
                  FillJsonWithParamInfo(pParam, index, result);
                }
              }
            }
          }
        }
      }
      nlohmann::json res;
      res["result"] = true;
      res["parameters"] = result;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    catch (const nlohmann::json::exception& e)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Params, nullptr, "Error parsing subset definition %s: %s", jsonFile, e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionGetCertificateInfo(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    nlohmann::json result;
    try
    {
      for (TMWParam* pParam = TMWParam::getMemberList()->GetFirstMember(); pParam != TMWDEFS_NULL; pParam = pParam->GetNextMember())
      {

        if ((pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_WORKSPACE) == PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_WORKSPACE ||
          (pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_PRIVATE) == PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_PRIVATE ||
          (pParam->GetPurposeTypeMask() & PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_OPCUA_CLT_TRUSTED) == PARAM_PURPOSE_MASK_CERTIFICATE_FILE_IN_OPCUA_CLT_TRUSTED
          )
        {
          if (pParam != nullptr)
          {
            CStdString sSection = pParam->TMWParam_get_section();
            if (sSection == "OPC UA Security")
            {
              FillJsonWithParamInfo(pParam, 0, result);
            }
            else
            {
              for (int index = 0; index < pParam->TMWParam_get_dimension(0); index++)
              {
                if (pParam->TMWParam_index_was_specified(index) == TMWDEFS_TRUE)
                {
                  FillJsonWithParamInfo(pParam, index, result);
                }
              }
            }
          }
        }
      }
      nlohmann::json res;
      res["result"] = true;
      res["parameters"] = result;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    catch (const nlohmann::json::exception& e)
    {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionCreateDTMCSVPointFile(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWSector *pSector = nullptr;
        if (pCollectionMember->IsA("GTWSector"))
        {
          pSector = dynamic_cast<GTWSector *>(pCollectionMember);
#if defined(_DEBUG) && defined(_WIN32)
          pSector->CreateDTMCSVFile("C:\\dtm_points.csv");
#endif
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAutoCreateTags(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWSessionEditor *pEditor = (GTWSessionEditor *)((GTWSession*)pCollectionMember)->GetBaseEditor(dto);
        if (pEditor->MiscCommand(MENU_CMD_AUTO_CREATE_TAGS))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());

          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Status_Bar, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_COMMAND_SENT_TO_SERVER", "Command sent to server");
          // refresh ui will be done when the create auto tags finishes see: ResetDNPCreateTagsAuto and ResetIECCreateTagsAuto
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDisconnectRestartTase2Server(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTase2Server *pServerTase2 = nullptr;
        if (pCollectionMember->IsA("GTWTase2Server"))
        {
          pServerTase2 = dynamic_cast<GTWTase2Server *>(pCollectionMember);
        }
        if (pServerTase2 != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            if (pServerTase2->Stop())
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
              return;
            }
          }
          else if (dto.params[2] == "Restart")
          {
            if (pServerTase2->ReStart())
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDisconnectConnectFromTase2Server(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTase2 = nullptr;
        if (pCollectionMember->IsA("GTWTASE2Client"))
        {
          pClientTase2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);
        }
        if (pClientTase2 != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            pClientTase2->Disconnect(true);           
          }
          else if (dto.params[2] == "Connect")
          {
            pClientTase2->Connect();
          }
          else if (dto.params[2] == "ConnectDiscover")
          {
            pClientTase2->Connect(true, dto.token.c_str());
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }

          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }


  static void ActionSaveTase2ServerToFile(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status || pCollectionMember != nullptr)
      {
        GTWTase2Server *pServerTase2 = nullptr;
        if (pCollectionMember->IsA("GTWTase2Server"))
        {
          pServerTase2 = dynamic_cast<GTWTase2Server *>(pCollectionMember);
        }
        if (pServerTase2 != nullptr)
        {
          CStdString sFullPath;
          sFullPath = GtwSysConfig::getCurrentWorkSpacePath() + "/" + dto.params[2];
          if (pServerTase2->Save(sFullPath))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionSave61850ModelToFile(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status || pCollectionMember != nullptr)
      {
        GTW61850Client *pClient61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Client"))
        {
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        }
        if (pClient61850 != nullptr)
        {
          CStdString sFullPath;
          sFullPath = GtwSysConfig::getCurrentWorkSpacePath() + "/" + dto.params[2];
          tmw61850::XMLWriteOptions xmlWriteOptions;
          pClient61850->GetClientConnection()->Save(sFullPath, dto.params[2].c_str(), xmlWriteOptions);
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionEnableDisableRCB(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      GTW61850ReportControlBlock *p61850Report = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        if (pCollectionMember->IsA("GTW61850ReportControlBlock"))
          p61850Report = dynamic_cast<GTW61850ReportControlBlock *>(pCollectionMember);

        if (p61850Report != nullptr)
        {
          if (dto.params[2] == "EnableReport")
          {
            if (!GTW61850Client::EnableReport(p61850Report, false, false))
            {
              p61850Report->StartRetryThread();
            }
          }
          else
          {
            p61850Report->Disable();
          }          
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionEnableDisableDSTS(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr && !dto.params[2].empty())
      {
        if (pCollectionMember->IsA("GTWTASE2ReportedDataSetControlBlock"))
        {
          GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorGTWTASE2(dto, pCollectionMember, nullptr, false);
          if (bool bResult = cltReportedDataSetEditorGTWTASE2.EnableDisableReportedDataSetControlBlock(dto.params[2], pCollectionMember))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
            return;
          }
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }


    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionReadDataset(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {

        if (pCollectionMember->IsA("GTW61850ReportControlBlock"))
        {
          GTW61850ReportControlBlock* p61850Report = nullptr;
          p61850Report = dynamic_cast<GTW61850ReportControlBlock*>(pCollectionMember);
          if (p61850Report != nullptr)
          {
            nlohmann::json res;
            if (p61850Report->ReadDataset())
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTW61850PolledDataSet"))
        {
          GTW61850PolledDataSet* p61850PolledDataSet = nullptr;
          p61850PolledDataSet = dynamic_cast<GTW61850PolledDataSet*>(pCollectionMember);
          if (p61850PolledDataSet != nullptr)
          {
            GTW61850PolledDataSetEditor polledDataSetEditor(dto, p61850PolledDataSet, nullptr, false);
            nlohmann::json res;
            if (polledDataSetEditor.ReadDataset())
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTW61850PolledPointSet"))
        {
          GTW61850PolledPointSet* p61850PolledPointSet = nullptr;
          p61850PolledPointSet = dynamic_cast<GTW61850PolledPointSet*>(pCollectionMember);
          if (p61850PolledPointSet != nullptr)
          {
            GTW61850PolledPointSetEditor polledPointSetEditor(dto, p61850PolledPointSet, nullptr, false);
            nlohmann::json res;
            if (polledPointSetEditor.ReadAllPoint())
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }

        }
        if (pCollectionMember->IsA("GTWTASE2ReportedDataSetControlBlock"))
        {
          GTWTASE2ReportedDataSetControlBlock* pTASE2Report = nullptr;
          pTASE2Report = dynamic_cast<GTWTASE2ReportedDataSetControlBlock*>(pCollectionMember);

          if (pTASE2Report != nullptr && pTASE2Report->GetTASE2Client())
          {
            GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorTASE2(dto, nullptr, pTASE2Report->GetTASE2Client(), false);
            nlohmann::json res;
            if (cltReportedDataSetEditorTASE2.ReadDataset(pTASE2Report))
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTWTASE2PolledDataSet"))
        {
          GTWTASE2PolledDataSet* pTASE2PolledDataSet = nullptr;
          pTASE2PolledDataSet = dynamic_cast<GTWTASE2PolledDataSet*>(pCollectionMember);
          if (pTASE2PolledDataSet != nullptr && pTASE2PolledDataSet->GetTASE2Client())
          {
            GTWTASE2PolledDataSetEditor polledDataSetEditorTASE2(dto, nullptr, pTASE2PolledDataSet->GetTASE2Client(), false);
            nlohmann::json res;
            if (polledDataSetEditorTASE2.ReadDataset(pTASE2PolledDataSet))
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  
  static void ActionVerifyDataset(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        if (pCollectionMember->IsA("GTW61850ReportControlBlock"))
        { 
          GTW61850ReportControlBlock* p61850Report = nullptr;
          p61850Report = dynamic_cast<GTW61850ReportControlBlock *>(pCollectionMember);
          if (p61850Report != nullptr)
          {
            nlohmann::json res;
            tmw::String sn;
            if (p61850Report->VerifyDataset())
            {
              res["result"] = true;
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_DATASET_VERIFY_SUCCEED", "Dataset '{{arg1}}' successfully verified!", p61850Report->GetDataSetControl()->GetFullName(sn));
            }
            else
            {
              res["result"] = false;
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_DATASET_VERIFY_FAILED", "Dataset verification failed for '{{arg1}}'. Please make sure client and server dataset definitions match otherwise reports will not work properly.", p61850Report->GetDataSetControl()->GetFullName(sn));
            }
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTW61850PolledDataSet"))
        {
          GTW61850PolledDataSet* p61850PolledDataSet = nullptr;
          p61850PolledDataSet = dynamic_cast<GTW61850PolledDataSet*>(pCollectionMember);
          if (p61850PolledDataSet != nullptr && p61850PolledDataSet->GetClientConnection() != nullptr)
          {
            GTW61850PolledDataSetEditor polledDataSetEditor(dto, p61850PolledDataSet, nullptr, false);
            nlohmann::json res;
            if (polledDataSetEditor.VerifyDataset(p61850PolledDataSet->GetClientConnection()))
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTWTASE2ReportedDataSetControlBlock"))
        {
          GTWTASE2ReportedDataSetControlBlock* pTASE2Report = nullptr;
          pTASE2Report = dynamic_cast<GTWTASE2ReportedDataSetControlBlock*>(pCollectionMember);

          if (pTASE2Report != nullptr && pTASE2Report->GetTASE2Client())
          {
            GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorTASE2(dto, nullptr, pTASE2Report->GetTASE2Client(), false);
            nlohmann::json res;
            if (cltReportedDataSetEditorTASE2.VerifyDataset(pTASE2Report))
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        if (pCollectionMember->IsA("GTWTASE2PolledDataSet"))
        {
          GTWTASE2PolledDataSet* pTASE2PolledDataSet = nullptr;
          pTASE2PolledDataSet = dynamic_cast<GTWTASE2PolledDataSet*>(pCollectionMember);
          if (pTASE2PolledDataSet != nullptr && pTASE2PolledDataSet->GetTASE2Client())
          {
            GTWTASE2PolledDataSetEditor polledDataSetEditorTASE2(dto, nullptr, pTASE2PolledDataSet->GetTASE2Client(), false);
            nlohmann::json res;
            if (polledDataSetEditorTASE2.VerifyDataset(pTASE2PolledDataSet))
              res["result"] = true;
            else
              res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionReset61850RetryConnectCount(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Client *pClient61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Client"))
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        if (pClient61850 != nullptr)
        {
          pClient61850->ResetConnectRetryCount();

          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  
  static void ActionResetAverageMdoUpdateRate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWAverageMdoUpdateRate* pAverageMdoUpdateRate = nullptr;
        if (pCollectionMember->IsA("GTWAverageMdoUpdateRate"))
          pAverageMdoUpdateRate = dynamic_cast<GTWAverageMdoUpdateRate*>(pCollectionMember);
        if (pAverageMdoUpdateRate != nullptr)
        {
          pAverageMdoUpdateRate->Reset();

          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionPerformWriteAction(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        //GTWMBWriteAction *mbWriteAction = nullptr;
        if (pCollectionMember->IsA("GTWMBWriteAction"))
        {
          GTWModbusSessionActionEditor modbusSessionActionEditor(dto, nullptr, pCollectionMember, false);
          if (modbusSessionActionEditor.MiscCommand(MENU_CMD_PERFORM_WRITE_ACTION))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            //GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRestart61850Server(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Server *pServer61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Server"))
        {
          pServer61850 = dynamic_cast<GTW61850Server *>(pCollectionMember);
        }
        if (pServer61850 != nullptr)
        {
          if (pServer61850->ReStart())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionStop61850Server(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Server* pServer61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Server"))
        {
          pServer61850 = dynamic_cast<GTW61850Server*>(pCollectionMember);
        }
        if (pServer61850 != nullptr)
        {
          if (pServer61850->Stop())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDisconnectConnectFrom61850Server(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Client *pClient61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Client"))
        {
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        }
        if (pClient61850 != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            pClient61850->Disconnect();
            pClient61850->SetConnectionActive(false);
          }
          else if (dto.params[2] == "Connect")
          {
            pClient61850->Connect();
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }

          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionOperateTase2Control(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      CStdString sValue = dto.params[2].c_str();
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2DataAttributeMDO *pTase2DataAttributeMDO = nullptr;

        if (pCollectionMember->IsA("GTWTASE2DataAttributeMDO")) 
          pTase2DataAttributeMDO = dynamic_cast<GTWTASE2DataAttributeMDO*>(pCollectionMember);

        if (pTase2DataAttributeMDO != nullptr)
        {
          GTWTASE2CommandPointEditor tase2CommandPointEditor(dto, pTase2DataAttributeMDO, pTase2DataAttributeMDO->GetClientNode(), pTase2DataAttributeMDO->GetControlBlock(), false);
          if (tase2CommandPointEditor.OperateCommand(sValue))
          {
            LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "%s", "Successfully sent operate command sequence.");
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            if (pTase2DataAttributeMDO->GetControlBlock() && pTase2DataAttributeMDO->GetClientNode())
            {
              std::string name = pTase2DataAttributeMDO->GetClientNode()->GetAliasName();
              name.append(".");
              name.append(pTase2DataAttributeMDO->GetControlBlock()->GetMemberName().c_str());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), name.c_str(), isUsedForMdos);
            }
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRead61850Tag(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      GTW61850DataAttributeMDO *pMDO = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status == true && pCollectionMember->IsA("GTW61850DataAttributeMDO"))
      {
        pMDO = dynamic_cast<GTW61850DataAttributeMDO *>(pCollectionMember);
        if (pMDO != nullptr)
        {
          pMDO->ReadDataAttributes();

          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          
          if (((GTW61850DataAttributeMDO *)pCollectionMember)->GetControlBlock())
          {
            std::string name = pMDO->GetClientNode()->GetAliasName();
            name.append(".");
            name.append(((GTW61850DataAttributeMDO *)pCollectionMember)->GetControlBlock()->GetMemberName().c_str());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), name.c_str(), isUsedForMdos);
          }
          else
          {
            std::string name = dto.params[1].substr(0, (dto.params[1].find_last_of('.')));
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), name.c_str(), isUsedForMdos);
          }
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionReadICCPTag(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      GTWTASE2DataAttributeMDO* pMDO = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status == true && pCollectionMember->IsA("GTWTASE2DataAttributeMDO"))
      {
        pMDO = dynamic_cast<GTWTASE2DataAttributeMDO*>(pCollectionMember);
        if (pMDO != nullptr)
        {
          GTWTASE2Client* pClient = pMDO->GetClientNode();
          if (pClient && pClient->Readitem(pMDO))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = "MDO";

            if (((GTWTASE2DataAttributeMDO*)pCollectionMember)->GetControlBlock())
            {
              std::string name = pMDO->GetClientNode()->GetAliasName();
              name.append(".");
              name.append(((GTWTASE2DataAttributeMDO*)pCollectionMember)->GetControlBlock()->GetMemberName().c_str());
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), name.c_str(), isUsedForMdos);
            }
            else
            {
              std::string name = dto.params[1].substr(0, (dto.params[1].find_last_of('.')));
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), name.c_str(), isUsedForMdos);
            }
            return;
          }

        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionExecuteSql(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    bool bQueryOk = false;
    GTWODBCClient* pODBCClient = nullptr;
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        if (pCollectionMember->IsA("GTWODBCClient"))
          pODBCClient = dynamic_cast<GTWODBCClient*>(pCollectionMember);

        if (pODBCClient != nullptr)
        {
          nlohmann::json jsonTableSource = "";
          GTWODBCQueryEditor ODBCQueryEditor(dto, NULL, pODBCClient, pODBCClient->getObIndex(), false);
          CStdString query = dto.params[2].c_str();
          query.Replace("%3F", "?");
          if (CStdString(query).ToUpper().Left(6) == "INSERT")
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, ODBCQueryEditor.GetDTOToken(),
              "TR_ODBC_FAILED_INSERT_NOT_SUPPORTED", "Insert not supported for test");
          }
          else
          {
            jsonTableSource = ODBCQueryEditor.ExecuteSql(query, dto.params[3], bQueryOk);
          }

          if (!jsonTableSource.is_null())
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = jsonTableSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    if (bQueryOk)
    {
      pServer->BuildOkResponse(response, "application/json", "");
    }
    else
    {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    }
  }

  static void ActionChangeTable(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWODBCClient *pODBCClient = nullptr;

        if (pCollectionMember->IsA("GTWODBCClient"))
          pODBCClient = dynamic_cast<GTWODBCClient *>(pCollectionMember);

        if (pODBCClient != nullptr)
        {
          nlohmann::json jsonTableSource = "";
          GTWODBCQueryEditor ODBCQueryEditor(dto, NULL, pODBCClient, pODBCClient->getObIndex(), false);

          jsonTableSource = ODBCQueryEditor.ShowTable(dto.params[2]);
          if (!jsonTableSource.is_null())
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = jsonTableSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionClearModel(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString dsJsonSource = "";
          GTWTASE2ClientEditor clientEditorTase2(dto, pClientTASE2, true);
          if (clientEditorTase2.ClearModel())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionLoadModel(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString dsJsonSource = "";
          GTWTASE2ClientEditor clientEditorTase2(dto, pClientTASE2, true);

          if (clientEditorTase2.LoadFileToModel(dto.params[2]))
          {
            dsJsonSource = clientEditorTase2.GetDomainsJson(pClientTASE2);
            if (dsJsonSource != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["data"] = dsJsonSource;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionExportModel(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString sFullPath;
          sFullPath = GtwSysConfig::getCurrentWorkSpacePath() + "/" + dto.params[2] + ".csv";
          if (pClientTASE2->GetClientConnection()->SaveToCSV(sFullPath.c_str()))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  
  static void ActionLoadEditorFiles(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          GTWTASE2ClientEditor clientEditorTase2(dto, pClientTASE2, true);
          std::string json = clientEditorTase2.LoadEditorfiles(".csv .xml .CSV .XML", "CurrentWorkspace");
          nlohmann::json res;
          res["result"] = true;
          res["data"] = json;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionChangeSelectedDomain(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString dsJsonSource = "";
          GTWTASE2ClientEditor clientEditorTase2(dto, pClientTASE2, true);
          dsJsonSource = clientEditorTase2.GetDataPointsJson(dto.params[2]);
          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionValidateEquation(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWmdoEquationEditor mdoEquationEditor(dto, nullptr, nullptr, true);
      CStdString query = dto.params[1].c_str();
      query.Replace("%2B", "+");
      if (mdoEquationEditor.ValidateEquation(query))
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionChangeDAPointList(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTW61850DataAttributeMDOEditor" && dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      GTW61850ControlBlock *p61850ControlBlock = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status == true && pCollectionMember->IsA("GTW61850ControlBlock"))
      {
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);
        if (p61850ControlBlock != nullptr)
        {
          GTW61850DataAttributeMDOEditor dataAttributeMDOEditorGTW61850(dto, nullptr, nullptr, true);

          bool dontShowQT = true;
          CStdString sSearch = "";
          CStdString fcs = "";

          if (dto.params[2] == "false")
            dontShowQT = false;

          if (dto.params[3] != "")
            fcs = dto.params[3];

          if (dto.params[4] != "")
            sSearch = dto.params[4];

          nlohmann::json DAPointsJsonSource = dataAttributeMDOEditorGTW61850.ListDAPointsJson(p61850ControlBlock, dontShowQT, fcs, sSearch);
          if (!DAPointsJsonSource.is_null())
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = DAPointsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
    }
    if (dto.objectName == "GTW61850WriteablePointEditor" && dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      GTW61850Client *pClient61850 = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status == true && pCollectionMember && pCollectionMember->IsA("GTW61850Client"))
      {
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        if (pClient61850 != nullptr)
        {
          GTW61850WriteablePointEditor writeablePointEditorGTW61850(dto, nullptr, pClient61850, nullptr, true);

          bool bShowSP = false;
          bool bShowCF = false;
          bool bShowSV = false;
          CStdString sSearch = "";

          if (dto.params[2].find("FC_SP") != std::string::npos)
            bShowSP = true;
          if (dto.params[2].find("FC_CF") != std::string::npos)
            bShowCF = true;
          if (dto.params[2].find("FC_SV") != std::string::npos)
            bShowSV = true;

          if (dto.params[3] != "")
            sSearch = dto.params[3];

          nlohmann::json DAPointsJsonSource = writeablePointEditorGTW61850.ListDAPointsJson(bShowSP, bShowCF, bShowSV, sSearch);
          if (!DAPointsJsonSource.is_null())
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = DAPointsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
    }
    if (dto.objectName == "GTW61850SdoEditor" && dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status == true && pCollectionMember->IsA("GTW61850Server"))
      {
        if (GTW61850Server *p61850Server = dynamic_cast<GTW61850Server *>(pCollectionMember); p61850Server != nullptr)
        {
          GTW61850SdoEditor sdoEditor(dto, p61850Server, nullptr, true);
          bool dontShowQT = true;
          bool showOnlyExtRef = false;
          CStdString sSearch = "";
          CStdString dataSet = "";
          CStdString fcs = "";

          if (dto.params[2] == "false")
            dontShowQT = false;

          if (dto.params[3] != "")
            fcs = dto.params[3];

          if (dto.params[4] != "")
            sSearch = dto.params[4];

          if (dto.params[5] != "")
            dataSet = dto.params[5];

          nlohmann::json DAPointsJsonSource = sdoEditor.ListDAPointsJson(dontShowQT, showOnlyExtRef,dataSet, fcs, sSearch);
          if (!DAPointsJsonSource.is_null())
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = DAPointsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionChangeFCDS(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;
      if (pCollectionMember->IsA("GTW61850Client"))
      {
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
      }
      if (pClient61850 != nullptr)
      {
        GTW61850DatasetEditor datasetEditor61850(dto, nullptr, pClient61850, true);
        GTWBaseEditor baseEditor(dto, nullptr, true, 0, 0, 0);

        datasetEditor61850.SetAllFCs(!baseEditor.GetBoolValueFromString(dto.params[2].c_str()));
        nlohmann::json datasetJsonSource;
        datasetEditor61850.LoadTree(pClient61850, datasetJsonSource);
        if (datasetJsonSource.size() != 0)
        {
          nlohmann::json res;
          res["result"] = true;
          res["data"] = datasetJsonSource;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDeleteDS(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTW61850PolledDataSetEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr && !dto.params[2].empty())
      {
        GTW61850Client *pClient61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Client"))
        {
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        }
        if (pClient61850 != nullptr)
        {
          GTW61850DatasetEditor datasetEditor61850(dto, nullptr, pClient61850, true);
          if (datasetEditor61850.DeleteDynamicDataSet(pClient61850, dto.params[2].c_str()))
          {
            GTW61850PolledDataSetEditor polledDataSetEditor61850(dto, nullptr, pClient61850, true);
            CStdString sPolledDataSetID = "";
            CStdString dsJsonSource = polledDataSetEditor61850.GetDatasetJson(sPolledDataSetID);
            nlohmann::json res;
            res["result"] = true;
            res["dataDS"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    if (dto.objectName == "GTWTASE2PolledDataSetEditor" || dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr  && !dto.params[2].empty())
      {
        GTWTASE2Client *pClientTASE2 = nullptr;
        if (pCollectionMember->IsA("GTWTASE2Client"))
        {
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);
        }
        if (pClientTASE2 != nullptr)
        {
          GTWTASE2DatasetEditor datasetEditorTASE2(dto, "", pClientTASE2, true);
          if (datasetEditorTASE2.DeleteDataSet(pClientTASE2, dto.params[2].c_str()))
          {
            CStdString dsJsonSource = "";
            if (dto.objectName == "GTWTASE2PolledDataSetEditor")
            {
              GTWTASE2PolledDataSetEditor polledDataSetEditorTASE2(dto, nullptr, pClientTASE2, true);
              CStdString sDataSetID = "";
              dsJsonSource = polledDataSetEditorTASE2.GetDatasetJson(sDataSetID);
            }
            else if (dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
            {
              GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorGTWTASE2(dto, nullptr, pClientTASE2, true);
              CStdString sDataSetID = "";
              dsJsonSource = cltReportedDataSetEditorGTWTASE2.GetDatasetJson(sDataSetID);
            }
            nlohmann::json res;
            res["result"] = true;
            res["dataDS"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAddTASE2DSTS(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pClient != nullptr && dto.params[2] != "" && dto.params[3] != "" && dto.params[4] != "")
      {
        GTWTASE2CltReportedDataSetEditor tase2CltReportedDataSetEditor(dto, nullptr, pClient, true);
        if (tase2CltReportedDataSetEditor.UpdateObjectAdd(dto.params[2], dto.params[4], dto.params[3]))
        {
          if (tase2CltReportedDataSetEditor.SetReportedDataSet())
          {
            if (tase2CltReportedDataSetEditor.SaveObject())
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionAddTASE2DSPD(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    if (bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pClient != nullptr && dto.params[2] !="" && dto.params[3] != "" && dto.params[4] != "")
      {
        GTWTASE2PolledDataSetEditor tase2PolledDataSetEditor(dto, nullptr, pClient, true);
        if (tase2PolledDataSetEditor.UpdateObjectAdd(dto.params[2], dto.params[4], dto.params[3]))
        {
          if (tase2PolledDataSetEditor.SetPolledDataSet())
          {
            if (tase2PolledDataSetEditor.SaveObject())
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionAddTASE2CommandPoint(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2ControlBlock* pTASE2ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTWTASE2ControlBlock"))
        pTASE2ControlBlock = dynamic_cast<GTWTASE2ControlBlock*>(pCollectionMember);

      if (pTASE2ControlBlock != nullptr)
      {
        GTWTASE2CommandPointEditor tase2CommandPointEditor(dto, nullptr, pTASE2ControlBlock->GetTASE2Client(), pTASE2ControlBlock, true);
        CStdString tagName = dto.params[2].c_str();
        CStdString tagOptions = dto.params[3].c_str();
        CStdString tagDescription = dto.params[4].c_str();
        CStdString userTagName = dto.params[5].c_str();
        CStdString tagTase2Type = dto.params[6].c_str();
        if (tase2CommandPointEditor.UpdateObject(tagName, tagOptions, tagDescription, userTagName, tagTase2Type))
        {
          if (tase2CommandPointEditor.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionReadConfigFromServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    if (bool status = GetGTWApp()->deepFindMember(dto.params[1].c_str(), dto.objectCollectionKind, &pCollectionMember); status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;
      if (pCollectionMember->IsA("GTW61850Client"))
      {
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
      }
      if (pClient61850 != nullptr)
      {
        nlohmann::json rcbJson;
        GTW61850ReportEditor reportEditor61850(dto, nullptr, pClient61850, true);
        if (reportEditor61850.ReadRcbFromServer(dto.params[2].c_str(), rcbJson))
        {
          nlohmann::json res;
          res["result"] = true;
          res["data"] = rcbJson;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionChangeDataSetName(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTW61850ReportEditor" || dto.objectName == "GTW61850GOOSEEditor" || dto.objectName == "GTW61850PolledDataSetEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Client *pClient61850 = nullptr;
        if (pCollectionMember->IsA("GTW61850Client"))
        {
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        }
        if (pClient61850 != nullptr)
        {
          CStdString dsJsonSource = "";
          if (dto.objectName == "GTW61850ReportEditor")
          {
            GTW61850ReportEditor reportEditor61850(dto, nullptr, pClient61850, true);
            tmw61850::ReportControl *pRPT = reportEditor61850.GetReportControl(dto.params[2]);
            if (GTW61850ClientEditor::ChangeDataSetName(dto.token.c_str(), (const char *)dto.params[3].c_str(), pRPT, pClient61850->GetClientConnection())) {
              CStdString sRCBIndex = "-1";
              CStdString sRCBName = "";
              dsJsonSource = reportEditor61850.GetRCBJson(sRCBIndex, sRCBName);
            }
          }
          if (dto.objectName == "GTW61850GOOSEEditor")
          {
            GTW61850GOOSEEditor GOOSEEditor61850(dto, nullptr, pClient61850, true);
            tmw61850::GOOSEControl *pGC = GOOSEEditor61850.GetGOOSEControl(dto.params[2]);
            if (GTW61850ClientEditor::ChangeDataSetName(dto.token.c_str(), (const char *)dto.params[3].c_str(), pGC, pClient61850->GetClientConnection())) {
              CStdString sGOOSEName = "";
              CStdString sGOOSEatasetName = "";
              dsJsonSource = GOOSEEditor61850.GetGCBJson(sGOOSEName, sGOOSEatasetName);
            }
          }
          else if (dto.objectName == "GTW61850PolledDataSetEditor")
          {
            GTW61850PolledDataSetEditor polledDataSetEditor61850(dto, nullptr, pClient61850, true);
            CStdString sPolledDataSetID = "";
            dsJsonSource = polledDataSetEditor61850.GetDatasetJson(sPolledDataSetID);
          }

          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["dataDS"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    if (dto.objectName == "GTWTASE2PolledDataSetEditor" || dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString dsJsonSource = "";
          if (dto.objectName == "GTWTASE2PolledDataSetEditor")
          {
            GTWTASE2PolledDataSetEditor polledDataSetEditorTASE2(dto, nullptr, pClientTASE2, true);
            CStdString sDataSetID = "";
            dsJsonSource = polledDataSetEditorTASE2.GetDatasetJson(sDataSetID);
          }
          else if (dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
          {
            GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorGTWTASE2(dto, nullptr, pClientTASE2, true);
            CStdString sDataSetID = "";
            dsJsonSource = cltReportedDataSetEditorGTWTASE2.GetDatasetJson(sDataSetID);
          }
          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["dataDS"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionGetDataSet(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTWTASE2ClientModelEditor")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client* pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          CStdString dsJsonSource = "";
          GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorGTWTASE2(dto, nullptr, pClientTASE2, true);
          CStdString sDataSetID = "";
          dsJsonSource = cltReportedDataSetEditorGTWTASE2.GetDatasetJson(sDataSetID);

          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["dataDS"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionGetDatasetMember(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTW61850ReportEditor" || dto.objectName == "GTW61850GOOSEEditor" || dto.objectName == "GTW61850PolledDataSetEditor" || dto.objectName == "GTW61850ChangeDataSetEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850Client *pClient61850 = nullptr;

        if (pCollectionMember->IsA("GTW61850Client"))
        {
          pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);
        }

        if (pClient61850 != nullptr)
        {
          std::string availableDataset = "";
          if (dto.objectName == "GTW61850GOOSEEditor")
          {
            GTW61850GOOSEEditor GOOSEEditor61850(dto, nullptr, pClient61850, true);
            availableDataset = GOOSEEditor61850.GetDatasetMemberJsonFromReport(dto.params[2]);
          }
          else if (dto.objectName == "GTW61850ReportEditor")
          {
            GTW61850ReportEditor reportEditor61850(dto, nullptr, pClient61850, true);
            availableDataset = reportEditor61850.GetDatasetMemberJsonFromReport(dto.params[2]);
          }
          else if (dto.objectName == "GTW61850PolledDataSetEditor")
          {
            GTW61850PolledDataSetEditor polledDataSetEditor61850(dto, nullptr, pClient61850, true);
            availableDataset = polledDataSetEditor61850.GetDatasetMemberJsonFromDS(dto.params[2]);
          }
          else if (dto.objectName == "GTW61850ChangeDataSetEditor")
          {
            GTW61850ChangeDataSetEditor changeDataSetEditor61850(dto, nullptr, pClient61850, true);
            availableDataset = changeDataSetEditor61850.GetDatasetMemberJsonFromDataSet(dto.params[2]);
          }

          if (availableDataset != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = availableDataset;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    if (dto.objectName == "GTWTASE2PolledDataSetEditor" || dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2Client *pClientTASE2 = nullptr;

        if (pCollectionMember->IsA("GTWTASE2Client"))
          pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

        if (pClientTASE2 != nullptr)
        {
          std::string availableDataset = "";
          if (dto.objectName == "GTWTASE2PolledDataSetEditor")
          {
            GTWTASE2PolledDataSetEditor polledDataSetEditorTASE2(dto, nullptr, pClientTASE2, true);
            availableDataset = polledDataSetEditorTASE2.GetDatasetMemberJsonFromDS(dto.params[2]);
          }
          else if (dto.objectName == "GTWTASE2CltReportedDataSetEditor" || dto.objectName == "GTWTASE2ClientModelEditor")
          {
            GTWTASE2CltReportedDataSetEditor cltReportedDataSetEditorGTWTASE2(dto, nullptr, pClientTASE2, true);
            availableDataset = cltReportedDataSetEditorGTWTASE2.GetDatasetMemberJsonFromDS(dto.params[2]);
          }
          if (availableDataset != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = availableDataset;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRefreshEquationArgument(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWmdoEquationEditor equationEditor(dto, nullptr, nullptr, true);
      bool includeSDO = dto.params[1] == "true";
      nlohmann::json equationParameterJson = equationEditor.GetBDOJson(includeSDO);
      if (equationParameterJson != "")
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = equationParameterJson;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
    }
  }

  static void ActionTestODBCConnectionString(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    tmw::String temp("N/A");
    bool failed = true;


    try
    {
      tmw::String  odbc;
      tmw::String dsnString = dto.params[1].c_str();
      dsnString.left(5, odbc);
      if (odbc.equalsNoCase("ODBC;"))
        dsnString.erase(0, 5);

      TMWODBC::DatabaseConnection dbCon;
      SQLRETURN ret = dbCon.open("test", (const char*)dsnString, NULL, NULL);
      if ((ret == SQL_SUCCESS) || (ret == SQL_SUCCESS_WITH_INFO))
      {
        failed = false;
      }
    }
    catch (tmw::Exception &ex)
    {
      temp = ex.getCharErrorMessageBlock();
    }
    catch (...)
    {
      temp = "Severe Error Caught";
    }
  
    if (failed)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, dto.token.c_str(), "TR_ODBC_CLIENT_CONNECT_RAW_FAIL", "Could not connect to using connection string = {{arg1}}.Error message{{arg2}}", dto.params[1].c_str(),(const char*)temp );
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }

    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_ODBC, dto.token.c_str(), "TR_SUCCES_ARG1", "Success: {{arg1}}", "Connection succeeded");


    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkResponse(response, "application/json", res.dump());
    return;
  }

  static void ActionLoadConfigFromServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    tmw::String subNetworkName, subNetworkType;
    tmw61850::CommunicationAddress comAddr;

    CStdString myPath = GtwSysConfig::getCurrentWorkSpacePath() + "/";
    CStdString sSCLPath = myPath + dto.params[1].c_str();
    CStdString sIED, sAP;

    const char* sAccessPoint = nullptr;
    if (dto.params[3].length() > 0) // then client ied selected
    {
      sIED = dto.params[3].c_str();
      // accesspoint must be null for a client ied for tmw61850::i61850Base::LoadCommunnicationFromSCL
    }
    else
    {
      CStdString ied_name = dto.params[2].c_str();
      GTW61850Server::GetIEDAccessPointFromFile(ied_name, sSCLPath, sIED, sAP);
      sAccessPoint = sAP.c_str();
    }

    if (!tmw61850::i61850Base::LoadCommunnicationFromSCL(
      sSCLPath.c_str(),
      sIED.c_str(),
      sAccessPoint,
      subNetworkName, subNetworkType, comAddr))
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Warning, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_LOAD_COMMUNICATION", "Failed to load communication section of the SCL file. Using defaults.");
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }

    tmw::Array<tmw61850::CommunicationAddress::AddressPair> &addresses = comAddr.GetAddressPairs();

    nlohmann::json jServerInfo;
    jServerInfo["Isrv61850ServerIPAddress"] = "";
    jServerInfo["Isrv61850ServerIPPort"] = "";
    jServerInfo["Isrv61850ServerTransportAddress"] = "";
    jServerInfo["Isrv61850ServerSessionAddress"] = "";
    jServerInfo["Isrv61850ServerPresentationAddress"] = "";
    jServerInfo["Isrv61850ServerAppID"] = "";
    jServerInfo["Isrv61850ServerAPInvokeID"] = "";
    jServerInfo["Isrv61850ServerAEQualifier"] = "";
    jServerInfo["Isrv61850ServerAEInvokeID"] = "";
    

    for (unsigned int i = 0; i < addresses.size(); i++)
    {
      tmw61850::CommunicationAddress::AddressPair &addrPair = addresses[i];
      CStdString sValue = (const char*)addrPair.m_value;

      switch (addrPair.m_pType)
      {
      case tmw61850::AddressP::PTYPE::IP:
        jServerInfo["Isrv61850ServerIPAddress"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_TSEL:
        jServerInfo["Isrv61850ServerTransportAddress"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_SSEL:
        jServerInfo["Isrv61850ServerSessionAddress"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_PSEL:
        jServerInfo["Isrv61850ServerPresentationAddress"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_AP_Title:
        jServerInfo["Isrv61850ServerAppID"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_AP_Invoke:
        jServerInfo["Isrv61850ServerAPInvokeID"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_AE_Qualifier:
        jServerInfo["Isrv61850ServerAEQualifier"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::OSI_AE_Invoke:
        jServerInfo["Isrv61850ServerAEInvokeID"] = sValue;
        break;
      case tmw61850::AddressP::PTYPE::MMS_Port:
        jServerInfo["Isrv61850ServerIPPort"] = sValue;
        break;
      }
    }

    //CR#19904
    //if (jServerInfo["Isrv61850ServerIPAddress"] == "")
    //  jServerInfo["Isrv61850ServerIPAddress"] = "127.0.0.1";

    //if (jServerInfo["Isrv61850ServerIPPort"] == "")
    //  jServerInfo["Isrv61850ServerIPPort"] = "102";

    nlohmann::json res;
    res["result"] = true;
    res["data"] = jServerInfo;
    pServer->BuildOkResponse(response, "application/json", res.dump());
  }

  static void ActionGetAvailableIEDFromFile(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWBaseEditor baseEditor(dto, nullptr, true, 0, 0, 0);
    bool bIsServer = baseEditor.GetBoolValueFromString(dto.params[2]);

    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);
    std::string availableIED = clientEditor61850.GetAvailableIEDsFromFileAsJson(dto.params[1], bIsServer);
    nlohmann::json res;
    if (res["data"] != NULL && res["data"] != "")
    {
      res["result"] = true;
      res["data"] = availableIED;
    }
    pServer->BuildOkResponse(response, "application/json", res.dump());
  }

  static void ActionGetServerFromSCLFileName(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWBaseEditor baseEditor(dto, nullptr, true, 0, 0, 0);
    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);
    std::string server = clientEditor61850.GetAvailableIEDsFromFileAsJson(dto.params[1], true);
    nlohmann::json res;
    if (server != "")
    {
      res["result"] = true;
      res["data"] = server;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#if USE_GOOSE_MONITOR
  static void ActionGetGOOSEMonitorStreams(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWGooseMonitor *pGooseMonitor = nullptr;
      nlohmann::json streamGOOSEJson;
      GTWGooseMonitorEditor gooseMonitorEditor(dto, pGooseMonitor, true);
      std::list<std::string> goosePathList;
      std::list<unsigned short> thresholds;
      if (gooseMonitorEditor.ListGOOSEStreamsJson(dto.params[1], streamGOOSEJson, goosePathList, thresholds))
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = streamGOOSEJson;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
  }
  static void ActionSubscribeUnsubscribeGooseStream(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWGooseSubscription *pGooseSubscription = nullptr;
        if (pCollectionMember->IsA("GTWGooseSubscription"))
          pGooseSubscription = dynamic_cast<GTWGooseSubscription *>(pCollectionMember);

        if (pGooseSubscription != nullptr)
        {
          GTWGooseSubscriptionEditor gooseSubscriptionEditor(dto, pGooseSubscription);
          bool result;
          if (dto.params[2] == "true")
            result = gooseSubscriptionEditor.MiscCommand(MENU_CMD_SUBSCRIBE_GOOSE_STREAM);
          else
            result = gooseSubscriptionEditor.MiscCommand(MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM);

          if (result)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif

  static void ActionShowConfigInfoTase2(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      std::string sTase2ServerName = dto.params[1].c_str();
      std::string sDomainName = dto.params[2].c_str();
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(sTase2ServerName, dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        tmwTase2::Tase2Base* pConnection;

        if (pCollectionMember->IsA("GTWTase2Server")) 
        {
          GTWTase2Server* pTase2Server = dynamic_cast<GTWTase2Server*>(pCollectionMember);
          if (pTase2Server != nullptr)
            pConnection = pTase2Server->GetServerConnection();
        }
        else if (pCollectionMember->IsA("GTWTASE2Client"))
        {
          GTWTASE2Client* pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);
          if (pTase2Client != nullptr)
            pConnection = pTase2Client->GetClientConnection();
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not display ICCP configuration");
          return;
        }

        if (pConnection != nullptr)
        {
          std::list<CStdString> sConfigStringList;
          GTWTase2Server::getDomainConfigString(pConnection , sDomainName, sConfigStringList);
          nlohmann::json res;
          res["result"] = true;
          res["data"] = sConfigStringList;
          pServer->BuildOkResponse(response, "application/json", res.dump());
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not display ICCP configuration");
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDeleteTase2Domain(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWTASE2Client* pTase2Client = GTWTASE2Client::getTASE2connection(dto.params[1].c_str());
      if (pTase2Client != nullptr)
      {
        size_t index;
        std::string sDomainName = dto.params[2].c_str();
        std::string sDomainFullName = dto.params[2].c_str();
        if ((index = sDomainFullName.find(" (BTID=")) >= 0)
          sDomainName = sDomainFullName.substr(0, index);

        GTWTASE2DomainEditor tase2DomainEditor(dto, pTase2Client, sDomainName, false);
        if (tase2DomainEditor.DeleteObject(nullptr))
        {
          CStdString dsJsonSource = "";
          GTWTASE2ClientEditor clientEditorTase(dto, pTase2Client, true);
          dsJsonSource = clientEditorTase.GetDomainsJson(pTase2Client);
          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDeleteTase2DataAttribute(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "" && dto.params[3] != "")
    {
      GTWTASE2Client* pTase2Client = GTWTASE2Client::getTASE2connection(dto.params[1].c_str());
      if (pTase2Client != nullptr)
      {
        size_t index;
        std::string sDomainName = dto.params[2].c_str();
        std::string sDomainFullName = dto.params[2].c_str();
        if ((index = sDomainFullName.find(" (BTID=")) >= 0)
          sDomainName = sDomainFullName.substr(0, index);

        GTWTASE2DataAttributeEditor tase2DataAttributeEditor(dto, pTase2Client, sDomainName, true);
        if (tase2DataAttributeEditor.DeleteObject(dto.params[3].c_str()))
        {
          CStdString dsJsonSource = "";
          GTWTASE2ClientEditor clientEditorTase(dto, pTase2Client, true);
          dsJsonSource = clientEditorTase.GetDataPointsJson(sDomainName);
          if (dsJsonSource != "")
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = dsJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionOnChangeDataTypeTase2(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] !="")
    {
      GTWTASE2DataAttributeEditor tase2DataAttributeEditor(dto, nullptr, "", true);
      nlohmann::json res;
      res["result"] = true;
      res["data"] = tase2DataAttributeEditor.ChangeDataType(dto.params[1].c_str());
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionSwitchToRChannel(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWChannelEditor chanEditor(dto, pCollectionMember, false, GTWTYPES_COLLECTION_KIND_to_enum_from_hr(dto.objectCollectionKind.c_str()));
        nlohmann::json res;
        res["result"] = true;
        res["data"] = chanEditor.MiscCommand(MENU_CMD_SWITCH_TO_RCHANNEL);
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAdd61850MultipleDAItems(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock* p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock*>(pCollectionMember);

      if (p61850ControlBlock != nullptr && dto.params[2] != "")
      {
        GTW61850DataAttributeMDOEditor dataAttributeMDOEditor61850(dto, nullptr, nullptr, true);
        if (dataAttributeMDOEditor61850.AddObject(p61850ControlBlock, dto.params[2], dto.params[3]))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionAdd61850MultipleWPItems(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock* p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock*>(pCollectionMember);

      if (p61850ControlBlock != nullptr && dto.params[2] != "")
      {
        GTW61850WriteablePointEditor writeablePointEditor(dto, nullptr, nullptr, nullptr, true);
        if (writeablePointEditor.AddObject(p61850ControlBlock, dto.params[2]))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionAdd61850CommandPointItem(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock* p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock*>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850CommandPointEditor commandPointEditor61850(dto, nullptr, p61850ControlBlock->Get61850Client(), p61850ControlBlock, true);
        if (commandPointEditor61850.AddObject(dto.params[3], dto.params[4], dto.params[5]))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          if (dto.params[2] != "")
          {
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[2].c_str(), isUsedForMdos);
          }
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

#if USE_OPC_UA
  static void ActionRefreshOPCUAServerList(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUaClientEditor opcUAClientEditor(dto, nullptr, true);
    std::string opcUAServerList = opcUAClientEditor.RefreshOPCServerList(dto.params[1]);
    if (opcUAServerList != "")
    {
      nlohmann::json res;
      res["result"] = true;
      res["data"] = opcUAServerList;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }

    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionLoadChildrenOPCUAClientItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient *pOPCUaClient = dynamic_cast<GTWOpcUaClient*>(pCollectionMember);
        if (pOPCUaClient != nullptr)
        {
          nlohmann::json opcUAChildItemJsonSource;
          pOPCUaClient->BrowseNode(dto, opcUAChildItemJsonSource);
          if (opcUAChildItemJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcUAChildItemJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  
  static void ActionRefreshOPCUAItemParent(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    assert(false);

    // This is never called. It was in old implementation and was commmented out in editor. All of the references to this should be removed at some point.


    /*
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient *pOPCUaClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcUaClient"))
          pOPCUaClient = dynamic_cast<GTWOpcUaClient *>(pCollectionMember);

        if (pOPCUaClient != nullptr)
        {
          GTWOPCUaClientItemEditor opcUAClientItemEditor(dto, pOPCUaClient, nullptr, true, true);
         
          
          
          assert(false);
          // UA:TODO
          nlohmann::json opcUATreeJsonSource;// = opcUAClientItemEditor.BuildJsonTree();





          if (opcUATreeJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcUATreeJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
    */
  }
  

  static void ActionValidateOPCUAItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient *pOPCUaClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcUaClient"))
          pOPCUaClient = dynamic_cast<GTWOpcUaClient *>(pCollectionMember);

        if (pOPCUaClient != nullptr)
        {
          GTWOPCUaClientItemEditor *pOpcUAClientItemEditor = pOPCUaClient->GetClientItemEditor();
          nlohmann::json opcUAItemInfoJsonSource;
          CStdString sError;
          bool bUnsupportedType = false;
          pOpcUAClientItemEditor->GetJson(dto.params[2], opcUAItemInfoJsonSource, sError, bUnsupportedType);
          if (opcUAItemInfoJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcUAItemInfoJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  static void ActionAddOPCUAMultipleItems(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      std::string clientName = dto.params[1];
      std::string checkedNodes = dto.params[2];

      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(clientName, dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient* pOPCUaClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcUaClient"))
          pOPCUaClient = dynamic_cast<GTWOpcUaClient*>(pCollectionMember);

        if (pOPCUaClient != nullptr)
        {
          if (pOPCUaClient->GetGTWOPCUaClientItemEditor()->AddOPCUAMultipleItems(checkedNodes) == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  void DoFilePost(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {

    TEST_AUTH(response, request);

    try {
      auto query = request->query_string;
      // store query key/value pairs in a map
      std::map<std::string, std::string> params;
      pServer->ExtractParams(query, params);
      auto workspaceName = params["workspaceName"];
      auto fileType = params["fileType"];
      auto schemaName = params["schemaName"];
      if (workspaceName.find("..") != std::string::npos)
      {
        throw std::invalid_argument("could not upload to: " + workspaceName);
        return;
      }

      SimpleWeb::UploadFile<SOCK_TYP> upload_file;
      if (upload_file.parse_upload_request(request) == 0)
      {
        if (upload_file.filename.find("\\") != std::string::npos)
        {
          throw std::invalid_argument("could not upload file: " + upload_file.filename);
          return;
        }

        if (upload_file.filename.find("/") != std::string::npos)
        {
          throw std::invalid_argument("could not upload file: " + upload_file.filename);
          return;
        }

        std::filesystem::path myFile;
        if (schemaName != "")
        {
          TMWParam* pParam = GTWConfig::GetParam(schemaName);
          if (pParam == nullptr)
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }
          myFile = std::filesystem::path(pParam->GetPathToFile().c_str());
        }

        if (myFile != "")
        {
          myFile = myFile.string() + "/" + upload_file.filename;
        }
        else if (fileType == "OPCUATrustedCertificates")
        {
          myFile = GtwSysConfig::getOPCUAServerTrustedCertificatePath() + "/" + upload_file.filename;
        }
        else if (workspaceName != "" && pServer->isValidWorkspaceName(workspaceName))
        {
          myFile = GtwSysConfig::getWorkSpacesPath() + "/" + workspaceName + "/" + upload_file.filename;
        }
        else if (workspaceName == "")
        {
          myFile = GtwSysConfig::getCurrentWorkSpacePath() + "/" + upload_file.filename;
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "failed to save file content invalid workspace");
          return;
        }

        std::string current_extension = myFile.extension().string();
        if (!ShouldSaveFile(myFile.string()))
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "can't upload file with executable extension, or to a location other than workspaces");
          return;
        }
        //GTW_PATH_RESTRICTION_WS_DIR needed for uploading file to a different workspace from the settings GUI
        if (upload_file.save_content_to_file(myFile.string(), GTW_PATH_RESTRICTION_WS_DIR) == false)
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "failed to save file content: " + myFile.string());
          return;
        }

        std::string fileNameDescp = (std::string)upload_file.filename;
        if (current_extension != "")
        {
          std::string descp;
          if (findCaseInsensitive(current_extension, "pem") != std::string::npos)
          {
            GetPEMCertDescp(myFile.string(), descp);
            fileNameDescp = upload_file.filename + " (" + descp + ")";
          }
          else if (findCaseInsensitive(current_extension, "der") != std::string::npos)
          {
            GetDERCertDescp(myFile.string(), descp);
            fileNameDescp = upload_file.filename + " (" + descp + ")";
          }
        }

        nlohmann::json res;
        res["result"] = true;
        res["fileDescription"] = fileNameDescp;
        pServer->BuildOkResponse(response, "application/json", res.dump());

        //*response << "HTTP/1.1 200 OK\r\nContent-Length: " << uploadRet.length() << "\r\n\r\n" << uploadRet;
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", upload_file.error_message);
        //*response << "HTTP/1.1 200 OK\r\nContent-Length: " << upload_file.error_message.length() << "\r\n\r\n" << upload_file.error_message;
      }
    }
    catch (std::exception& e) {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", e.what());
      //*response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n" << e.what();
    }
  }

  void DoFileGet(HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    TEST_AUTH(response, request);

    try
    {
      auto query = request->query_string;
      // store query key/value pairs in a map
      std::map<std::string, std::string> params;
      pServer->ExtractParams(query, params);
      auto fileName = params["fileName"];
      auto fileType = params["fileType"];
      auto schemaName = params["schemaName"];
      if (fileName.find("..") != std::string::npos)
      {
        throw std::invalid_argument("could not read file: " + fileName);
        return;
      }

      std::string myfilePath;
      if (schemaName != "")
      {
        TMWParam* pParam = GTWConfig::GetParam(schemaName);
        if (pParam == nullptr)
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
        myfilePath = pParam->GetPathToFile();
      }

      if (myfilePath != "")
      {
        myfilePath = myfilePath + "/" + fileName;
      }
      else if (fileType == "SDGLogs")
      {
        myfilePath = GtwSysConfig::getSDGLogPath() + "/" + fileName;
      }
      else if (fileType == "LicenseLogs")
      {
        myfilePath = GtwSysConfig::getLicenseLogPath() + "/" + fileName;
      }
      else if (fileType == "SOELogs")
      {
        myfilePath = GtwSysConfig::getSOELogPath() + "/" + fileName;
      }
      else if (fileType == "CurrentWorkspace")
      {
        //Replace '/' by '_' for 61850 export points/mapping 
        std::replace(fileName.begin(), fileName.end(), '/', '_');
        myfilePath = GtwSysConfig::getCurrentWorkSpacePath() + "/" + fileName;
      }
      else if (fileType == "Workspaces")
      {
        myfilePath = GtwSysConfig::getWorkSpacesPath() + "/" + fileName;
      }
      else if (fileType == "OPCUATrustedCertificates")
      {
        myfilePath = GtwSysConfig::getOPCUAServerTrustedCertificatePath() + "/" + fileName;
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "invalid fileType");
        return;
      }

      pServer->RetrieveFile(response, myfilePath);
    }
    catch (const std::exception&) {
      string content = "Could not retrieve ini file " + request->path;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", content);
      //*response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
    }
  }

  static void ActionReadOPCUAMDO(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        if (pCollectionMember->IsA("GTWOpcUaMasterDataObject"))
        {
          GTWOpcUaMasterDataObject* pOPCUaMdo = nullptr;
          pOPCUaMdo = dynamic_cast<GTWOpcUaMasterDataObject*>(pCollectionMember);
          if (pOPCUaMdo != nullptr)
          {
            GTWOpcUaClient* pOpcUaClient = pOPCUaMdo->getClient();
            GTWOPCUaClientItemEditor opcUAClientItemEditor(dto, pOpcUaClient, pOPCUaMdo, false, false);
            if (opcUAClientItemEditor.ReadMDO() == TMWDEFS_TRUE)
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString refreshUIParameter = dto.params[1];
              size_t pos = refreshUIParameter.find_last_of(".");
              if (pos != std::string::npos)
                refreshUIParameter.erase(pos);

              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), refreshUIParameter, "MDO");
              return;
            }
          }
        }
#if USE_OPC_UA_ARRAYS
        else if (pCollectionMember->IsA("GTWOpcUaMDOArrayIndex"))
        {
          GTWOpcUaMDOArrayIndex* pOPCUaMdo = nullptr;
          pOPCUaMdo = dynamic_cast<GTWOpcUaMDOArrayIndex*>(pCollectionMember);

          if (pOPCUaMdo != nullptr)
          {
            GTWOpcUaClient* pOpcUaClient = pOPCUaMdo->getClient();
            GTWOPCUaClientItemEditor opcUAClientItemEditor(dto, pOpcUaClient, pOPCUaMdo, false, false);
            if (opcUAClientItemEditor.ReadMDO() == TMWDEFS_TRUE)
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString refreshUIParameter = dto.params[1];
              size_t pos = refreshUIParameter.find_last_of(".");
              if (pos != std::string::npos)
                refreshUIParameter.erase(pos);

              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), refreshUIParameter, "MDO");
              return;
            }
          }
        }
#endif
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  static void ActionAddOPCUAItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "" && dto.params[3] != "" && dto.params[4] != "" && dto.params[5] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient *pOPCUaClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcUaClient"))
          pOPCUaClient = dynamic_cast<GTWOpcUaClient *>(pCollectionMember);

        if (pOPCUaClient != nullptr)
        {
          GTWOPCUaClientItemEditor *pOpcUAClientItemEditor = pOPCUaClient->GetClientItemEditor();
          if (pOpcUAClientItemEditor->AddOPCUAItem(dto.params[2], dto.params[4], dto.params[3], dto.params[5]) == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  static void ActionDisconnectConnectFromOPCUAServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOpcUaClient *pOPCUaClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcUaClient"))
          pOPCUaClient = dynamic_cast<GTWOpcUaClient *>(pCollectionMember);

        if (pOPCUaClient != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            pOPCUaClient->DisconnectClient();
          }
          else if (dto.params[2] == "Connect")
          {
            pOPCUaClient->ConnectClient();
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAddDeleteOPCUaUser(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if ((dto.params[2] == "ADD" && dto.params[3] != "" && dto.params[4] != "") || (dto.params[2] == "DELETE" && dto.params[3] != "") || (dto.params[2] == "MODIFY" && dto.params[3] != "" && dto.params[4] != ""))
    {
      GTWOPCUAUserSecurityEditor OPCUAUserSecurityEditor(dto, nullptr, false);
      std::string OpcUaClientUserList = OPCUAUserSecurityEditor.AddDeleteOPCUaUser(dto.params[2], dto.params[3], dto.params[4]);
      if (OpcUaClientUserList != "")
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = OpcUaClientUserList;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  static void ActionGetOPCUaServerStatus(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUAServerEditor OPCUAServerEditor(dto, nullptr, false);
    std::string sServerStatus = OPCUAServerEditor.GetServerStatus();
    if (sServerStatus != "")
    {
      nlohmann::json res;
      res["result"] = true;
      res["data"] = sServerStatus;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  static void ActionTrustOPCUaClientCertificate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWOPCUaClientEditor OPCUAClientEditor(dto, nullptr, false);
      std::string rejectedCertList = "";
      if (OPCUAClientEditor.TrustRejectedCert(dto.params[1].c_str(), rejectedCertList) == TMWDEFS_TRUE)
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = rejectedCertList;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  static void ActionTrustOPCUaCertificate(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWOPCUAServerEditor OPCUAServerEditor(dto, nullptr, false);
      std::string rejectedCertList = "";
      if (OPCUAServerEditor.TrustRejectedCert(dto.params[1].c_str(), rejectedCertList) == TMWDEFS_TRUE)
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = rejectedCertList;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif

#if USE_OPC_44
  static void ActionRefreshOPCServerList(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.objectName == "GTWOPCClientEditor")
    {
      GTWOPCClientEditor opcClientEditor(dto, nullptr, true);
      std::string opcServerList = opcClientEditor.RefreshOPCServerList(dto.params[1], dto.params[2]);
      if (opcServerList != "")
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = opcServerList;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    else if (dto.objectName == "GTWOPCAEClientEditor")
    {
      GTWOPCAEClientEditor opcAEClientEditor(dto, nullptr, true);
      std::string opcServerList = opcAEClientEditor.RefreshOPCServerList(dto.params[1]);
      if (opcServerList != "")
      {
        nlohmann::json res;
        res["result"] = true;
        res["data"] = opcServerList;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRefreshAreaApace(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCAEClient *pOPCAEClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCAEClient"))
          pOPCAEClient = dynamic_cast<GTWOPCAEClient *>(pCollectionMember);

        if (pOPCAEClient != nullptr)
        {
          GTWOPCAEClientItemEditor opcAEClientItemEditor(dto, pOPCAEClient, nullptr, true, true);
          nlohmann::json opcAETreeJsonSource = opcAEClientItemEditor.BuildJsonTree();
          if (opcAETreeJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcAETreeJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionLoadChildrenOPCClientItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient"))
          pOPCClient = dynamic_cast<GTWOPCClient *>(pCollectionMember);

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, nullptr, true, false);
          nlohmann::json opcChildItemJsonSource = opcClientItemEditor.BuildJsonBranchLeafTree(dto.params[2]);
          if (opcChildItemJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcChildItemJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRefreshOPCItemParent(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient"))
          pOPCClient = dynamic_cast<GTWOPCClient *>(pCollectionMember);

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, nullptr, true, true);
          nlohmann::json opcTreeJsonSource = opcClientItemEditor.BuildJsonTree();
          if (opcTreeJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcTreeJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRefreshOPCAEAreaApace(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCAEClient *pOPCAEClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCAEClient"))
          pOPCAEClient = dynamic_cast<GTWOPCAEClient *>(pCollectionMember);

        if (pOPCAEClient != nullptr)
        {
          GTWOPCAEClientItemEditor opcAEClientItemEditor(dto, pOPCAEClient, nullptr, true, true);
          nlohmann::json opcAETreeJsonSource = opcAEClientItemEditor.BuildJsonTree();
          if (opcAETreeJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcAETreeJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionLoadChildrenOPCAEAreaApace(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCAEClient *pOPCAEClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCAEClient"))
          pOPCAEClient = dynamic_cast<GTWOPCAEClient *>(pCollectionMember);

        if (pOPCAEClient != nullptr)
        {
          GTWOPCAEClientItemEditor opcAEClientItemEditor(dto, pOPCAEClient, nullptr, true, false);
          nlohmann::json opcAEChildItemJsonSource = opcAEClientItemEditor.BuildJsonBranchLeafTree(dto.params[2]);
          if (opcAEChildItemJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcAEChildItemJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionValidateOPCItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient"))
          pOPCClient = dynamic_cast<GTWOPCClient *>(pCollectionMember);

        if (pOPCClient != nullptr)
        {
          nlohmann::json res;
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, nullptr, true, false);
          if (opcClientItemEditor.ValidateItem(dto.params[2]))
          {
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
          else
          {
            opcClientItemEditor.SetTreeMapToClientEditor(true);
            if (opcClientItemEditor.ValidateItem(dto.params[2]))
            {
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
            else
            {
              res["result"] = false;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }

        GTWOPCAEClient* pOPCAEClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCAEClient"))
          pOPCAEClient = dynamic_cast<GTWOPCAEClient*>(pCollectionMember);

        if (pOPCAEClient != nullptr)
        {
          nlohmann::json res;
          GTWOPCAEClientItemEditor opcClientAEItemEditor(dto, pOPCAEClient, nullptr, true, false);
          if (opcClientAEItemEditor.ValidateItem(dto.params[2]))
          {
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
          else
          {
            res["result"] = false;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAddOPCItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "" && dto.params[3] != "" && dto.params[4] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      std::string clientName = dto.params[1];
      std::string itemName = dto.params[2];
      std::string varTypeName = dto.params[3];
      std::string id = dto.params[4];

      bool status = GetGTWApp()->deepFindMember(clientName, dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient"))
        {
          pOPCClient = (GTWOPCClient*)pCollectionMember;
        }
        else if (pCollectionMember->IsA("GTWOpcMasterDataObject"))
        {
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();
          itemName = ((GTWOpcMasterDataObject*)pCollectionMember)->GetItemName();
        }

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, pCollectionMember, true, false);
          GTWOpcMasterDataObject* ppMdo = nullptr;
          bool bConnect = true;
          if (opcClientItemEditor.AddOPCItem(itemName, varTypeName, id, &ppMdo, bConnect ) == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //CStdString refreshUIParameter = dto.params[1] + "." + itemName;
            //size_t pos = refreshUIParameter.find_last_of(".");
            //if (pos != std::string::npos)
            //  refreshUIParameter.erase(pos);

            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), clientName.c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionAddOPCMultipleItems(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      std::string clientName = dto.params[1];
      std::string checkedNodes = dto.params[2];

      bool status = GetGTWApp()->deepFindMember(clientName, dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient* pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient")) {
          pOPCClient = (GTWOPCClient*)pCollectionMember;
        }
        else if (pCollectionMember->IsA("GTWOpcMasterDataObject")) {
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();
        }

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, nullptr, true, false);

          if (opcClientItemEditor.AddOPCMultipleItems(checkedNodes) == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), clientName.c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionRefreshOPCDAProperties(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcMasterDataObject"))
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, nullptr, true, false);
          nlohmann::json opcDAPropertiesJsonSource = opcClientItemEditor.RefreshDAProperties(dto.params[1]);
          if (opcDAPropertiesJsonSource.size() != 0)
          {
            nlohmann::json res;
            res["result"] = true;
            res["data"] = opcDAPropertiesJsonSource;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionActivateOPCItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcMasterDataObject"))
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, ((GTWOpcMasterDataObject*)pCollectionMember), true, false);
          if (opcClientItemEditor.ActivateDAItem(dto.params[2]) == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionReadOPCItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcMasterDataObject"))
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();

        if (pOPCClient != nullptr)
        {
          if (pOPCClient->Readitem((GTWOpcMasterDataObject*)pCollectionMember))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDisconnectConnectFromOPCServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient *pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCClient"))
          pOPCClient = ((GTWOPCClient*)pCollectionMember);

        if (pOPCClient != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            pOPCClient->GetActiveControl()->SetValue(false);
          }
          else if (dto.params[2] == "Connect")
          {
            pOPCClient->GetActiveControl()->SetValue(true);
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDisconnectConnectFromOPCAEServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "" && dto.params[2] != "")
    {
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCAEClient *pOPCAEClient = nullptr;
        if (pCollectionMember->IsA("GTWOPCAEClient"))
          pOPCAEClient = ((GTWOPCAEClient*)pCollectionMember);

        if (pOPCAEClient != nullptr)
        {
          if (dto.params[2] == "Disconnect")
          {
            pOPCAEClient->GetActiveControl()->SetValue(false);
          }
          else if (dto.params[2] == "Connect")
          {
            pOPCAEClient->GetActiveControl()->SetValue(true);
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  
  static void ActionReadOPCDAMDO(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    if (dto.params[1] != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWOPCClient* pOPCClient = nullptr;
        if (pCollectionMember->IsA("GTWOpcMasterDataObject"))
          pOPCClient = ((GTWOpcMasterDataObject*)pCollectionMember)->GetOPCClient();

        if (pOPCClient != nullptr)
        {
          GTWOPCClientItemEditor opcClientItemEditor(dto, pOPCClient, pCollectionMember, false, false);
          if (opcClientItemEditor.ReadOPCDAMDO() == TMWDEFS_TRUE)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Status_Bar, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_COMMAND_SENT_TO_SERVER", "Command sent to server");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif

protected:
  std::map<MENUENTRY_EDIT_CMD, EditorCommandDispatchItem<SOCK_TYP> *> EditorCommandDispatchMap;
  std::map<MENUENTRY_ACTION_CMD, EditorActionDispatchItem<SOCK_TYP> *>  EditorActionDispatchMap;

  bool DispatchCreateOrUpdateCommand(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    http_audit_record ar = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    auto item = EditorCommandDispatchMap.find(dto.cmdEnum);
    if (item != EditorCommandDispatchMap.end())
    {
      EditorCommandDispatchItem<SOCK_TYP> *pItem = item->second;
      if (pItem != nullptr)
      {
        if (pItem->pCreateOrUpdateMethod == NoOp)
        {
          return false;
        }
        pItem->pCreateOrUpdateMethod(dto, pServer, response, request);
        
          ar.change(dto.toJsonString(true), dto.toJsonString(false));
          http_audit_db::add_audit_record(ar);

        GTWmain::SetIniCsvDirty(true);
        return true;
      }
    }

    // Command failed - cleanup any orphaned channels with UI refresh (don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);
    return false;
  }

  bool DispatchGetDataCommand(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    http_audit_record ar = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    auto item = EditorCommandDispatchMap.find(dto.cmdEnum);
    if (item != EditorCommandDispatchMap.end())
    {
      EditorCommandDispatchItem<SOCK_TYP> *pItem = item->second;
      if (pItem != nullptr)
      {
        if (pItem->pGetDataMethod == NoOp)
        {
          return false;
        }
        pItem->pGetDataMethod(dto, pServer, response, request);
        
        // in case we want to log GET DATA
        //ar.append(dto.toJsonString(false));
        //HttpServerCommon::audit_db.add_audit_record(ar);
        
        return true;
      }
    }
    return false;
  }

  bool DispatchActionCommand(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    std::string token = pServer->GetToken(request);
    std::string userName;
    std::string userRole;
    USER_PERMISSION_MASK userPermissions;
    http_audit_record ar = pServer->IsTokenValidRequest(request, token, userName, userPermissions, userRole);

    auto item = EditorActionDispatchMap.find(dto.actionEnum);
    if (item != EditorActionDispatchMap.end())
    {
      EditorActionDispatchItem<SOCK_TYP> *pItem = item->second;
      if (pItem != nullptr)
      {
        if (pItem->pActionMethod == NoOp)
        {
          return false;
        }
        pItem->pActionMethod(dto, pServer, response, request);
        ar.append(dto.toJsonString(false));
        http_audit_db::add_audit_record(ar);
        return true;
      }
    }
    return false;
  }

  bool CheckRoleCommandAction(USER_ROLE_ENUM userRole, MENUENTRY_ACTION_CMD actionCommand)
  {
    if (userRole == OPERATOR_ROLE &&
      !(
        actionCommand == Read61850Tag
        || actionCommand == ReadICCPTag
        || actionCommand == OperateTase2Control
        || actionCommand == DisconnectConnectFrom61850Server
        || actionCommand == Restart61850Server
        || actionCommand == Stop61850Server
        || actionCommand == PerformWriteAction
        || actionCommand == DisconnectConnectFromTase2Server
        || actionCommand == DisconnectRestartTase2Server
        || actionCommand == ValidateOPCItem
        || actionCommand == ReadOPCItem
        || actionCommand == DisconnectConnectFromOPCServer
        || actionCommand == DisconnectConnectFromOPCAEServer
        || actionCommand == DisconnectConnectFromOPCUAServer
        || actionCommand == EnableDisableRCB
        || actionCommand == EnableDisableDSTS
        || actionCommand == VerifyDataset
        || actionCommand == Reset61850RetryConnectCount
        || actionCommand == ResetAverageMdoUpdateRate
        || actionCommand == GetOPCUaServerStatus))
      return true;
    else
      return false;
  }

  bool CheckRoleCommandEdit(USER_ROLE_ENUM userRole, MENUENTRY_EDIT_CMD menuCommand)
  {
    if (userRole == OPERATOR_ROLE &&
      !(menuCommand == MENU_CMD_RESTART_TASE2_SERVER
        || menuCommand == MENU_CMD_SHOW_CONFIG_TASE2_SERVER
        || menuCommand == MENU_CMD_SHOW_CONFIG_TASE2_CLIENT
        || menuCommand == MENU_CMD_DISCONNECT_TASE2_SERVER
        || menuCommand == MENU_CMD_RESTART_61850_SERVER
        || menuCommand == MENU_CMD_STOP_61850_SERVER
        || menuCommand == MENU_CMD_ENABLE_RCB
        || menuCommand == MENU_CMD_DISABLE_RCB
        || menuCommand == MENU_CMD_VERIFY_DATASET
        || menuCommand == MENU_CMD_READ_DATASET
        || menuCommand == MENU_CMD_READ_OPC_UA_MDO
        || menuCommand == MENU_CMD_CONNECT_TO_61850_SERVER
        || menuCommand == MENU_CMD_DISCONNECT_FROM_61850_SERVER
        || menuCommand == MENU_CMD_OPERATE_TASE2_CONTROL
        || menuCommand == MENU_CMD_CONNECT_TO_TASE2_SERVER
        || menuCommand == MENU_CMD_CONNECT_TO_DISCOVER_TASE2_SERVER
        || menuCommand == MENU_CMD_DISCONNECT_FROM_TASE2_SERVER
        || menuCommand == MENU_CMD_CHANGE_VALUE
        || menuCommand == MENU_CMD_READ_DNP_PROTO
        || menuCommand == MENU_CMD_WRITE_DNP_PROTO
        || menuCommand == MENU_CMD_READ_DNP_DESCP
        || menuCommand == MENU_CMD_WRITE_DNP_DESCP
        || menuCommand == MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT
        || menuCommand == MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE
        || menuCommand == MENU_CMD_READ_61850_MDO
        || menuCommand == MENU_CMD_READ_ICCP_MDO
        || menuCommand == MENU_CMD_PERFORM_WRITE_ACTION
        || menuCommand == MENU_CMD_WRITE_MULTI_POINT
        || menuCommand == MENU_CMD_SUBSCRIBE_GOOSE_STREAM
        || menuCommand == MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM
        || menuCommand == MENU_CMD_SELECT_DATA_ATTRIBUTE
        || menuCommand == MENU_CMD_SET_VALUE_AND_QUALITY
        || menuCommand == MENU_CMD_READ_OPC_UA_MDO
        || menuCommand == MENU_CMD_CONNECT_OPC_SERVER
        || menuCommand == MENU_CMD_DISCONNECT_OPC_SERVER
        || menuCommand == MENU_CMD_CONNECT_OPC_AE_SERVER
        || menuCommand == MENU_CMD_DISCONNECT_OPC_AE_SERVER
        || menuCommand == MENU_CMD_CONNECT_OPC_UA_SERVER
        || menuCommand == MENU_CMD_DISCONNECT_OPC_UA_SERVER
        || menuCommand == MENU_CMD_OPC_UA_GET_SERVER_STATUS
        || menuCommand == MENU_CMD_SWITCH_TO_RCHANNEL
        || menuCommand == MENU_CMD_READ_OPC_DA_MDO
        ))
      return true;
    else
      return false;
  }

  std::string BuildEditorContextMenuResponse(GTWCollectionMember *pClctnMember, std::string objectClassName, std::string objectCollectionKind, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWBaseDataObject *pBdo = dynamic_cast<GTWBaseDataObject *>(pClctnMember);

    EditorCommandDTO dto(MENU_CMD_NONE);
    dto.token = pServer->GetToken(request);
    //dto.objectName = pt["objectName"].get<std::string>();
    //dto.parentObjectName = pt["parentObjectName"].get<std::string>();
    //dto.objectDataJson = pt["objectDataJson"].get<std::string>();
    //dto.pt_objectDataJson = nlohmann::json::parse(dto.objectDataJson);
    dto.objectCollectionKind = objectCollectionKind;
    dto.objectClassName = objectClassName;

    USER_PERMISSION_MASK userPermissions;
    std::string userRole;
    std::string userName;
    http_audit_record audit_record = pServer->IsTokenValidRequest(request, dto.token, userName, userPermissions, userRole);

    //if (objectClassName == "GTWInternalChannel")
    //{
    //  return "";
    //}
    //if (pClctnMember != nullptr && pClctnMember->IsA("GTWInternalChannel"))
    //{
    //  return "";
    //}

    if (pClctnMember != nullptr && pClctnMember->IsA("GTWSession"))
    {
      GTWSession *pSes = dynamic_cast<GTWSession *>(pClctnMember);
      GTWTYPES_PROTOCOL protocol = pSes->GetProtocol();
      if (objectCollectionKind == "MDO" &&
        (
          protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_S101
          || protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_S102
          || protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_S103
          || protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_S104
          || protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_SDNP
          || protocol == GTWTYPES_PROTOCOL::GTWTYPES_PROTOCOL_SMB
          )
        )
      {
        return "";
      }
      //if (pSes->IsInternalSession())
      //{
      //  return "";
      //}
    }

    if (pClctnMember != nullptr && pClctnMember->IsA("GTWSector"))
    {
      GTWSector *pSec = dynamic_cast<GTWSector *>(pClctnMember);
      //if (pSec->IsInternalSector())
      //{
      //  return "";
      //}
    }

    CStdString outStr;

    GTWBaseEditor *pEditor = nullptr;

    if (pClctnMember != nullptr)
    {
      pEditor = pClctnMember->GetBaseEditor(dto);
    }
    else if (objectClassName == "GatewayRootNode" || objectClassName == "")
    {
      pEditor = GetGTWApp()->GetRootEditor(dto);
    }

    if (pEditor == nullptr)
    {
      if (pBdo && GTWHttpServerImplCommon::GetBdoCanChangeValue(pBdo) == true)
      {
        CMenuEntryArray menuItems;
        CMenuEntry menuEntry1("Change Value of MDO", "Change Value of MDO", "Change Value of MDO text", nullptr, 1, MENU_CMD_CHANGE_VALUE);
        menuItems.push_back(menuEntry1);
        nlohmann::json array;
        for (std::vector<CMenuEntry>::const_iterator it = menuItems.begin(), end = menuItems.end(); it != end; ++it)
        {
          if (GtwSysConfig::gtwDoAuth() && CheckRoleCommandEdit(USER_ROLE_ENUM_to_enum(userRole.c_str()), it->m_cmd))
            continue;

          nlohmann::json child;
          child["menuText"] = "TR_" + std::string(MENUENTRY_EDIT_CMD_to_string(it->m_cmd));
          child["defaultMenuText"] = it->m_strCaption;
          child["command"] = MENUENTRY_EDIT_CMD_to_string(it->m_cmd);
          array.push_back(child);
        }
        std::string json = array.dump();
        return json;
      }
      else if (pClctnMember != nullptr && pClctnMember->IsEditable() == false)
      {
        return "";
      }
    }

    int id;
    CMenuEntryArray menuItems;
    if (pEditor == nullptr || pEditor->BuildSelPopupMenu(&id, &menuItems) == TMWDEFS_FALSE)
    {
      return "";
    }
    /*
    if (pBdo && GTWHttpServerImplCommon::GetBdoCanChangeValue(pBdo) == true)
    {
      CMenuEntryArray menuItems;
      CMenuEntry menuEntry1("Change Value of MDO", "Change Value of MDO", "Change Value of MDO text", nullptr, 1, MENU_CMD_CHANGE_VALUE);
      menuItems.push_back(menuEntry1);
    }
    */

    nlohmann::json array;
    for (std::vector<CMenuEntry>::const_iterator it = menuItems.begin(), end = menuItems.end(); it != end; ++it)
    {
      if (GtwSysConfig::gtwDoAuth() && CheckRoleCommandEdit(USER_ROLE_ENUM_to_enum(userRole.c_str()), it->m_cmd))
        continue;

      nlohmann::json child;
      child["menuText"] = "TR_" + std::string(MENUENTRY_EDIT_CMD_to_string(it->m_cmd));
      child["defaultMenuText"] = it->m_strCaption;
      child["command"] = MENUENTRY_EDIT_CMD_to_string(it->m_cmd);
      child["isLicensed"] = it->m_isLicensed;
      array.push_back(child);
    }

    std::string json = array.dump();

    return json;
  }


  // editor dispatch functions
protected:

  static void AddInternalMdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWInternalUserMDOEditor intMdoEditor(dto, NULL, NULL, true);
    if (intMdoEditor.UpdateObject())
    {
      if (intMdoEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddUserFolderCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionBase *pCollection = nullptr;
    GTWCollectionMember *pParentMember = nullptr;

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);


    GTWUserDefinedFolderEditor userFolderEditor(dto, pParentMember, NULL, true);
    if (userFolderEditor.UpdateObject())
    {
      if (userFolderEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName == "" ? "root" : pParentMember->GetFullName().c_str(), dto.parentObjectName == "" ? "ALL" : "MDO");
        return;
      }
    }

    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    GTWCollectionBase *pCollection = nullptr;
    GTWCollectionMember *pParentMember = nullptr;

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      pParentMember = nullptr;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }

    GTWCollectionListParent *pCollectionListParent = dynamic_cast<GTWCollectionListParent *>(pParentMember);
    GTWmdoEditor mdoEditor(dto, pCollectionListParent, nullptr, true);
    if (mdoEditor.UpdateObject())
    {
      if (mdoEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        CStdString isUsedForMdos = mdoEditor.GetEditableObject()->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMultipleMdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    GTWCollectionBase* pCollection = nullptr;
    GTWCollectionMember* pParentMember = nullptr;

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      pParentMember = nullptr;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }

    GTWCollectionListParent* pCollectionListParent = dynamic_cast<GTWCollectionListParent*>(pParentMember);
    GTWmdoEditor mdoEditor(dto, pCollectionListParent, nullptr, true);
    if (mdoEditor.UpdateMultipleObject())
    {
      nlohmann::json messages;
      if (mdoEditor.SaveMultipleObject(messages))
      {
        nlohmann::json res;
        res["messages"] = messages;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        if (mdoEditor.GetEditableObject() != NULL)
        {
          CStdString isUsedForMdos = mdoEditor.GetEditableObject()->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
        }
        return;
      }
      else
      {
        nlohmann::json res;
        res["messages"] = messages;
        res["result"] = false;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "application/json", res.dump());
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }
  
  static void EditCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    if (dto.objectClassName == "GatewayRootNode")
    {
      GTWRootEditor rootEditor(dto, nullptr, false);
      if (rootEditor.UpdateObject())
      {
        if (rootEditor.SaveObject())
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
      }
    }
    if (dto.objectClassName == "GTWTase2SlaveDataObject" && dto.objectName != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.objectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2SdoEditor sdoEditor(dto, nullptr, pCollectionMember, false);
        //sdoEditor.setAddMode(false);
        if (sdoEditor.UpdateObject())
        {
          if (sdoEditor.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : ;
            CStdString sParentName = "";
            pCollectionMember->GetParentCollection()->GetPathName(sParentName);
            if (sParentName != "")
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), sParentName, "SDO");
            return;
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }

    if (dto.objectClassName == "GTWTASE2DomainEditor" && dto.objectName != "" && dto.parentObjectName != "")
    {
      GTWTASE2Client* pTase2Client = GTWTASE2Client::getTASE2connection(dto.parentObjectName.c_str());
      if (pTase2Client != nullptr)
      {
        size_t index;
        std::string sDomainName = dto.objectName.c_str();
        std::string sDomainFullName = dto.objectName.c_str();
        if ((index = sDomainFullName.find(" (BTID=")) >= 0)
          sDomainName = sDomainFullName.substr(0, index);

        GTWTASE2DomainEditor tase2DomainEditor(dto, pTase2Client, sDomainName, false);
        if (tase2DomainEditor.UpdateObject())
        {
          if (tase2DomainEditor.SaveObject(false))
          {
            CStdString dsJsonSource = "";
            GTWTASE2ClientEditor clientEditorTase(dto, pTase2Client, true);
            dsJsonSource = clientEditorTase.GetDomainsJson(pTase2Client);
            if (dsJsonSource != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["data"] = dsJsonSource;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
#if USE_OPC_UA
    if (dto.objectClassName == "GTWOPCUAUserSecurityEditor")
    {
      GTWOPCUAUserSecurityEditor OPCUAUserSecurityEditor(dto, nullptr, false);
      if (OPCUAUserSecurityEditor.UpdateObject())
      {
        if (OPCUAUserSecurityEditor.SaveObject())
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
      }
    }
#endif
    GTWCollectionMember* pCollectionMember = nullptr;
    if (dto.objectClassName == "GTWEquationDataObject" && dto.cmdEnum == MENU_CMD_EDIT)
    {
      if (dto.parentObjectName != dto.objectName) // then user is changing equation name, parentObjectName is the original name
      {
        bool deepFindStatus = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
        if (deepFindStatus)
        {
          GTWEquationDataObject* pEq = dynamic_cast<GTWEquationDataObject*>(pCollectionMember);
          assert(pEq);
          if (pEq)
          {
            if (GetGTWApp()->getMdoCollection()->GetCollectionMember(dto.objectName))
            {
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Editor,
                dto.token.c_str(), "TR_OBJECT_ALREADY_EXISTS", "Error: Object with name '{{arg1}}' already exists.", dto.objectName.c_str());
              return;
            }
            pEq->Rename(dto.objectName); // objectName is the new name
            GTWBaseEditor::NotifyOpcNameChange(dto.token.c_str());
          }
        }
      }
    }
    else if (dto.objectName != "" &&
      (dto.objectClassName == "GTWM104RedundantChannel" || dto.objectClassName == "GTWS104RedundantChannel"))
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      // Here use parentObjectName to look up redundant channel
      bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWBaseEditor* pEditor = pCollectionMember->GetBaseEditor(dto);
        if (pEditor->UpdateObject())
        {
          if (pEditor->SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            return;
          }
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
        return;
      }
    }
    else if (dto.objectClassName == "GTWS104RedundantChannel" && dto.objectName != "")
    {
      GTWCollectionMember* pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
      }
    }

    bool deepFindStatus = GetGTWApp()->deepFindMember(dto.objectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (deepFindStatus)
    {
      GTWBaseEditor *pEditor = pCollectionMember->GetBaseEditor(dto);
      if (pEditor == nullptr)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_method_not_allowed, "", "");
        return;
      }
      else
      {
        if (pEditor->UpdateObject())
        {
          if (pEditor->SaveObject())
          {
            if (pCollectionMember->IsA("GTW61850ReportControlBlock"))
            {
              if (!((GTW61850ReportEditor*)pEditor)->WebAddObject())
              {
                pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
                return;
              }
            }
            else if (pCollectionMember->IsA("GTW61850Server"))
            {
              GTW61850ServerEditor serverEditor61850(dto, pCollectionMember, TMWDEFS_FALSE);
              GTW62351SecurityEditor securityEditor62351(dto, serverEditor61850.Get61850ServerIndex(), GTWBaseEditor::PROTOCOL_61850_SERVER, TMWDEFS_FALSE);
              if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
              {
                pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
                return;
              }
            }
            else if (pCollectionMember->IsA("GTW61850Client"))
            {
              GTW61850ClientEditor clientEditor61850(dto, pCollectionMember, TMWDEFS_FALSE);
              GTW62351SecurityEditor securityEditor62351(dto, clientEditor61850.Get61850ClientIndex(), GTWBaseEditor::PROTOCOL_61850_CLIENT, TMWDEFS_FALSE);
              if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
              {
                pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
                return;
              }
            }
            else if (pCollectionMember->IsA("GTWTase2Server"))
            {
              GTWTase2ServerEditor serverEditorTase2(dto, pCollectionMember, TMWDEFS_FALSE);
              GTW62351SecurityEditor securityEditor62351(dto, serverEditorTase2.GetTASE2ServerIndex(), GTWBaseEditor::PROTOCOL_TASE2, TMWDEFS_FALSE);
              if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
              {
                pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
                return;
              }
            }
            else if (pCollectionMember->IsA("GTWTASE2Client"))
            {
              GTWTASE2ClientEditor clientEditorTase2(dto, pCollectionMember, TMWDEFS_FALSE);
              GTW62351SecurityEditor securityEditor62351(dto, clientEditorTase2.GetTASE2Index(), GTWBaseEditor::PROTOCOL_TASE2, TMWDEFS_FALSE);
              if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
              {
                pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
                return;
              }
            }
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //if (dynamic_cast<GTWCollectionListParent*>(pCollectionMember) != nullptr)
            if (pCollectionMember->GetParentMember() != nullptr)
            {
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), pCollectionMember->GetParentMember()->GetFullName(), isUsedForMdos);
            }
            else
            {
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
            }
            return;
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            return;
          }
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update");
        return;
      }
    }
    GTWMasterDataObject *pMdo = nullptr;
    GTWDEFS_STAT status = GetGTWApp()->findMdo(dto.objectName.c_str(), &pMdo);
    if (status == GTWDEFS_STAT_SUCCESS && pMdo != nullptr)
    {
      GTWBaseDataObject *pBdo = pMdo->getBdo();
      GTWCollectionListParent *pParent = pBdo->GetParentMember();
      GTWBaseEditor *pEditor = pBdo->GetBaseEditor(dto);
      assert(pEditor);
      if (pEditor == nullptr)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "no editor");
        return;
      }
      if (pEditor->UpdateObject())
      {
        if (pEditor->SaveObject())
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
          return;
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Could not save");
          return;
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Could not update");
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found");
    return;
  }

  static void AddMappingSdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWCollectionMember *pParentMember = nullptr;
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      pParentMember = nullptr;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }
    if (pParentMember->IsA("GTW61850Server"))
    {
      std::string dropMemberName = dto.pt_objectDataJson["dropMemberName"].get<std::string>();
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dropMemberName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850SdoEditor sdoEditor(dto, pParentMember, pCollectionMember, true);
        if (sdoEditor.UpdateObject())
        {
          if (sdoEditor.AddObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : ;
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "SDO");
            return;
          }
        }
      }
    }
    else if (pParentMember->IsA("GTWTase2Server") || pParentMember->IsA("GTWTase2ServerLogicalDevice") || pParentMember->IsA("GTWTase2VCCFolder"))
    {
      std::string dropMemberName = dto.pt_objectDataJson["dropMemberName"].get<std::string>();
      GTWCollectionMember *pCollectionMember = nullptr;
      bool status = GetGTWApp()->deepFindMember(dropMemberName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2SdoEditor sdoEditor(dto, pParentMember, pCollectionMember, true);
        if (sdoEditor.UpdateObject())
        {
          if (sdoEditor.AddObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            //CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : ;
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "SDO");
            return;
          }
        }
      }
    }
    else
    {
      GTWDataType *pDataType = dynamic_cast<GTWDataType *>(pParentMember);
      GTWSdoMappingEditor sdoMappingEditor(dto, pDataType, nullptr, true);
      if (sdoMappingEditor.UpdateObject())
      {
        if (sdoMappingEditor.SaveObject())
        {
          if (sdoMappingEditor.GetEditableObject() != nullptr)
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = sdoMappingEditor.GetEditableObject()->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMappingSdosCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWCollectionMember *pParentSdo = nullptr;
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentSdo, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentSdo == nullptr)
    {
      bool status = GetGTWApp()->deepFindMember(nodeFullNameCopy, dto.objectCollectionKind, &pParentSdo);
      if (!status || pParentSdo == nullptr)
      {
        pParentSdo = nullptr;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
        return;
      }
    }
    if (!pParentSdo->IsA("GTW61850Server"))
    {
      GTWSdosMappingEditor sdosMappingEditor(dto, pParentSdo, nullptr, true);
      std::string msg;
      if (sdosMappingEditor.UpdateSaveObject(msg))
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_SUCCES_ARG1", "Success: {{arg1}}", msg.c_str());
        return;
      }
      else
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_ERROR_ARG1", "Error: {{arg1}}", msg.c_str());
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMappingMdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);

    if (status || pCollectionMember != nullptr)
    {
      if (pCollectionMember->IsA("GTWTASE2Client"))
      {
        GTWTASE2Client* pTase2Client = nullptr;
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

        if (pTase2Client != nullptr)
        {
          GTWTASE2MappingDataAttributeEditor tase2MappingDataAttributeEditor(dto, pTase2Client, true);
          std::string msg;
          if (tase2MappingDataAttributeEditor.UpdateSaveObject(msg))
          {
            if (tase2MappingDataAttributeEditor.GetEditableObject() != nullptr)
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              if (tase2MappingDataAttributeEditor.GetEditableObject()->GetParentMember() != nullptr)
                GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), tase2MappingDataAttributeEditor.GetEditableObject()->GetParentMember()->GetFullName(), "MDO");
              else
                GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
              return;
            }
          }
          else
          {
            if (msg != "")
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_ERROR_ARG1", "Error: {{arg1}}", msg.c_str());
            else
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_ERROR_MAPPING", "Error: Incorrect Mapping");
          }
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
          return;
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
      }
    }

    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    GTWMdoMappingEditor mdoMappingEditor(dto, nullptr, nullptr, true);
    std::string msg;
    if (mdoMappingEditor.UpdateSaveObject(msg))
    {
      if (mdoMappingEditor.GetEditableObject() != nullptr)
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        if (mdoMappingEditor.GetEditableObject()->GetParentMember() != nullptr)
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), mdoMappingEditor.GetEditableObject()->GetParentMember()->GetFullName(), "MDO");
        else
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    else
    {
      if (msg != "")
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_ERROR_ARG1", "Error: {{arg1}}", msg.c_str());
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMappingMdosCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWCollectionMember *pParentMdo = nullptr;
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMdo, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMdo == nullptr)
    {
      bool status = GetGTWApp()->deepFindMember(nodeFullNameCopy, dto.objectCollectionKind, &pParentMdo);
      if (!status || pParentMdo == nullptr)
      {
        pParentMdo = nullptr;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
        return;
      }
    }
    if (!pParentMdo->IsA("GTW61850Server"))
    {
      GTWMdosMappingEditor mdosMappingEditor(dto, pParentMdo, nullptr, true);
      std::string msg;
      if (mdosMappingEditor.UpdateSaveObject(msg))
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_SUCCES_ARG1", "Success: {{arg1}}", msg.c_str());
        return;
      }
      else
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_ERROR_ARG1", "Error: {{arg1}}", msg.c_str());
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddEquationMdoCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWmdoEquationEditor mdoEquationEditor(dto, nullptr, nullptr, true);
    if (mdoEquationEditor.UpdateObject())
    {
      if (mdoEquationEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOdbcClientCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWODBCClientEditor clientEditorODBC(dto, nullptr, true);
    if (clientEditorODBC.UpdateObject())
    {
      if (clientEditorODBC.SaveObject())
      {
        GTWRootEditor rootEditor(dto, NULL, false);
        if (rootEditor.AddODBCClient(clientEditorODBC.GetClientIndex()))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOdbcItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWODBCClient *pODBCClient = dynamic_cast<GTWODBCClient *>(pCollectionMember);
      if (pODBCClient != nullptr)
      {
        GTWODBCQueryEditor queryEditorODBC(dto, nullptr, pODBCClient, pODBCClient->getObIndex(), true);
        if (queryEditorODBC.UpdateObject())
        {
          if (queryEditorODBC.SaveObject())
          {
            if (queryEditorODBC.AddQuery(pODBCClient, false))
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMasterChannelCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
    if (channelEditor.UpdateObject())
    {
      if (channelEditor.SaveObject())
      {
        if (channelEditor.AddChannel())
        {
          CStdString channelFullName;
          if (channelEditor.GetRedundancyGroupName().length() > 0) // then creating a redundant channel
          {
            channelFullName = channelEditor.GetRedundancyGroupName();
          }
          else
          {
            GTWChannel* pChannel = GetGTWApp()->GetChannel(channelEditor.GetChannelIndex());
            channelFullName = pChannel->GetFullName();
          }

          nlohmann::json res;
          res["result"] = true;
          res["objectParentName"] = channelFullName;
          res["objectClassName"] = "GTWSession";
          res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
          res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_SESSION);
          pServer->BuildOkResponse(response, "application/json", res.dump());
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
          return;
        }
      }
    }

    // Master channel creation failed - cleanup any orphaned channels with UI refresh (don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
  }

  static void AddSlaveRedundantChannelCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);

    if (channelEditor.UpdateObject())
    {
      channelEditor.SetRedundancyGroupName(dto.parentObjectName.c_str());
      if (channelEditor.AddRedundantChannel())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        //GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "SDO");
        return;
      }
    }

    // Slave redundant channel creation failed - cleanup any orphaned channels with UI refresh (don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddMasterRedundantChannelCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);

    if (channelEditor.UpdateObject())
    {
      channelEditor.SetRedundancyGroupName(dto.parentObjectName.c_str());
      if (channelEditor.AddRedundantChannel())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        //GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
        return;
      }
    }

    // Master redundant channel creation failed - cleanup any orphaned channels with UI refresh (don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddSlaveChannelCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);
    if (channelEditor.UpdateObject())
    {
      if (channelEditor.SaveObject())
      {
        if (channelEditor.AddChannel())
        {
          CStdString channelName;
          if (channelEditor.GetRedundancyGroupName().length() > 0) // then creating a redundant channel
          {
            channelName = channelEditor.GetRedundancyGroupName();
          }
          else
          {
            GTWChannel* pChannel = GetGTWApp()->GetChannel(channelEditor.GetChannelIndex());
            channelName = pChannel->GetMemberName();
          }

          nlohmann::json res;
          res["result"] = true;
          res["objectParentName"] = channelName;
          res["objectClassName"] = "GTWSession";
          res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);
          res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_SESSION);
          pServer->BuildOkResponse(response, "application/json", res.dump());
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddSessionCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWChannel *pChannel = dynamic_cast<GTWChannel *>(pCollectionMember);
      if (pChannel != nullptr)
      {
        TMWTYPES_USHORT channelIndex = pChannel->GetChannelIndex();
        GTWTYPES_PROTOCOL protocol = (GTWTYPES_PROTOCOL)GTWConfig::PhysComProtocol(channelIndex);
        GTWSessionEditor sessionEditor(dto, NULL, channelIndex, protocol, TMWDEFS_TRUE);
        if (sessionEditor.UpdateObject())
        {
          if (sessionEditor.SaveObject())
          {
            if (sessionEditor.AddSession())
            {
              GTWSession *pSession = (GTWSession *)sessionEditor.GetEditableObject();
              CStdString sessionFullName = pSession->GetFullName();

              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = sessionFullName;
              res["objectCollectionKind"] = dto.objectCollectionKind;
              if (protocol == GTWTYPES_PROTOCOL_MMB || protocol == GTWTYPES_PROTOCOL_SMB || protocol == GTWTYPES_PROTOCOL_MDNP || protocol == GTWTYPES_PROTOCOL_SDNP)
              {
                res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_DATA_TYPE);
                res["objectClassName"] = "GTWDataType";
              }
              else
              {
                res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_SECTOR);
                res["objectClassName"] = "GTWSector";
              }
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);

              // Clear wizard in progress flag since session was successfully added
              GTWChannel::ClearWizardInProgressFlag(dto.parentObjectName);
              //if (pCollectionMember->GetParentCollection()->IsUsedForMdos() == false)
              //  GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
              return;
            }
          }
        }
      }
    }

    // Session creation failed - cleanup any orphaned channels with UI refresh (don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);

    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddSectorCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = dynamic_cast<GTWSession *>(pCollectionMember);

      if (pSession != nullptr)
      {
        GTWSectorEditor sectorEditor(dto, NULL, pSession, TMWDEFS_TRUE);
        if (sectorEditor.UpdateObject())
        {
          if (sectorEditor.SaveObject())
          {
            if (sectorEditor.AddSector(pSession, false))
            {
              GTWSector *pSector = (GTWSector *)sectorEditor.GetEditableObject();
              CStdString sectorFullName = pSector->GetFullName();

              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = sectorFullName;
              res["objectClassName"] = "GTWDataType";
              res["objectCollectionKind"] = dto.objectCollectionKind;
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_DATA_TYPE);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              //if (pCollectionMember->GetParentCollection()->IsUsedForMdos() == false)
              //  GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddDataTypeCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;
      GTWSector *pSector = nullptr;

      TMWTYPES_USHORT sectorIndex = -1;
      if (pCollectionMember->IsA("GTWSector"))
      {
        pSector = dynamic_cast<GTWSector *>(pCollectionMember);
        if (pSector != nullptr)
        {
          sectorIndex = pSector->GetSectorIndex();
          pSession = pSector->GetSession();
        }
      }
      else if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession*>(pCollectionMember);
      }

      if (pSession != nullptr)
      {
        TMWTYPES_USHORT sessionIndex = pSession->GetSessionIndex();
        GTWdataTypeEditor dataTypeEditor(dto, NULL, sessionIndex, sectorIndex, TMWDEFS_TRUE);

        if (pCollectionMember->IsA("GTWSector"))
        {
          dataTypeEditor.SetSector(pSector);
        }
        else if (pCollectionMember->IsA("GTWSession"))
        {
          dataTypeEditor.SetSession(pSession);
        }

        if (dataTypeEditor.UpdateObject())
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          //if (pCollectionMember->GetParentCollection()->IsUsedForMdos() == false)
          //  GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2ItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ActionAddTase2MultiplePPSItems(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.params[1], dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2ControlBlock* pTASE2ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTWTASE2ControlBlock"))
        pTASE2ControlBlock = dynamic_cast<GTWTASE2ControlBlock*>(pCollectionMember);

      if (pTASE2ControlBlock != nullptr && dto.params[2] != "")
      {
        GTWTASE2DataAttributeMDOEditor tase2DataAttributeMDOEditor(dto, nullptr, nullptr, true);
        if (tase2DataAttributeMDOEditor.AddObject(pTASE2ControlBlock, dto.params[2]))
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2DomainCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pTase2Client = nullptr;
      if (pCollectionMember->IsA("GTWTASE2Client"))
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pTase2Client != nullptr)
      {
        GTWTASE2DomainEditor tase2DomainEditor(dto, pTase2Client, "",  true);
        if (tase2DomainEditor.UpdateObject())
        {
          if (tase2DomainEditor.SaveObject(true))
          {
            CStdString dsJsonSource = "";
            GTWTASE2ClientEditor clientEditorTase(dto, pTase2Client, true);
            dsJsonSource = clientEditorTase.GetDomainsJson(pTase2Client);
            if (dsJsonSource != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["data"] = dsJsonSource;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }
  
  static void AddTase2DataAttributeCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pTase2Client = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      size_t index;
      std::string sDomainName = dto.objectName.c_str();
      std::string sDomainFullName = dto.objectName.c_str();
      if ((index = sDomainFullName.find(" (BTID=")) >= 0)
        sDomainName = sDomainFullName.substr(0, index);

      if (pTase2Client != nullptr && sDomainName != "")
      {
        GTWTASE2DataAttributeEditor tase2DataAttributeEditor(dto, pTase2Client, sDomainName, true);
        if (tase2DataAttributeEditor.UpdateObject())
        {
          if (tase2DataAttributeEditor.SaveObject())
          {
            CStdString dsJsonSource = "";
            GTWTASE2ClientEditor clientEditorTase(dto, pTase2Client, true);
            dsJsonSource = clientEditorTase.GetDataPointsJson(sDomainName);
            if (dsJsonSource != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["data"] = dsJsonSource;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
  }
#if USE_GOOSE_MONITOR
  static void AddGooseMonitorCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWGooseMonitorEditor gooseMonitorEditor(dto, nullptr, true);
    if (gooseMonitorEditor.UpdateObject())
    {
      if (gooseMonitorEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }
#endif
  static void AddWriteActionCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession* pSession = nullptr;

      if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession*>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pSession != nullptr)
      {
        GTWModbusSessionActionEditor modbusSessionActionEditor(dto, pSession, NULL, TMWDEFS_TRUE);
        if (modbusSessionActionEditor.CreateObjectMBWriteAction())
        {
          nlohmann::json res;
          res["result"] = true;
          pServer->BuildOkResponse(response, "application/json", res.dump());
          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2CommandPointCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2ControlBlock *pTASE2ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTWTASE2ControlBlock"))
        pTASE2ControlBlock = dynamic_cast<GTWTASE2ControlBlock *>(pCollectionMember);

      if (pTASE2ControlBlock != nullptr)
      {
        GTWTASE2CommandPointEditor tase2CommandPointEditor(dto, nullptr, pTASE2ControlBlock->GetTASE2Client(), pTASE2ControlBlock, true);
        if (tase2CommandPointEditor.UpdateObject())
        {
          if (tase2CommandPointEditor.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ManageTase2DataSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        GTWTASE2DatasetEditor TASE2DatasetEditor(dto, dto.objectName.c_str(), pClientTASE2, false);
        if (TASE2DatasetEditor.UpdateObject())
        {
          tmwTase2::Tase2DataSet* pDS = TASE2DatasetEditor.ModifyDataSet();
          if (pDS != nullptr)
          {
            tmw::String fullName;
            pDS->GetFullName(fullName);
            if (fullName != NULL && fullName != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["DSName"] = (std::string)fullName;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2DataSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        GTWTASE2DatasetEditor TASE2DatasetEditor(dto, "", pClientTASE2, true);
        if (TASE2DatasetEditor.UpdateObject())
        {
          tmwTase2::Tase2DataSet* pDS = TASE2DatasetEditor.AddDataSet();
          if (pDS != nullptr)
          {
            tmw::String fullName;
            pDS->GetFullName(fullName);
            if (fullName != NULL && fullName != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["DSName"] = (std::string)fullName;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2DstsCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    //GTWCollectionMember* pCollectionMember = nullptr;
    //bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    //if (status && pCollectionMember != nullptr)
    //{
    //  GTWTASE2Client* pClient = nullptr;

    //  if (pCollectionMember->IsA("GTWTASE2Client"))
    //    pClient = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

    //  if (pClient != nullptr)
    //  {
    //    GTWTASE2CltReportedDataSetEditor tase2CltReportedDataSetEditor(dto, nullptr, pClient, true);
    //    if (tase2CltReportedDataSetEditor.UpdateObject())
    //    {
    //      if (tase2CltReportedDataSetEditor.SaveObject())
    //      {
    //        if (tase2CltReportedDataSetEditor.SetReportedDataSet())
    //        {
    //          nlohmann::json res;
    //          res["result"] = true;
    //          pServer->BuildOkResponse(response, "application/json", res.dump());
    //          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
    //          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
    //          return;
    //        }
    //      }
    //    }
    //  }
    //}
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }


  static void AddTase2PolledPointSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTWTASE2PolledPointSetEditor tase2PolledPointSetEditor(dto, nullptr, pClient, true);
        if (tase2PolledPointSetEditor.UpdateObject())
        {
          if (tase2PolledPointSetEditor.SaveObject())
          {
            if (tase2PolledPointSetEditor.AddPolledPointSet())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient->GetAliasName() + "." + tase2PolledPointSetEditor.GetPolledPointSetID();
              res["objectClassName"] = "GTWTASE2DataAttributeMDO";
              res["objectCollectionKind"] = dto.objectCollectionKind;
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_TASE2_ITEM);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2PolledDataSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    //GTWCollectionMember* pCollectionMember = nullptr;
    //bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    //if (status && pCollectionMember != nullptr)
    //{
    //  GTWTASE2Client* pClient = nullptr;

    //  if (pCollectionMember->IsA("GTWTASE2Client"))
    //    pClient = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

    //  if (pClient != nullptr)
    //  {
    //    GTWTASE2PolledDataSetEditor tase2PolledDataSetEditor(dto, nullptr, pClient, true);
    //    if (tase2PolledDataSetEditor.UpdateObject())
    //    {
    //      if (tase2PolledDataSetEditor.SaveObject())
    //      {
    //        if (tase2PolledDataSetEditor.SetPolledDataSet())
    //        {
    //          nlohmann::json res;
    //          res["result"] = true;
    //          pServer->BuildOkResponse(response, "application/json", res.dump());
    //          CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
    //          GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
    //          return;
    //        }
    //      }
    //    }
    //  }
    //}
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddTase2LogicalDeviceCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTase2Server *pTase2Server = nullptr;

      if (pCollectionMember->IsA("GTWTase2Server"))
        pTase2Server = dynamic_cast<GTWTase2Server *>(pCollectionMember);

      if (pServer != nullptr)
      {
        GTWTase2ServerLogicalDeviceEditor tase2ServerLogicalDeviceEditor(dto, nullptr, pTase2Server, true);
        
        if (tase2ServerLogicalDeviceEditor.UpdateObject())
        {
          if (tase2ServerLogicalDeviceEditor.SaveObject())
          {
            if (tase2ServerLogicalDeviceEditor.AddLogicalDeviceWeb())
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  // Called when edit model windows closes (close button pressed)
  static void EditTase2EditModel(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
      {
        pClient = dynamic_cast<GTWTASE2Client*>(pCollectionMember);
      }

      if (pClient != nullptr)
      {
        pClient->SetEditing(false);

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());

        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
  }

  static void EditTase2DataPointsCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkResponse(response, "application/json", res.dump());
    GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
    return;
  }

  static void AddTase2CreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWRootEditor rootEditor(dto, NULL, false);
    GTWTASE2ClientEditor clientEditorTase2(dto, nullptr, true);
    TMWTYPES_USHORT clientIndex = clientEditorTase2.GetTASE2Index();
    GTW62351SecurityEditor securityEditor62351(dto, clientIndex, GTWBaseEditor::PROTOCOL_TASE2, true);
    if (clientEditorTase2.UpdateObject())
    {
      if (clientEditorTase2.SaveObject())
      {
        if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
        if (clientEditorTase2.GetServiceRole() == CLIENT_ROLE)
        {
          if (!rootEditor.AddTASE2Client(clientIndex))
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(), "TR_TASE2_CLIENT_DUP_LIC", "Failed to add TASE2 Client (could be duplicate, or no license)");
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            clientEditorTase2.DeleteINIparms();
            securityEditor62351.DeleteINIparms();
            return;
          }
        }
        else if (clientEditorTase2.GetServiceRole() == SERVER_ROLE)
        {
          if (!rootEditor.AddTase2Server(clientIndex))
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(), "TR_TASE2_SERVER_DUP_LIC", "Failed to add TASE2 Server (could be duplicate, or no license)");
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            clientEditorTase2.DeleteINIparms();
            securityEditor62351.DeleteINIparms();
            return;
          }
        }
        else if (clientEditorTase2.GetServiceRole() == CLIENT_SERVER_ROLE)
        {
          GTWTase2Server *pT2Server = rootEditor.AddTase2Server(clientIndex);
          if (!pT2Server)
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(), "TR_TASE2_SERVER_DUP_LIC", "Failed to add TASE2 Server (could be duplicate, or no license)");
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            clientEditorTase2.DeleteINIparms();
            securityEditor62351.DeleteINIparms();
            return;
          }

          if (!rootEditor.AddTASE2Client(clientIndex, pT2Server->CreatePeerClient()))
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(), "TR_TASE2_CLIENT_DUP_LIC", "Failed to add TASE2 Client (could be duplicate, or no license)");
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            clientEditorTase2.DeleteINIparms();
            securityEditor62351.DeleteINIparms();
            return;
          }
        }

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61400AlarmsNodeCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTW61400AlarmsEditor alarmsEditor61400(dto, nullptr, pClient, true);
        if (alarmsEditor61400.UpdateObject())
        {
          if (alarmsEditor61400.SaveObject())
          {
            if (alarmsEditor61400.AddAlarmsNode(pClient))
            {
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61400AlarmMDOCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61400Alarms *pClient = nullptr;

      if (pCollectionMember->IsA("GTW61400Alarms"))
        pClient = dynamic_cast<GTW61400Alarms *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTW61400AlarmMDOEditor alarmMDOEditor61400(dto, pClient, nullptr, true);
        if (alarmMDOEditor61400.UpdateObject())
        {
          if (alarmMDOEditor61400.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850WritablePointCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;
      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850WriteablePointEditor writablePointEditor61850(dto, nullptr, p61850ControlBlock->Get61850Client(), p61850ControlBlock, true);
        if (writablePointEditor61850.UpdateObject())
        {
          if (writablePointEditor61850.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850WritablePointSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850WritablePointSetEditor writablePointEditor61850(dto, nullptr, pClient61850, true);
        if (writablePointEditor61850.UpdateObject())
        {
          if (writablePointEditor61850.SaveObject())
          {
            if (writablePointEditor61850.CreateWritablePointSet())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + writablePointEditor61850.Get61850WPSName();
              res["objectClassName"] = "GTW61850WritablePointSetEditor";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_WRITABLE_POINT);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }


  static void Add61850CommandPointCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850CommandPointEditor commandPointEditor61850(dto, nullptr, p61850ControlBlock->Get61850Client(), p61850ControlBlock, true);
        if (commandPointEditor61850.UpdateObject())
        {
          if (commandPointEditor61850.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  
  static void AddTase2CommandPointSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClientTase2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTase2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClientTase2 != nullptr)
      {
        GTWTASE2CommandPointSetEditor commandPointEditorTase2(dto, nullptr, pClientTase2, true);
        if (commandPointEditorTase2.UpdateObject())
        {
          if (commandPointEditorTase2.SaveObject())
          {
            if (commandPointEditorTase2.CreateCommandPointSet())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClientTase2->GetAliasName() + "." + commandPointEditorTase2.GetCommandPointSetID();
              res["objectClassName"] = "GTWTASE2CommandPointSet";
              res["objectCollectionKind"] = dto.objectCollectionKind;
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_TASE2_COMMAND_POINT);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850CommandPointSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850CommandPointSetEditor commandPointEditor61850(dto, nullptr, pClient61850, true);
        if (commandPointEditor61850.UpdateObject())
        {
          if (commandPointEditor61850.SaveObject())
          {
            if (commandPointEditor61850.CreateCommandPointSet())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + commandPointEditor61850.Get61850CommandPointSetID();
              res["objectClassName"] = "GTW61850CommandPointSet";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_COMMAND_POINT);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Change61850DataSet(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850ChangeDataSetEditor changeDataSetEditor(dto, nullptr, pClient61850, true);
        if (changeDataSetEditor.UpdateObject())
        {
          nlohmann::json res;
          res["result"] = true;
          res["DSName"] = (std::string)changeDataSetEditor.GetDataSetName();
          pServer->BuildOkResponse(response, "application/json", res.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }


  static void Add61850DataSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850DatasetEditor GTW61850DatasetEditor(dto, nullptr, pClient61850, true);
        if (GTW61850DatasetEditor.UpdateObject())
        {
          tmw61850::DataSet* pDS = GTW61850DatasetEditor.CreateDynamicDataSet();
          if (pDS != nullptr)
          {
            tmw::String fullName;
            pDS->GetFullName(fullName);
            if (fullName != NULL && fullName != "")
            {
              nlohmann::json res;
              res["result"] = true;
              res["DSName"] = (std::string)fullName;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void SelectDataAttributeCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    CStdString sDAName = dto.pt_objectDataJson["DAName"].get<std::string>();

    if (sDAName != "")
    {
      nlohmann::json res;
      res["result"] = true;
      res["DAName"] = sDAName;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850ItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
  }

  static void Add61850PolledPointSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        if (pClient61850->GetClientConnection()->Model()->Children() == NULL)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_INVALID_MODEL", "Error: Invalid 61850 model");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Invalid 61850 model");
          return;
        }
        GTW61850PolledPointSetEditor polledPointSetEditor61850(dto, nullptr, pClient61850, true);

        if (polledPointSetEditor61850.UpdateObject())
        {
          if (polledPointSetEditor61850.SaveObject())
          {
            if (polledPointSetEditor61850.CreatePolledPointSet())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + polledPointSetEditor61850.Get61850PPSName();
              res["objectClassName"] = "GTW61850PolledPointSetEditor";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_ITEM);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850PolledDataSetCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        if (pClient61850->GetClientConnection()->Model()->Children() == NULL)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_INVALID_MODEL", "Error: Invalid 61850 model");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "No Model found");
          return;
        }
        GTW61850PolledDataSetEditor polledDataSetEditor61850(dto, nullptr, pClient61850, true);

        if (polledDataSetEditor61850.UpdateObject())
        {
          if (polledDataSetEditor61850.SaveObject())
          {
            if (polledDataSetEditor61850.WebAddObject())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + polledDataSetEditor61850.Get61850PDSName();
              res["objectClassName"] = "GTW61850PolledDataSetEditor";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_ITEM);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850ReportCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        if (pClient61850->GetClientConnection()->Model()->Children() == NULL)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_INVALID_MODEL", "Error: Invalid 61850 model");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "No model found");
          return;
        }
        GTW61850ReportEditor reportEditor61850(dto, nullptr, pClient61850, true);

        if (reportEditor61850.UpdateObjectAdd())
        {
          if (reportEditor61850.SaveObject())
          {
            if (reportEditor61850.WebAddObject())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + reportEditor61850.Get61850RCBName();
              res["objectClassName"] = "GTW61850ReportEditor";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_ITEM);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);

              if (!reportEditor61850.GetRCB()->IsReportEnabled())
              {
                CStdString sName = reportEditor61850.GetRCB()->GetFullName();
                GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Information, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_FAILED_TO_ENABLE_RCB",
                  "Added RCB '{{arg1}}' successfully but failed to enable the report. See logs for details.", sName.c_str());
              }
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850GooseCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        if (pClient61850->GetClientConnection()->Model()->Children() == NULL)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_INVALID_MODEL", "Error: Invalid 61850 model");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "No model found");
          return;
        }
        GTW61850GOOSEEditor GOOSEEditor61850(dto, nullptr, pClient61850, true);

        if (GOOSEEditor61850.UpdateObjectAdd())
        {
          if (GOOSEEditor61850.SaveObject())
          {
            if (GOOSEEditor61850.WebAddObject())
            {
              nlohmann::json res;
              res["result"] = true;
              res["objectParentName"] = pClient61850->GetAliasName() + "." + GOOSEEditor61850.Get61850GOOSEName();
              res["objectClassName"] = "GTW61850GOOSEEditor";
              res["objectCollectionKind"] = GTWTYPES_COLLECTION_KIND_to_hr(GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
              res["nextEditorCommand"] = MENUENTRY_EDIT_CMD_to_string(MENU_CMD_ADD_61850_ITEM);
              pServer->BuildOkResponse(response, "application/json", res.dump());
              CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos);
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850ClientsFromFileCreate(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWRootEditor rootEditor(dto, NULL, false);
    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);
    clientEditor61850.SetLoadClientsFromFile(true);

    TMWTYPES_USHORT clientIndex = clientEditor61850.Get61850ClientIndex();
    GTW62351SecurityEditor securityEditor62351(dto, clientIndex, GTWBaseEditor::PROTOCOL_61850_CLIENT, true);
    if (clientEditor61850.UpdateObject())
    {
      if (clientEditor61850.SaveObject())
      {
        if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to update/save client");
          return;
        }
        
        if (!clientEditor61850.AddClientsFromFile(dto))
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to add clients");
          //clientEditor61850.DeleteINIparms();
          //securityEditor62351.DeleteINIparms();
          return;
        }
        
        /*
        if (!rootEditor.Add61850Client(clientIndex, true))
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_CLIENT_DUP_LIC", "Failed to add IEC 61850 Client (could be duplicate, or no license)");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          clientEditor61850.DeleteINIparms();
          securityEditor62351.DeleteINIparms();
          return;
        }
        */
        //clientEditor61850.SaveSCLFile(GTW61850Client::get61850Client(GTWConfig::I61850ClientName[clientIndex]));

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850ClientCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWRootEditor rootEditor(dto, NULL, false);
    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);
    TMWTYPES_USHORT clientIndex = clientEditor61850.Get61850ClientIndex();
    GTW62351SecurityEditor securityEditor62351(dto, clientIndex, GTWBaseEditor::PROTOCOL_61850_CLIENT, true);
    if (clientEditor61850.UpdateObject())
    {
      if (clientEditor61850.SaveObject())
      {
        if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to update/save client");
          return;
        }
#if USE_61850_SCD
        if (clientEditor61850.LoadClientsFromFile())
        {
          if (!clientEditor61850.AddClientsFromFile())
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Failed to add clients");
            //clientEditor61850.DeleteINIparms();
            //securityEditor62351.DeleteINIparms();
            return;
          }
        }
        else
#endif
        {
          if (!rootEditor.Add61850Client(clientIndex, true))
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_CLIENT_DUP_LIC", "Failed to add IEC 61850 Client (could be duplicate, or no license)");
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
            clientEditor61850.DeleteINIparms();
            securityEditor62351.DeleteINIparms();
            return;
          }
          clientEditor61850.SaveSCLFile(GTW61850Client::get61850Client(GTWConfig::I61850ClientName[clientIndex]));
        }

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void Add61850ServerCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWRootEditor rootEditor(dto, NULL, false);
    GTW61850ServerEditor serverEditor61850(dto, nullptr, true);
    TMWTYPES_USHORT serverIndex = serverEditor61850.Get61850ServerIndex();
    GTW62351SecurityEditor securityEditor62351(dto, serverIndex, GTWBaseEditor::PROTOCOL_61850_SERVER, true);

    if (serverEditor61850.UpdateObject())
    {
      if (serverEditor61850.SaveObject())
      {
        if (!securityEditor62351.UpdateObject() || !securityEditor62351.SaveObject())
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          return;
        }
        if (!rootEditor.Add61850Server(serverIndex))
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_SERVER_DUP_LIC", "Failed to add IEC 61850 Server (could be duplicate, or no license)");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not save");
          serverEditor61850.DeleteINIparms();
          securityEditor62351.DeleteINIparms();
          return;
        }

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

#if USE_OPC_UA
  static void AddOpcUaCertificateCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUACertificateEditor GTWOPCUACertificateEditor(dto, nullptr, true);
    if (GTWOPCUACertificateEditor.UpdateObject())
    {
      if (GTWOPCUACertificateEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOpcUaClientCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWOPCUaClientEditor GTWOPCUAClientEditor(dto, nullptr, true);
    GTWRootEditor rootEditor(dto, NULL, false);
    if (GTWOPCUAClientEditor.UpdateObject())
    {
      if (GTWOPCUAClientEditor.SaveObject())
      {
        rootEditor.AddOPCUaClient(GTWOPCUAClientEditor.GetOPCUaClientIndex());
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
  }

  static void CloseOpcUaItem(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOpcUaClient* pOPCUaClient = nullptr;
      if (pCollectionMember->IsA("GTWOpcUaClient"))
      { 
        pOPCUaClient = dynamic_cast<GTWOpcUaClient*>(pCollectionMember);
      }
      if (pOPCUaClient != nullptr)
      {
        if (pOPCUaClient->GetGTWOPCUaClientItemEditor() != nullptr)
        {
          pOPCUaClient->GetGTWOPCUaClientItemEditor()->ClearMapTree();
        }
      }
    }
    nlohmann::json res;
    res["result"] = true;
    pServer->BuildOkResponse(response, "application/json", res.dump());
  }

  static void EditOpcUaServerCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWOPCUAServerEditor OPCUaServerEditor(dto, nullptr, false);
    if (OPCUaServerEditor.UpdateObject())
    {
      if (OPCUaServerEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
  }
#endif

#if USE_OPC_44
  static void AddOpcAeClientCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWOPCAEClientEditor GTWOPCAEClientEditor(dto, nullptr, true);
    GTWRootEditor rootEditor(dto, NULL, false);
    if (GTWOPCAEClientEditor.UpdateObject())
    {
      if (GTWOPCAEClientEditor.SaveObject())
      {
        GTWOPCAEClient* pClient = nullptr;
        rootEditor.AddOPCAEClient(GTWOPCAEClientEditor.GetOPCAEClientIndex());
        GTWCollectionMember* pCollectionMember = nullptr;
        bool deepFindStatus = GetGTWApp()->deepFindMember(GTWConfig::OPCAEserverName(GTWOPCAEClientEditor.GetOPCAEClientIndex()), dto.objectCollectionKind, &pCollectionMember);
        if (deepFindStatus == true && pCollectionMember != nullptr)
        {
          GTWOPCAEClient* pClient = (GTWOPCAEClient*)pCollectionMember;
          pClient->GetActiveControl()->SetValue(true);
        }

        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOpcClientCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);
    GTWOPCClientEditor OPCClientEditor(dto, nullptr, true);
    if (OPCClientEditor.UpdateObject())
    {
      if (OPCClientEditor.SaveObject())
      {
        nlohmann::json res;
        res["result"] = true;
        pServer->BuildOkResponse(response, "application/json", res.dump());
        GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), "root", "ALL");
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOpcItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    // FVE should we do this?????
    //nlohmann::json res;
    //res["result"] = true;
    //pServer->BuildOkResponse(response, "application/json", res.dump());
    return;
  }

  static void AddOpcAeItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName, dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOPCAEClient *pOPCAEClient = nullptr;
      if (pCollectionMember->IsA("GTWOPCAEClient"))
        pOPCAEClient = dynamic_cast<GTWOPCAEClient *>(pCollectionMember);

      if (pOPCAEClient != nullptr)
      {
        GTWOPCAEClientItemEditor opcAEClientItemEditor(dto, pOPCAEClient, nullptr, false, false);
        if (opcAEClientItemEditor.UpdateObject())
        {
          if (opcAEClientItemEditor.AddObject(MENU_CMD_ADD_OPC_AE_ITEM))
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void AddOpcAeAttrCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      if (pCollectionMember->IsA("GTWOpcAEMasterDataObject"))
      {
        if (pCollectionMember != nullptr)
        {
          GTWOPCAEClientAttrEditor OPCAEClientAttEditor(dto, pCollectionMember, nullptr, true);
          if (OPCAEClientAttEditor.UpdateObject())
          {
            if (OPCAEClientAttEditor.AddObject(MENU_CMD_ADD_OPC_AE_ATTR))
            {
              GTWOPCAEClient *pOPCAEClient = ((GTWOpcAEMasterDataObject*)pCollectionMember)->GetOPCAEClient();
              nlohmann::json res;
              res["result"] = true;
              pServer->BuildOkResponse(response, "application/json", res.dump());
              GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), pOPCAEClient->GetMemberName().c_str(), "MDO");
              return;
            }
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

#if USE_OPC_44
  static void Add61850ControlOpcMappingCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (pCollectionMember->IsA("GTW61850Server"))
    {
      GTW61850Server *p61850Server = dynamic_cast<GTW61850Server *>(pCollectionMember);
      if (p61850Server != nullptr)
      {
        GTWOPC61850ServerControlEditor OPC61850ServerControlEditor(dto, p61850Server, nullptr, true);
        if (OPC61850ServerControlEditor.UpdateObject())
        {
          if (OPC61850ServerControlEditor.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), p61850Server->GetAliasName().c_str(), "MDO");
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }
#endif

  static void Add61850ControlOpcMappingItemCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    CStdString sItemName = dto.pt_objectDataJson["itemName"].get<std::string>();
    CStdString sOPCClientName = dto.pt_objectDataJson["objectName"].get<std::string>();

    if (sItemName != "" && sOPCClientName != "")
    {
      nlohmann::json res;
      res["result"] = true;
      res["itemName"] = sOPCClientName + "." + sItemName;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      return;
    }
    else
    {
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    return;
  }
#endif // USE_OPC_44

  static void AddMultiPointCreateOrUpdate(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    //GTWHttpServer<SOCK_TYP> *pGtwHttpServer = static_cast<GTWHttpServer<SOCK_TYP> *>(pServer);

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;

      if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession *>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pSession != nullptr)
      {
        GTWMMBMultiPointEditor modbusMultiPointEditor(dto, pSession, NULL, TMWDEFS_TRUE);
        if (modbusMultiPointEditor.UpdateObject())
        {
          if (modbusMultiPointEditor.SaveObject())
          {
            nlohmann::json res;
            res["result"] = true;
            pServer->BuildOkResponse(response, "application/json", res.dump());
            CStdString isUsedForMdos = pCollectionMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
            GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.parentObjectName.c_str(), isUsedForMdos.c_str());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "could not update or save");
    return;
  }

  static void ShowTase2ServerConfig(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTase2Server* pTase2Server = nullptr;

      if (pCollectionMember->IsA("GTWTase2Server"))
        pTase2Server = dynamic_cast<GTWTase2Server*>(pCollectionMember);

      GTWTase2ServerEditor pTase2ServerEditor(dto, pTase2Server, TMWDEFS_TRUE);
      
      if (pTase2ServerEditor.WebReadObjectShowConfig(dto.pt_objectDataJson, true))
      {
        

        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: root");
      return;
    }
  }

  static void ShowTase2ClientConfig(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* GTWTase2Client = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        GTWTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      GTWTASE2ClientEditor pTase2ClientEditor(dto, GTWTase2Client, TMWDEFS_TRUE);
      
      if (pTase2ClientEditor.WebReadObjectShowConfig(dto.pt_objectDataJson, true))
      {
        

        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: root");
      return;
    }
  }

  static void EditGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWMasterDataObject *pMdo = nullptr;
    GTWSlaveDataObject  *pSdo = nullptr;
    GTWCollectionListParent *root = nullptr;
    bool editAtRunTime = true;
    //needed because DeepFindCollectionMemberUsingName change objectName
    CStdString objectFullName = dto.objectName;
    if ((dto.objectClassName == "GatewayRootNode" && dto.objectName == "" && dto.parentObjectName == "") || (dto.objectName == "root" && dto.parentObjectName == "root"))
    {
      GTWBaseEditor *pEditor = GetGTWApp()->GetRootEditor(dto);
      if (pEditor == nullptr)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_method_not_allowed, "", "");
        return;
      }
      else
      {
        if (pEditor->WebReadObject(dto.pt_objectDataJson, editAtRunTime))
        {
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: root");
        return;
      }
    }
    if (dto.objectClassName == "GTWTASE2DomainEditor" && dto.objectName !="" && dto.parentObjectName != "")
    {
      GTWTASE2Client * pTase2Client = GTWTASE2Client::getTASE2connection(dto.parentObjectName.c_str());
      if (pTase2Client != nullptr)
      {
        size_t index;
        std::string sDomainName = dto.objectName.c_str();
        std::string sDomainFullName = dto.objectName.c_str();
        if ((index = sDomainFullName.find(" (BTID=")) >= 0)
          sDomainName = sDomainFullName.substr(0, index);

        GTWTASE2DomainEditor tase2DomainEditor(dto, pTase2Client, sDomainName, false);
        if (tase2DomainEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    GTWDEFS_STAT status = GetGTWApp()->findMdo(dto.objectName.c_str(), &pMdo);
#if USE_OPC_UA
    if (dto.objectClassName == "GTWOPCUAUserSecurityEditor")
    {
      GTWOPCUAUserSecurityEditor OPCUAUserSecurityEditor(dto, nullptr, false);
      
      if (OPCUAUserSecurityEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    GTWOpcUaMasterDataObject* pOPCUaMdo = dynamic_cast<GTWOpcUaMasterDataObject*>(pMdo);
    if (pOPCUaMdo != nullptr)
    {
      GTWOpcUaClient* pOpcUaClient = pOPCUaMdo->getClient();
      GTWOPCUaClientItemEditor OPCUaClientItemEditor(dto, pOpcUaClient, pOPCUaMdo, false, false);
      
      if (OPCUaClientItemEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    /*
    GTWOpcUaMDOArrayIndex* pOPCUaMdoArrayIndex = dynamic_cast<GTWOpcUaMDOArrayIndex*>(pMdo);
    if (pOPCUaMdo != nullptr)
    {
      GTWOpcUaClient* pOpcUaClient = pOPCUaMdo->getClient();
      GTWOPCUaClientItemEditor OPCUaClientItemEditor(dto, pOpcUaClient, pOPCUaMdoArrayIndex, false, false);

      if (OPCUaClientItemEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    */
#endif // USE_OPC_UA
    if (status != GTWDEFS_STAT_SUCCESS || pMdo == nullptr)
    {
      pMdo = nullptr;
      status = GetGTWApp()->findSdo(dto.objectName.c_str(), &pSdo);
      if (status != GTWDEFS_STAT_SUCCESS || pSdo == nullptr)
      {
        bool isOPCClient = TMWDEFS_FALSE;
        bool isM103 = TMWDEFS_FALSE;
        GTWCollectionMember *pMember = nullptr;
        CStdString deepFindForSDO = objectFullName;
        CStdString deepFindForMDO = objectFullName;
        GTWCollectionBase *pCollection = GetGTWApp()->getSdoCollection();
        if (pCollection->DeepFindCollectionMemberUsingName(deepFindForSDO, &pMember, isOPCClient, isM103) != GTWDEFS_STAT_SUCCESS)
        {
          pCollection = GetGTWApp()->getMdoCollection();
          if (pCollection->DeepFindCollectionMemberUsingName(deepFindForMDO, &pMember, isOPCClient, isM103) != GTWDEFS_STAT_SUCCESS)
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found" + dto.objectName);
            return;
          }
        }

        if (pMember != nullptr)
        { // this does a device (i.e. chan sess, sctr etc)
          root = static_cast<GTWCollectionListParent *>(pMember);
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
          return;
        }
      }
    }
    if (pMdo != nullptr)
    {
#if USE_OPC_44
      GTWOpcMasterDataObject *pOPCMdo = dynamic_cast<GTWOpcMasterDataObject *>(pMdo);
      if (pOPCMdo != nullptr)
      {
        GTWOPCClient* pOPCClient = pOPCMdo->GetOPCClient();
        GTWOPCClientItemEditor OPCClientItemEditor(dto, pOPCClient, pOPCMdo, false, false);
        
        if (OPCClientItemEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
#endif // USE_OPC_44
      GTW61850DataAttributeMDO *dataAttributeMDO61850 = dynamic_cast<GTW61850DataAttributeMDO *>(pMdo);
      if (dataAttributeMDO61850 != nullptr)
      {
        if (dataAttributeMDO61850->isControl())
        {
          GTW61850CommandPointEditor *pCommandPointEditor61850 = (GTW61850CommandPointEditor*)dataAttributeMDO61850->GetBaseEditor(dto);
          pCommandPointEditor61850->SetEditableObject(dataAttributeMDO61850);

          if (pCommandPointEditor61850->WebReadObject(dto.pt_objectDataJson, dataAttributeMDO61850->GetClientNode(), false, editAtRunTime))
          {
            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
        }
        else
        {
          GTW61850DataAttributeMDOEditor *pDataAttributeMDOEditor61850 = (GTW61850DataAttributeMDOEditor*)dataAttributeMDO61850->GetBaseEditor(dto);
          pDataAttributeMDOEditor61850->SetEditableObject(dataAttributeMDO61850);
          {
            
            if (pDataAttributeMDOEditor61850->WebReadObject(dto.pt_objectDataJson, nullptr, false, editAtRunTime))
            {
              
              pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
              return;
            }
          }
        }
      }
      GTWTASE2DataAttributeMDO *dataAttributeMDOTASE2 = dynamic_cast<GTWTASE2DataAttributeMDO *>(pMdo);
      if (dataAttributeMDOTASE2 != nullptr)
      {
        if (dataAttributeMDOTASE2->isCommandMDO())
        {
          GTWTASE2CommandPointEditor *pCommandPointEditorTASE2 = (GTWTASE2CommandPointEditor*)dataAttributeMDOTASE2->GetBaseEditor(dto);
          pCommandPointEditorTASE2->SetEditableObject(dataAttributeMDOTASE2);

          
          if (pCommandPointEditorTASE2->WebReadObject(dto.pt_objectDataJson, editAtRunTime))
          {
            
            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
        }
        else
        {
          GTWTASE2DataAttributeMDOEditor *pDataAttributeMDOEditorTASE2 = (GTWTASE2DataAttributeMDOEditor*)dataAttributeMDOTASE2->GetBaseEditor(dto);
          pDataAttributeMDOEditorTASE2->SetEditableObject(dataAttributeMDOTASE2);
          {
            
            if (pDataAttributeMDOEditorTASE2->WebReadObject(dto.pt_objectDataJson, nullptr, editAtRunTime))
            {
              
              pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
              return;
            }
          }
        }
      }
      GTWBaseDataObject *pBdo = pMdo->getBdo();
      GTWBaseEditor *pEditor = pBdo->GetBaseEditor(dto);
      //assert(pEditor);
      if (pEditor == nullptr)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_method_not_allowed, "", "");
        return;
      }
      
      if (pEditor->WebReadObject(dto.pt_objectDataJson, editAtRunTime))
      {
        

        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }
    if (pSdo != nullptr)
    {
      if (pSdo->getBdo()->IsA("GTW61850SlaveDataObject"))
      {
        GTWCollectionMember *m_pTargetMember = pSdo->getBdo()->GetParentMember();
        if (m_pTargetMember != nullptr)
        {
          GTW61850SdoEditor sdoEditor(dto, m_pTargetMember, pSdo->getBdo(), true);
          

          if (pSdo->getBdo() != nullptr)
          {
            CStdString sClassName = pSdo->getBdo()->ClassName();
            if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
            {
              

              pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
              return;
            }
          }
        }
      }
      else if (pSdo->getBdo()->IsA("GTWTase2SlaveDataObject"))
      {
        GTWCollectionMember* m_pTargetMember = pSdo->getBdo()->GetParentMember();
        if (m_pTargetMember != nullptr)
        {
          GTWTASE2SdoEditor sdoEditor(dto, m_pTargetMember, pSdo->getBdo(), false);
          if (pSdo->getBdo() != nullptr)
          {
            CStdString sClassName = pSdo->getBdo()->ClassName();
            if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
            {
              

              pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
              return;
            }
          }
        }
      }
#if USE_OPC_44
      else if (pSdo->getBdo()->IsA("GTWOPC61850ControlImpl"))
      {
        GTW61850Server *pParentMember = (GTW61850Server *)pSdo->getBdo()->GetParentMember();
        if (pParentMember != nullptr)
        {
          GTWOPC61850ServerControlEditor sdoEditor(dto, pParentMember, pSdo->getBdo(), true);
          

          if (pSdo->getBdo() != nullptr)
          {
            CStdString sClassName = pSdo->getBdo()->ClassName();
            if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
            {
              

              pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
              return;
            }
          }
        }
      }
#endif
      else
      {
        GTWSdoMappingEditor *pSdoMappingEditor = dynamic_cast<GTWSdoMappingEditor *>(pSdo->getBdo()->GetBaseEditor(dto));
        if (pSdoMappingEditor == nullptr)
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "No editor for SDO");
          return;
        }
        

        if (pSdo->getBdo() != nullptr)
        {
          CStdString sClassName = pSdo->getBdo()->ClassName();
          if (pSdoMappingEditor->WebReadObject(dto.pt_objectDataJson, true))
          {
            

            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
        }
      }
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
    if (root != nullptr)
    {
      GTWTase2Server* pT2Server = dynamic_cast<GTWTase2Server*>(root);
      if (pT2Server && pT2Server->IsServerUp() && !pT2Server->CanModifyWhileRunning())
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(),
          "TR_TASE2_CANNOT_EDIT_SERVER_RUNNING", "Cannot edit server while it is running. Stop the server and try again.");
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      bool deleteEditor = false;
      GTWBaseEditor *pEditor = root->GetBaseEditor(dto);
      assert(pEditor);
      if (pEditor == nullptr)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_method_not_allowed, "", "");
        return;
      }
      
      if (pEditor->WebReadObject(dto.pt_objectDataJson, editAtRunTime))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#if USE_GOOSE_MONITOR
  static void AddGooseMonitorGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWGooseMonitorEditor gooseMonitorEditor(dto, nullptr, true);
    
    if (gooseMonitorEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      

      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif
  static void AddWriteActionGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;

      if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession *>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pSession != nullptr)
      {
        GTWModbusSessionActionEditor modbusSessionActionEditor(dto, pSession, NULL, TMWDEFS_TRUE);
        
        if (modbusSessionActionEditor.WebReadWriteActionEditor(dto.objectName, dto.pt_objectDataJson, true))
        {
          

          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2DataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        GTWTASE2DatasetEditor TASE2DatasetEditor(dto, "", pClientTASE2, true);
        
        if (TASE2DatasetEditor.WebReadObject(dto.pt_objectDataJson, true, true,false))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2CommandPointGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2ControlBlock *pTASE2ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTWTASE2ControlBlock"))
        pTASE2ControlBlock = dynamic_cast<GTWTASE2ControlBlock *>(pCollectionMember);

      if (pTASE2ControlBlock != nullptr)
      {
        GTWTASE2CommandPointEditor tase2CommandPointEditor(dto, nullptr, pTASE2ControlBlock->GetTASE2Client(), pTASE2ControlBlock, true);
        
        if (tase2CommandPointEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2PolledDataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTWTASE2PolledDataSetEditor tase2PolledDataSetEditor(dto, nullptr, pClient, true);
        
        if (tase2PolledDataSetEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2PolledPointSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTWTASE2PolledPointSetEditor tase2PolledPointSetEditor(dto, nullptr, pClient, true);
        
        if (tase2PolledPointSetEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2ItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2ControlBlock *pTASE2ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTWTASE2ControlBlock"))
        pTASE2ControlBlock = dynamic_cast<GTWTASE2ControlBlock *>(pCollectionMember);

      if (pTASE2ControlBlock != nullptr)
      {
        GTWTASE2DataAttributeMDOEditor tase2DataAttributeMDOEditor(dto, nullptr, nullptr, true);
        
        if (tase2DataAttributeMDOEditor.WebReadObject(dto.pt_objectDataJson, pTASE2ControlBlock, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2DstsGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClient = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTWTASE2CltReportedDataSetEditor tase2CltReportedDataSetEditor(dto, nullptr, pClient, true);
        
        if (tase2CltReportedDataSetEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2LogicalDeviceGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTase2Server *pTase2Server = nullptr;

      if (pCollectionMember->IsA("GTWTase2Server"))
        pTase2Server = dynamic_cast<GTWTase2Server *>(pCollectionMember);

      if (pTase2Server != nullptr)
      {
        if (pTase2Server->IsServerUp() && !pTase2Server->CanModifyWhileRunning())
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(),
            "TR_TASE2_CANNOT_CREATE_DOMAIN_CONNECTED", "Cannot add a domain to an ICCP server while it is running. Stop the server and try again.");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }

        GTWTase2ServerLogicalDeviceEditor tase2ServerLogicalDeviceEditor(dto, nullptr, pTase2Server, true);
        if (tase2ServerLogicalDeviceEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  // Called when edit model is called on client
  static void EditTase2EditModelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        if (pClientTASE2->IsConnected())
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(),
            "TR_TASE2_CANNOT_EDIT_MODEL_CONNECTED", "Cannot edit client model while actively connected to a server.");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
        else if (pClientTASE2->IsChannelActive() && pClientTASE2->IsReconnectTimerRunning()) // if the timer is actually running it is actively trying to connect, then fail this request
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, dto.token.c_str(),
           "TR_TASE2_CANNOT_EDIT_MODEL", "Cannot edit client model while reconnect timer is running. Please set the channel active control to false and then re-try edit.");
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }

        GTWTASE2ClientEditor clientEditorTase2(dto, pClientTASE2, false);
        if (clientEditorTase2.WebReadObjectModel(dto.objectName, dto.objectClassName, dto.pt_objectDataJson, true))
        {
          pClientTASE2->SetEditing(true);
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2GetDataClient(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWTASE2ClientEditor clientEditorTase2(dto, nullptr, true);
    clientEditorTase2.SetServiceRole(CLIENT_ROLE);
    
    if (clientEditorTase2.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2GetDataServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWTASE2ClientEditor clientEditorTase2(dto, nullptr, true);
    clientEditorTase2.SetServiceRole(SERVER_ROLE);
    
    if (clientEditorTase2.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }


  static void AddTase2GetDataClientServer(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWTASE2ClientEditor clientEditorTase2(dto, nullptr, true);
    clientEditorTase2.SetServiceRole(CLIENT_SERVER_ROLE);
    
    if (clientEditorTase2.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }


  static void AddTase2DomainGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pTase2Client = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pTase2Client != nullptr)
      {
        GTWTASE2DomainEditor tase2DomainEditor(dto, pTase2Client, "", true);
        
        if (tase2DomainEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2DataAttributeGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pTase2Client = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      size_t index;
      std::string sDomainName = dto.objectName.c_str();
      std::string sDomainFullName = dto.objectName.c_str();
      if ((index = sDomainFullName.find(" (BTID=")) >= 0)
        sDomainName = sDomainFullName.substr(0, index);

      if (pTase2Client != nullptr && sDomainName !="")
      {
        GTWTASE2DataAttributeEditor tase2DataAttributeEditor(dto, pTase2Client, sDomainName, true);
        
        if (tase2DataAttributeEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ManageTase2DataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        GTWTASE2DatasetEditor TASE2DatasetEditor(dto, dto.objectName.c_str(), pClientTASE2, false);
        
        if (TASE2DatasetEditor.WebReadObject(dto.pt_objectDataJson, false, true, false))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ManageTase2DataSetGetDataFullEdit(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client* pClientTASE2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTASE2 = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

      if (pClientTASE2 != nullptr)
      {
        GTWTASE2DatasetEditor TASE2DatasetEditor(dto, dto.objectName.c_str(), pClientTASE2, false);

        if (TASE2DatasetEditor.WebReadObject(dto.pt_objectDataJson, false, true, true))
        {

          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61400AlarmsNodeGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTW61400AlarmsEditor alarmsEditor61400(dto, nullptr, pClient, true);
        
        if (alarmsEditor61400.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61400AlarmMDOGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61400Alarms *pClient = nullptr;

      if (pCollectionMember->IsA("GTW61400Alarms"))
        pClient = dynamic_cast<GTW61400Alarms *>(pCollectionMember);

      if (pClient != nullptr)
      {
        GTW61400AlarmMDOEditor alarmMDOEditor61400(dto, pClient, nullptr, true);
        if (alarmMDOEditor61400.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850WritablePointSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850WritablePointSetEditor writablePointEditor61850(dto, nullptr, pClient61850, true);
        
        if (writablePointEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850WritablePointGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850WriteablePointEditor writablePointEditor61850(dto, nullptr, p61850ControlBlock->Get61850Client(), p61850ControlBlock, false);
        
        if (writablePointEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTase2CommandPointSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWTASE2Client *pClientTase2 = nullptr;

      if (pCollectionMember->IsA("GTWTASE2Client"))
        pClientTase2 = dynamic_cast<GTWTASE2Client *>(pCollectionMember);

      if (pClientTase2 != nullptr)
      {
        GTWTASE2CommandPointSetEditor commandPointEditorTase2(dto, nullptr, pClientTase2, true);
        
        if (commandPointEditorTase2.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850CommandPointSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850CommandPointSetEditor commandPointEditor61850(dto, nullptr, pClient61850, true);
        
        if (commandPointEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850CommandPointGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850CommandPointEditor commandPointEditor61850(dto, nullptr, p61850ControlBlock->Get61850Client(), p61850ControlBlock, true);
        if (commandPointEditor61850.WebReadObject(dto.pt_objectDataJson, p61850ControlBlock->Get61850Client(), true, true))
        {
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_CLIENT_NO_CONTROLLABLE_ELEMENTS_FOUND", "No controls found");
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Change61850DataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850ControlBlock *controlBlock = pClient61850->FindControlBlock(dto.objectName);
        //tmw::CriticalSectionLock mapLock(*controlBlock->GetMemberCollection());
        if (controlBlock && controlBlock->GetMemberCollection() && controlBlock->GetMemberCollection()->GetMap()->size() > 0)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_CLIENT_CANT_CHANGE_DATASET", "Unable to change dataset.  The control block {{arg1}} has associated MDO's.", dto.objectName.c_str());
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
        GTW61850ChangeDataSetEditor GTW61850ChangeDataSetEditor(dto, nullptr, pClient61850, true);
        
        if (GTW61850ChangeDataSetEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850DataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850ControlBlock *controlBlock = pClient61850->FindControlBlock(dto.objectName);
        //tmw::CriticalSectionLock mapLock(*controlBlock->GetMemberCollection());
        if (controlBlock && controlBlock->GetMemberCollection() && controlBlock->GetMemberCollection()->GetMap()->size() > 0)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, dto.token.c_str(), "TR_61850_CLIENT_CANT_CHANGE_DATASET", "Unable to change dataset.  The control block {{arg1}} has associated MDO's.", dto.objectName.c_str());
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }

        GTW61850DatasetEditor GTW61850DatasetEditor(dto, nullptr, pClient61850, true);
        
        if (GTW61850DatasetEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void SelectDataAttributeGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850DataAttributeMDOEditor dataAttributeMDOEditor61850(dto, nullptr, nullptr, true);
        
        if (dataAttributeMDOEditor61850.WebReadObjectQualityTime(dto.objectName, dto.objectClassName, dto.pt_objectDataJson, p61850ControlBlock))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850ItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850ControlBlock *p61850ControlBlock = nullptr;

      if (pCollectionMember->IsA("GTW61850ControlBlock"))
        p61850ControlBlock = dynamic_cast<GTW61850ControlBlock *>(pCollectionMember);

      if (p61850ControlBlock != nullptr)
      {
        GTW61850DataAttributeMDOEditor dataAttributeMDOEditor61850(dto, nullptr, nullptr, true);
        
        if (dataAttributeMDOEditor61850.WebReadObject(dto.pt_objectDataJson, p61850ControlBlock, true, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850PolledPointSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850PolledPointSetEditor polledPointSetEditor61850(dto, nullptr, pClient61850, true);
        
        if (polledPointSetEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850PolledDataSetGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;
      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850PolledDataSetEditor polledDataSetEditor61850(dto, nullptr, pClient61850, true);
        if (polledDataSetEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850ReportGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850ReportEditor reportEditor61850(dto, nullptr, pClient61850, true);
        
        if (reportEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850GooseGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTW61850Client *pClient61850 = nullptr;

      if (pCollectionMember->IsA("GTW61850Client"))
        pClient61850 = dynamic_cast<GTW61850Client *>(pCollectionMember);

      if (pClient61850 != nullptr)
      {
        GTW61850GOOSEEditor GOOSEEditor61850(dto, nullptr, pClient61850, true);
        
        if (GOOSEEditor61850.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850ClientGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);
    
    if (clientEditor61850.WebReadObject(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850ClientsFromFileGetData(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTW61850ClientEditor clientEditor61850(dto, nullptr, true);

    if (clientEditor61850.WebReadObjectLoadClientsFromFile(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void Add61850ServerGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTW61850ServerEditor serverEditor61850(dto, nullptr, true);
    
    if (serverEditor61850.WebReadObject(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

#if USE_OPC_UA
  static void AddOpcUaClientGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUaClientEditor OPCUAClientEditor(dto, nullptr, true);
    
    if (OPCUAClientEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcUaCertificateGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUACertificateEditor GTWOPCUACertificateEditor(dto, nullptr, true);

    if (GTWOPCUACertificateEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcUaMultipleItemsGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOpcUaClient* pClient = dynamic_cast<GTWOpcUaClient*>(pCollectionMember);
      if (!pClient)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      if (!pClient->IsConnected())
      {
        CStdString sErr;
        sErr.Format("OPC UA Client '%s' is not connected.", pClient->GetFullName().c_str());
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_OPC_UA, dto.token.c_str(), "", sErr.c_str());

        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pClient->BrowseMultiPoint(dto))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  static void AddOpcUaItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOpcUaClient* pClient = dynamic_cast<GTWOpcUaClient*>(pCollectionMember);
      if (!pClient)
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      if (!pClient->IsConnected())
      {
        CStdString sErr;
        sErr.Format("OPC UA Client '%s' is not connected.", pClient->GetFullName().c_str());
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_OPC_UA, dto.token.c_str(), "", sErr.c_str());

        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pClient->Browse(dto))
      {
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
  }

  static void EditOpcUaServerGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCUAServerEditor opcUaServerEditor(dto, nullptr, false);
    
    if (opcUaServerEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif

#if USE_OPC_44
  static void AddOpcAeClientGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCAEClientEditor OPCAEClientEditor(dto, nullptr, true);
    
    if (OPCAEClientEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcClientGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWOPCClientEditor OPCClientEditor(dto, nullptr, true);
    
    if (OPCClientEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcMultipleItemsGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOPCClient* opcClient = nullptr;
      if (pCollectionMember->IsA("GTWOPCClient"))
      {
        opcClient = dynamic_cast<GTWOPCClient*>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      if (opcClient != nullptr)
      {
        GTWOPCClientItemEditor OPCClientItemEditor(dto, opcClient, nullptr, true, false);

        if (OPCClientItemEditor.WebReadObjectMultiPoints(dto.pt_objectDataJson, true, true))
        {

          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember* pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOPCClient* opcClient = nullptr;
      if (pCollectionMember->IsA("GTWOPCClient"))
      {
        opcClient = dynamic_cast<GTWOPCClient*>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      if (opcClient != nullptr)
      {
        GTWOPCClientItemEditor OPCClientItemEditor(dto, opcClient, nullptr, true, false);

        if (OPCClientItemEditor.WebReadObject(dto.pt_objectDataJson, true))
        {

          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcAeItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOPCAEClient *pOPCAEClient = nullptr;
      if (pCollectionMember->IsA("GTWOPCAEClient"))
      {
        pOPCAEClient = dynamic_cast<GTWOPCAEClient *>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }
      if (pOPCAEClient != nullptr)
      {
        GTWOPCAEClientItemEditor OPCAEClientItemEditor(dto, pOPCAEClient, nullptr, true, false);
        
        if (OPCAEClientItemEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOpcAeAttrGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      if (pCollectionMember->IsA("GTWOpcAEMasterDataObject"))
      {
        if (pCollectionMember != nullptr)
        {
          GTWOPCAEClientAttrEditor OPCAEClientAttEditor(dto, pCollectionMember, nullptr, true);
          
          if (OPCAEClientAttEditor.WebReadObject(dto.pt_objectDataJson, true))
          {
            
            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

#if USE_OPC_44
  static void Add61850ControlOpcMappingGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (pCollectionMember->IsA("GTW61850Server"))
    {
      GTW61850Server *p61850Server = dynamic_cast<GTW61850Server *>(pCollectionMember);
      if (p61850Server != nullptr)
      {
        GTWOPC61850ServerControlEditor OPC61850ServerControlEditor(dto, p61850Server, nullptr, true);
        
        if (OPC61850ServerControlEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif
  static void Add61850ControlOpcMappingItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWOPCClient *opcClient = nullptr;
      if (pCollectionMember->IsA("GTWOPCClient"))
      {
        opcClient = dynamic_cast<GTWOPCClient *>(pCollectionMember);

        if (opcClient != nullptr)
        {
          if (!opcClient->IsConnected())
          {
            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, dto.token.c_str(), "TR_OPC_CLIENT_IS_NOT_CONNECTED_CANNOT_SELECT_POINTS_UNTIL_CONNECTED.", "OPC Client {{arg1}} is not connected - cannot select points until connected.", opcClient->GetAliasName());
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }

          GTWOPC61850ServerControlItemEditor OPC61850ServerControlItemEditor(dto, opcClient, true, false);
          
          if (OPC61850ServerControlItemEditor.WebReadObject(dto.pt_objectDataJson, true))
          {
            
            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
#endif

  static void AddMultiPointGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;

      if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession *>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pSession != nullptr)
      {
        GTWMMBMultiPointEditor modbusMultiPointEditor(dto, pSession, NULL, TMWDEFS_TRUE);
        
        if (modbusMultiPointEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddDataTypeGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;
      TMWTYPES_USHORT sectorIndex = -1;

      if (pCollectionMember->IsA("GTWSector"))
      {
        GTWSector *pSector = dynamic_cast<GTWSector *>(pCollectionMember);
        if (pSector != nullptr)
        {
          pSession = pSector->GetSession();
          sectorIndex = pSector->GetSectorIndex();
        }
      }
      else if (pCollectionMember->IsA("GTWSession"))
      {
        pSession = dynamic_cast<GTWSession *>(pCollectionMember);
      }
      else
      {
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
        return;
      }

      if (pSession != nullptr)
      {
        TMWTYPES_USHORT sessionIndex = pSession->GetSessionIndex();
        GTWdataTypeEditor dataTypeEditor(dto, NULL, sessionIndex, sectorIndex, TMWDEFS_TRUE);
        
        if (dataTypeEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddSectorGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWSession *pSession = nullptr;
      pSession = dynamic_cast<GTWSession *>(pCollectionMember);

      if (pSession != nullptr)
      {
        GTWSectorEditor sectorEditor(dto, NULL, pSession, TMWDEFS_TRUE);
        
        if (sectorEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddSessionGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWChannel *pChannel = dynamic_cast<GTWChannel *>(pCollectionMember);
      if (pChannel != nullptr)
      {
        TMWTYPES_USHORT channelIndex = pChannel->GetChannelIndex();
        GTWTYPES_PROTOCOL protocol = (GTWTYPES_PROTOCOL)GTWConfig::PhysComProtocol(channelIndex);
        GTWSessionEditor sessionEditor(dto, NULL, channelIndex, protocol, TMWDEFS_TRUE);
        
        if (sessionEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddSerialSlaveChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_232);
    
    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTcpSlaveChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_TCP);
    channelEditor.SetPortName("127.0.0.1");

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWChannel *pChannel = dynamic_cast<GTWChannel *>(pCollectionMember);
      if (pChannel != nullptr)
      {
        if (pChannel->IsRedundancyGroup())
        {
          channelEditor.SetCreateRedundant(true);
          channelEditor.SetProtocol(GTWTYPES_PROTOCOL_S104);
        }
      }
    }

    
    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddSerialMasterChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_232);
    
    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddTcpMasterChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_TCP);
    channelEditor.SetPortName("127.0.0.1");

    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWChannel *pChannel = dynamic_cast<GTWChannel *>(pCollectionMember);
      if (pChannel != nullptr)
      {
        if (pChannel->IsRedundancyGroup())
        {
          channelEditor.SetCreateRedundant(true);
          channelEditor.SetProtocol(GTWTYPES_PROTOCOL_M104);
        }
      }
    }

    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddDNP3_UDP_TCPSlaveChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_SDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_UDP_TCP);
    channelEditor.SetMode(TMWTARGTCP_MODE_SERVER);
    channelEditor.SetProtocol(GTWTYPES_PROTOCOL_SDNP);
    channelEditor.SetPortName("127.0.0.1");
    channelEditor.SetIpPort(20000);
    
    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddDNP3_UDP_TCPMasterChannelGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWChannelEditor channelEditor(dto, nullptr, true, GTWTYPES_COLLECTION_KIND::GTWTYPES_COLLECTION_KIND_MDO);
    channelEditor.SetPhysComType(TMWTARGIO_TYPE_UDP_TCP);
    channelEditor.SetMode(TMWTARGTCP_MODE_CLIENT);
    channelEditor.SetProtocol(GTWTYPES_PROTOCOL_MDNP);
    channelEditor.SetPortName("127.0.0.1");
    channelEditor.SetIpPort(20000);
    
    if (channelEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOdbcItemGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWCollectionMember *pCollectionMember = nullptr;
    bool status = GetGTWApp()->findMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
    if (status && pCollectionMember != nullptr)
    {
      GTWODBCClient *pODBCClient = dynamic_cast<GTWODBCClient *>(pCollectionMember);
      if (pODBCClient != nullptr)
      {
        GTWODBCQueryEditor queryEditorODBC(dto, nullptr, pODBCClient, pODBCClient->getObIndex(), true);
        
        if (queryEditorODBC.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddOdbcClientGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWODBCClientEditor clientEditorODBC(dto, nullptr, true);
    
    if (clientEditorODBC.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddEquationMdoGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWmdoEquationEditor mdoEquationEditor(dto, nullptr, nullptr, true);
    
    if (mdoEquationEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddMappingMdosGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    GTWCollectionMember *pParentMdo = nullptr;
    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString objectFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(objectFullNameCopy, &pParentMdo, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMdo == nullptr)
    {
      bool status = GetGTWApp()->deepFindMember(objectFullNameCopy, dto.objectCollectionKind, &pParentMdo);
      if (!status || pParentMdo == nullptr)
      {
        pParentMdo = nullptr;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
        return;
      }
    }
    

    if (!pParentMdo->IsA("GTW61850Server"))
    {
      //GTWDataType *pDataType = dynamic_cast<GTWDataType *>(pParentMdo);
      //CStdString className = "";
      //if (pDataType != nullptr)
      //{
      //  dto.objectClassName = pDataType->m_pDataTypeGroup->GetBdoClass();
      //}
      GTWMdosMappingEditor mdosMappingEditor(dto, pParentMdo, nullptr, true);
      if (mdosMappingEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void ActionDropOnFolder(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    try
    {
      EditorCommandDTO dto = _dto;
      GTWCollectionMember* pTargetMember = nullptr;
      bool isOPCClient = TMWDEFS_FALSE;
      bool isM103 = TMWDEFS_FALSE;
      CStdString folderName = dto.params[1].c_str();
      GTWCollectionBase* pCollection = nullptr;
      pCollection = GetGTWApp()->getMdoCollection();
      GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(folderName, &pTargetMember, isOPCClient, isM103);
      if (status != GTWDEFS_STAT_SUCCESS || pTargetMember == nullptr)
      {
        bool status = GetGTWApp()->deepFindMember(folderName, dto.objectCollectionKind, &pTargetMember);
        if (!status || pTargetMember == nullptr)
        {
          pTargetMember = nullptr;
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.params[1]);
          return;
        }
      }

      GTWCollectionMember* pDropMember = nullptr;
      isOPCClient = TMWDEFS_FALSE;
      isM103 = TMWDEFS_FALSE;
      CStdString itemNames = dto.params[2].c_str();

      std::istringstream iss(itemNames);
      std::vector<std::string> vect;

      std::string s;
      while (getline(iss, s, ','))
        vect.push_back(s);

      size_t i;
      for (i = 0; i < vect.size(); i++)
      {
        CStdString itemName = vect[i];

        pCollection = nullptr;
        pCollection = GetGTWApp()->getMdoCollection();
        status = pCollection->DeepFindCollectionMemberUsingName(itemName, &pDropMember, isOPCClient, isM103);
        if (status != GTWDEFS_STAT_SUCCESS || pDropMember == nullptr)
        {
          bool status = GetGTWApp()->deepFindMember(itemName, dto.objectCollectionKind, &pDropMember);
          if (!status || pDropMember == nullptr)
          {
            pDropMember = nullptr;
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.params[2]);
            return;
          }
        }

        if (pTargetMember->IsA("GTWUserFolder"))
        {
          GTWUserDefinedFolderEditor folderEditor(dto, pTargetMember, pDropMember, false);
          if (!folderEditor.MoveObjectToFolder(pTargetMember, pDropMember))
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Editor, nullptr, "Cannot move %s to %s", itemName.c_str(), folderName.c_str());
        }
      }

      nlohmann::json res;
      res["result"] = true;
      pServer->BuildOkResponse(response, "application/json", res.dump());
      CStdString isUsedForMdos = pTargetMember->GetParentCollection()->IsUsedForMdos() == true ? "MDO" : "SDO";
      GtwBroadcastMessage::SendRefreshUI(dto.token.c_str(), dto.params[1].c_str(), isUsedForMdos);
      return;
    }
    catch (const std::exception& e)
    {
      LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Editor, nullptr, "Exception: %s", e.what());
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
      return;
    }
  }

  static void AddMappingSdosGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    GTWCollectionMember *pParentSdo = nullptr;
    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString objectFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(objectFullNameCopy, &pParentSdo, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentSdo == nullptr)
    {
      bool status = GetGTWApp()->deepFindMember(objectFullNameCopy, dto.objectCollectionKind, &pParentSdo);
      if (!status || pParentSdo == nullptr)
      {
        pParentSdo = nullptr;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
        return;
      }
    }
    

    if (!pParentSdo->IsA("GTW61850Server"))
    {
      //GTWDataType *pDataType = dynamic_cast<GTWDataType *>(pParentSdo);
      //CStdString className = "";
      //if (pDataType != nullptr)
      //{
      //  dto.objectClassName = pDataType->m_pDataTypeGroup->GetBdoClass();
      //}
      GTWSdosMappingEditor sdosMappingEditor(dto, pParentSdo, nullptr, true);
      if (sdosMappingEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddMappingMdoGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    
    GTWCollectionMember* pCollectionMember = nullptr;
    bool bStatus = GetGTWApp()->deepFindMember(dto.parentObjectName.c_str(), dto.objectCollectionKind, &pCollectionMember);

    if (bStatus || pCollectionMember != nullptr)
    {
      if (pCollectionMember->IsA("GTWTASE2Client"))
      {
        GTWTASE2Client* pTase2Client = nullptr;
        pTase2Client = dynamic_cast<GTWTASE2Client*>(pCollectionMember);

        if (pTase2Client != nullptr)
        {
          GTWTASE2MappingDataAttributeEditor tase2MappingDataAttributeEditor(dto, pTase2Client, true);
          
          if (tase2MappingDataAttributeEditor.WebReadObject(dto.pt_objectDataJson, true))
          {
            
            pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
            return;
          }
          else
          {
            pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
            return;
          }
        }
        else
        {
          pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
          return;
        }
      }
    }

    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString objectFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase* pCollection = nullptr;
    GTWCollectionMember* pParentMember = nullptr;
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(objectFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status == GTWDEFS_STAT_SUCCESS && pParentMember != nullptr)
    {
      GTWMdoMappingEditor mdoMappingEditor(dto, pParentMember, nullptr, true);
      if (mdoMappingEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }

    if (status == GTWDEFS_STAT_NOT_FOUND && pParentMember == nullptr && objectFullNameCopy == "")
    {
      GTWMdoMappingEditor mdoMappingEditor(dto, pParentMember, nullptr, true);
      if (mdoMappingEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }

    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddMappingSdoGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    GTWCollectionMember* pParentMember = nullptr;
    GTWCollectionMember* pCollectionMember = nullptr;
    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString objectFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase* pCollection = nullptr;
    pCollection = GetGTWApp()->getSdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(objectFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      bool status = GetGTWApp()->deepFindMember(objectFullNameCopy, dto.objectCollectionKind, &pParentMember);
      if (!status || pParentMember == nullptr)
      {
        pParentMember = nullptr;
        pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
        return;
      }
    }
    

    if (pParentMember->IsA("GTW61850Server"))
    {
      bool status = GetGTWApp()->deepFindMember(dto.objectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTW61850SdoEditor sdoEditor(dto, pParentMember, pCollectionMember, true);
        if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    else if (pParentMember->IsA("GTWTase2Server") || pParentMember->IsA("GTWTase2VCCFolder"))
    {
      bool status = GetGTWApp()->deepFindMember(dto.objectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2SdoEditor sdoEditor(dto, pParentMember, pCollectionMember, true);
        if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    else if (pParentMember->IsA("GTWTase2ServerLogicalDevice"))
    {
      bool status = GetGTWApp()->deepFindMember(dto.objectName.c_str(), dto.objectCollectionKind, &pCollectionMember);
      if (status && pCollectionMember != nullptr)
      {
        GTWTASE2SdoEditor sdoEditor(dto, pParentMember, pCollectionMember, true);
        if (sdoEditor.WebReadObject(dto.pt_objectDataJson, true))
        {
          
          pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
          return;
        }
      }
    }
    else
    {
      GTWCollectionListParent* pCollectionListParent = dynamic_cast<GTWCollectionListParent*>(pParentMember);
      //GTWDataType *pDataType = dynamic_cast<GTWDataType *>(pParentMember);

      //CStdString className = "";
      //if (pDataType != nullptr)
      //{
      //  dto.objectClassName = pDataType->m_pDataTypeGroup->GetBdoClass();
      //}
      GTWSdoMappingEditor sdoMappingEditor(dto, pCollectionListParent, nullptr, true);
      if (sdoMappingEditor.WebReadObject(dto.pt_objectDataJson, true))
      {
        
        pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
        return;
      }
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddMdoGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    GTWCollectionMember *pParentMember = nullptr;
    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase *pCollection = nullptr;
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      pParentMember = nullptr;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }

    GTWCollectionListParent *pCollectionListParent = dynamic_cast<GTWCollectionListParent *>(pParentMember);
    GTWDataType *pDataType = dynamic_cast<GTWDataType *>(pParentMember);
    CStdString className = "";
    if (pDataType != nullptr && dto.objectClassName == "GTWDataType")
    {
      dto.objectClassName = pDataType->m_pDataTypeGroup->GetBdoClass();
    }
    
    GTWmdoEditor mdoEditor(dto, pCollectionListParent, nullptr, true);
    if (mdoEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddMultipleMdoGetData(EditorCommandDTO &_dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    EditorCommandDTO dto = _dto;
    GTWCollectionMember* pParentMember = nullptr;
    bool isOPCClient = TMWDEFS_FALSE;
    bool isM103 = TMWDEFS_FALSE;
    CStdString nodeFullNameCopy = dto.parentObjectName.c_str();
    GTWCollectionBase* pCollection = nullptr;
    pCollection = GetGTWApp()->getMdoCollection();
    GTWDEFS_STAT status = pCollection->DeepFindCollectionMemberUsingName(nodeFullNameCopy, &pParentMember, isOPCClient, isM103);
    if (status != GTWDEFS_STAT_SUCCESS || pParentMember == nullptr)
    {
      pParentMember = nullptr;
      pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "Object not found: " + dto.objectName);
      return;
    }

    GTWCollectionListParent* pCollectionListParent = dynamic_cast<GTWCollectionListParent*>(pParentMember);
    GTWDataType* pDataType = dynamic_cast<GTWDataType*>(pParentMember);
    CStdString className = "";
    if (pDataType != nullptr && dto.objectClassName == "GTWDataType")
      dto.objectClassName = pDataType->m_pDataTypeGroup->GetBdoClass();

    GTWmdoEditor mdoEditor(dto, pCollectionListParent, nullptr, true);
    if (mdoEditor.WebReadMultipleObject(dto.pt_objectDataJson, true, true))
    {
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }
  

  static void AddInternalMdoGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWInternalUserMDOEditor intMdoEditor(dto, nullptr, nullptr, true);
    

    // class name used to get options
    if (intMdoEditor.WebReadObject(dto.pt_objectDataJson, true))
    {
      
      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }
    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void AddUserFolderGetData(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    GTWUserDefinedFolderEditor userFolderEditor(dto, nullptr, nullptr, true);


    // class name used to get options
    if (userFolderEditor.WebReadObject(dto.pt_objectDataJson, true))
    {

      pServer->BuildOkResponse(response, "application/json", dto.pt_objectDataJson.dump());
      return;
    }

    pServer->BuildBadResponse(response, SimpleWeb::StatusCode::client_error_bad_request, "", "");
    return;
  }

  static void NoOp(EditorCommandDTO &dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    throw std::runtime_error("command not implemented");
  }

public:
  static bool DeleteSdgObject(EditorCommandDTO &dto, std::string& msg)
  {
    GTWCollectionMember* pCollectionMember = nullptr;

    if (dto.objectName.length() > 0)
    {
      //For deleting BDO
      GTWBaseDataObject* pBdo = nullptr;
      GTWCollectionListParent* pParent = nullptr;
      GTWDEFS_STAT status = GetGTWApp()->findBdo(dto.objectName.c_str(), &pBdo);
      if (status == GTWDEFS_STAT_SUCCESS && pBdo != nullptr)
      {
        GTWCollectionListParent* controlBlockParent = nullptr;
        GTW61850DataAttributeMDO* daMDO = dynamic_cast<GTW61850DataAttributeMDO*>(pBdo);
        if (daMDO)
          controlBlockParent = daMDO->GetControlBlock();

        GTWTASE2DataAttributeMDO* t2DaMDO = dynamic_cast<GTWTASE2DataAttributeMDO*>(pBdo);
        if (t2DaMDO)
          controlBlockParent = t2DaMDO->GetControlBlock();

        pParent = pBdo->GetParentMember();
        if (pParent != nullptr)
        {
          if (controlBlockParent)
          {
            dto.parentObjectName = controlBlockParent->GetFullName();
            dto.objectCollectionKind = (controlBlockParent->GetMemberCollection()->IsUsedForMdos() == true ? "MDO" : "SDO");
          }
          else
          {
            dto.parentObjectName = pParent->GetFullName();
            dto.objectCollectionKind = (pParent->GetMemberCollection()->IsUsedForMdos() == true ? "MDO" : "SDO");
          }
        }
        else
        {
          dto.parentObjectName = "root";
          dto.objectCollectionKind = "ALL";
        }

        //Replace by FindExistingParent
        //GTWBaseEditor* pEditor = pBdo->GetBaseEditor(dto);
        //int s = pParent != nullptr ? pParent->GetMemberCollection()->GetMap()->size() : 0;
        //if (s == 1)
        //{
        //  dto.parentObjectName = "root";
        //  dto.objectCollectionKind = "ALL";
        //}
      }

      //For deleting the Rest
      bool bOk = GetGTWApp()->deepFindMember(dto.objectName, dto.objectCollectionKind, &pCollectionMember);
      if (bOk == false && pCollectionMember == nullptr)
      {
        msg += " object not found: " + dto.objectName;
        return false;
      }

      GTWBaseEditor* pEditor = pCollectionMember->GetBaseEditor(dto);
      if (pEditor == nullptr)
      {
        msg += " no editor: " + dto.objectName;
        return false;
      }

      if (pEditor->DeleteObject((GTWCollectionMember**)&pParent, true))
        return true;
    }
    msg += " could not delete: " + dto.objectName;
    return false;
  }

  // Handler for channel session wizard cancellation - triggers orphaned channel cleanup
  static void ChannelSessionWizardCancelledHandler(EditorCommandDTO& dto, HttpServerPtr pServer, HttpResponsePtr response, HttpRequestPtr request)
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_General, nullptr,
      "Channel session wizard cancelled for: %s", dto.parentObjectName.c_str());

    // Clear wizard in progress flag since user explicitly cancelled
    GTWChannel::ClearWizardInProgressFlag(dto.parentObjectName);

    // Trigger orphaned channel cleanup with UI refresh (user-initiated, don't skip wizard channels)
    GTWChannel::CleanupOrphanedChannels(true, false);

    nlohmann::json res;
    res["result"] = true;
    res["message"] = "Channel session wizard cancellation cleanup completed";
    pServer->BuildOkResponse(response, "application/json", res.dump());
  }
};

