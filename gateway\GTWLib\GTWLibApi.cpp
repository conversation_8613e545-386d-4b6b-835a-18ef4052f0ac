/*****************************************************************************/
/* Triangle MicroWorks, Inc.                        Copyright(c) 1997 - 2019 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWLibApi.cpp                                               */
/* DESCRIPTION:  Implementation of the gateway's library contorl methods     */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com(919) 870 - 6615                  */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#include "stdafx.h"
#include "gateway/GTWOsUtils/simple_timer.h"
#include "GTWLibApi.h"

#include <iostream>
#include "tmwscl/utils/tmwtargp.h"


#include "GTWtarg.h"
#include "gateway/GTWLib/GTWLicenseManager.h"
#include "gateway/GTWLib/GTWInitializer.h"
#include "gateway/GTWOsUtils/GtwLogger.h"
#include "gateway/GTWLib/GTWOpcServerCallBack.h"

#if USE_OPC_44
#include "ServerCommon.h"
#include "gateway/GTWLib/GTWOpcServer.h"
#endif

#if USE_OPC_UA
#include "gateway/GTWLib/OPCUA/OpcUaServer.h"
#include "gateway/GTWLib/GTWOpcUaServer.h"
#endif
#include "../GTWWebLib/HttpClientBase.h"
#include "../GTWWebLib/GtwWebConfig.h"
#include "GTWHttpServer.h"
#include "gateway/GTWWebLib/HttpStatic.h"
#include "tmwscl/utils/tmwcrypto.h"
#include "GTWCryptoWrapper.h"
#include "../GTWSNLicUtils/SNNative.h"
#include "../GTWSNLicUtils/hasp_api_cpp.h"
#include "../GTWWebLib/HttpServerBase.h"
#include "OpcServer.h"
#include "OpcClient.h"
#include "GTWS104Protocol.h"
#include "GTWModbusSlaveProtocol.h"
#include "GTWModbusMasterProtocol.h"
#include "GTWM104Protocol.h"
#include "GTWM101Protocol.h"
#include "GTWM103Protocol.h"
#include "GTWDnpMasterProtocol.h"
#include "../GTWOsUtils/gtw_fstream.h"
#include "../GTWOsUtils/ConfigIntegrityChecker.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

void OnAddLogEntry(GtwLogger::SourceEnum source,
  unsigned int entryID,
  const char* name,
  GtwLogger::SeverityMaskEnum severity,
  unsigned int categoryMask,
  const GtwOsDateTime& timeStamp,
  const char* msg)
{
  if (GtwSysConfig::gtwUseWebSSL())
  {
    if (HttpStatic::pHttpsServer)
      ((GTWHttpServer<SimpleWeb::HTTPS>*)HttpStatic::pHttpsServer)->UpdateLogEntry(source, entryID, name, severity, categoryMask, timeStamp, msg);
  }
  else
  {
    if (HttpStatic::pHttpServer)
      ((GTWHttpServer<SimpleWeb::HTTP>*)HttpStatic::pHttpServer)->UpdateLogEntry(source, entryID, name, severity, categoryMask, timeStamp, msg);
  }
}

void GTWLibApi::OnAddError(GtwLogger::SourceEnum source,
  unsigned int entryID,
  const char* name,
  GtwLogger::SeverityMaskEnum severity,
  unsigned int categoryMask,
  const GtwOsDateTime& timeStamp,
  const char* msg)
{
  if (
    source == GtwLogger::SourceEnum::Source_SDG &&
    severity == GtwLogger::SeverityMaskEnum::Severity_Error &&
    ((categoryMask & GtwLogger::SDGCategoryMaskEnum::SDG_Category_General) == GtwLogger::SDGCategoryMaskEnum::SDG_Category_General)
    )
  {
    GtwLogger::errMsg = GtwLogger::errMsg + " " + msg;
    return;
  }
  return;
}


using namespace std;

ENGINE_EXIT_FAIL_STATE_ENUM StartInitOPCClassicServer()
{
#if USE_OPC_44
  bool opcInitOk = false;
  //// Everyone can connect back to IAdviseSink
  //HRESULT hr = CoInitializeSecurity(NULL,
  //  -1,
  //  NULL,
  //  NULL,
  //  RPC_C_AUTHN_LEVEL_NONE,
  //  RPC_C_IMP_LEVEL_IDENTIFY,
  //  NULL,
  //  EOAC_NONE,
  //  NULL);
  //if (FAILED(hr))
  //{
  //  LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, _T("CoInitializeSecurity failed, %lx\n"), hr);
  //}
  // start Softing specific
  if (gtwlib_IsOpcClassicServerLicensed())
  {
    GTWOpcServerCreate(false);
    if (GTWOpcServerInit("GTWService", "SCADA Data Gateway Protocol Mapping Engine Service") != 0)
    {
      TMWTYPES_ULONG port = GTWConfig::OPCXmlDaServerPort;
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_OPC_SU,"OPC Classic Server", "OPC server init failed.\n\nMake sure the ini parameter OPCXmlDaServerPort(=%u) is set to a port not being used on the system.", port);
      CoUninitialize();
      opcInitOk = false;
      return FAIL_OPC_CLASSIC_START;
    }
    opcInitOk = true;

    if (GTWOpcServerStart() != 0 && opcInitOk)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_OPC_SU,"OPC Classic Server", "%s", "OPC Classic server initialized but failed to start");
      opcInitOk = false;
      return FAIL_OPC_CLASSIC_START;
    }

    GetGTWApp()->CreateOpcServerActiveControl();
  }
  else
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC,"OPC Classic Server", "OPC classic server not started - DA, XML, and AE servers are not licensed.");
  }
  // end Softing specific

  // add the OPCServer to the SDO Collection
  if (opcInitOk == true)
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, "OPC Classic Server", "%s", "Adding SDG OPC Server...");
    GTWChannelCollection* pClctn = GetGTWApp()->getSdoCollection();
    GTWCollectionMember* member;
    if (GetOpcServerCB())
    {
      CStdString memberName = GetOpcServerCB()->GetMemberName();
      if (pClctn->FindCollectionMemberUsingName(memberName, &member) != GTWDEFS_STAT_SUCCESS)
      {
        pClctn->InsertCollectionMember(GetOpcServerCB());
      }
    }
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, "OPC Classic Server", "%s", "OPC Classic Server started");
  }
#endif
  return _SUCCESS;
}

ENGINE_EXIT_FAIL_STATE_ENUM setup(const CStdString& sIniFileName)
{
  tmw::TMWBaseLibrary::StartLibrary();
  tmw61850::TMW61850Library::StartLibrary(false);
  tmwTase2::TMWTase2Library::StartLibrary();

  CStdString appName;
  CStdString csvFileName;
  CStdString wsHashesFile;

  std::filesystem::path p(sIniFileName.c_str());
  csvFileName = p.replace_extension(".csv").string();

  std::filesystem::path iniPath(sIniFileName.c_str());

  // Create workspace directory if needed
  string ws_dir = iniPath.parent_path().string();
  if (!std::filesystem::exists(ws_dir))
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
      "Workspace directory does not exist, creating: %s", ws_dir.c_str());

    try {
      if (!std::filesystem::create_directories(ws_dir))
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
          "Failed to create workspace directory: %s", ws_dir.c_str());
        return FAIL_CREATE_WORKSPACE_DIR;
      }

      LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "Workspace directory created successfully: %s", ws_dir.c_str());
    } catch (const std::exception& e) {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "Failed to create workspace directory: %s - %s", ws_dir.c_str(), e.what());
      return FAIL_CREATE_WORKSPACE_DIR;
    }
  }

  if (std::filesystem::exists(std::string(sIniFileName.c_str())) &&
    std::filesystem::is_directory(std::string(sIniFileName.c_str())) == false)
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
      "Loading INI file: %s", (const char*)sIniFileName);

    TMWParamSubset* localSubset = nullptr;
    std::vector<TMWParamSubset*> subsets;

    // Only process local parameters if redundancy will be enabled
    bool parsingResult = false;
    if (!TMWParam::TMWParam_parse(0, NULL, sIniFileName.c_str(), appName)) {
      return FAIL_INI_LOAD;
    }

    // Check if redundancy is enabled after initial parse
    if (GtwSysConfig::redEnable()) {
      localSubset = new TMWParamSubset();
      std::string localParamsPath = GtwSysConfig::getLocalParamsPath();

      // Generate local params json if it doesn't exist
      if (!std::filesystem::exists(localParamsPath)) {
        TMWParamSubset::GenerateAllLocalSubsets(localParamsPath.c_str());
      }

      if (!localSubset->LoadDefinition(localParamsPath.c_str())) {
        LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_Params, nullptr,
          "Could not load local parameter definitions from %s", localParamsPath.c_str());
        delete localSubset;
        localSubset = nullptr;
      }

      if (localSubset) {
        subsets.push_back(localSubset);
      }

      // Load INI with overrides
      parsingResult = TMWParamManager::LoadWithOverrides(sIniFileName.c_str(), subsets);

      if (localSubset) {
        delete localSubset;
      }
    }
    else {
      // If redundancy is not enabled, consider the initial parse successful
      parsingResult = true;
    }

    if (CStdString(GTWConfig::MigrationStatus) == CStdString("{}") && GTWmain::m_iniLoadedMajorVersion < 5)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup,
        BroadcastTypeEnum::Broadcast_Error,
        GtwLogger::SDG_Category_Start_Stop, nullptr,
        "TR_ERROR_IN_INI_FILE_MIGRATE",
        "Migration of INI file: {{arg1}} needs to be done",
        sIniFileName.c_str());
      return FAIL_INI_LOAD;
    }

    if (!parsingResult)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup,
        BroadcastTypeEnum::Broadcast_Error,
        GtwLogger::SDG_Category_Start_Stop, nullptr,
        "TR_ERROR_IN_INI_FILE_PARSE",
        "Error parsing INI file: {{arg1}}",
        sIniFileName.c_str());
    }

#if !USE_OPC_44
    if (TMWParam::TMWParam_ini_has_opc_param)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup,
        BroadcastTypeEnum::Broadcast_Warning,
        GtwLogger::SDG_Category_Start_Stop, nullptr,
        "TR_WARNING_IN_INI_FILE_PARSE_CLASSIC_OPC",
        "Parsing INI file found unsupported opc classic parameter(s) in file: {{arg1}}, note these parameters will be removed when the workspace is saved",
        sIniFileName.c_str());
    }
#endif
  }
  else
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
      "INI file did not exist, creating new INI file: %s", (const char*)sIniFileName);

    // For new INI files, just do regular parse without overrides
    bool parsingResult = TMWParam::TMWParam_parse(0, NULL, sIniFileName.c_str(), appName);
    if (!parsingResult)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup,
        BroadcastTypeEnum::Broadcast_Error,
        GtwLogger::SDG_Category_Start_Stop, nullptr,
        "TR_ERROR_IN_INI_FILE_PARSE",
        "Error parsing INI file: {{arg1}}",
        sIniFileName.c_str());
      return FAIL_INI_LOAD;
    }
    else
    {
      if (GTWConfig::PointMapFile[0] == 0)
      {
        std::filesystem::path csvPath(csvFileName.c_str());
        GTWConfig::PointMapFile = csvPath.filename().string().c_str();
      }
      if (GTWConfig::PointMapFile[0] != 0)
      {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
          "Saving new CSV file: %s", csvFileName.c_str());
        if (GetGTWApp()->GetPointMap()->savePointData(GTWConfig::PointMapFile, false) == false)
        {
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
            "Save of CSV file:%s failed", csvFileName.c_str());
          return FAIL_SAVE_POINTMAP;
        }
      }
      LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "Saving new INI file: %s", (const char*)sIniFileName);
      TMWParam::TMWParam_write_ini();
    }
  }

  GTWmain::SetIniCsvDirty(false);

  // Initialize time
  GTWmain::initGTWTime();

  // Warn if log timezone (in gtw_config.json) does not match workspace timezone
  CStdString sCurLogTz = GtwWebConfig::gtwLogTimeZone();
  bool bCurLogIgnoreDST = GtwWebConfig::gtwIgnoreTimeZoneDST();
  bool bCurWsIgnoreDST = GTWConfig::IgnoreDST;
  if (sCurLogTz != CStdString((const char*)GTWConfig::TimeZoneName) || bCurLogIgnoreDST != bCurWsIgnoreDST)
  {
    LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_Start_Stop, nullptr, "The GLOBAL log timezone '%s (ignoreDST=%s)' does not match the workspace timezone '%s (ignoreDST=%s)'",
      sCurLogTz.c_str(), (bCurLogIgnoreDST ? "true" : "false"), (const char*)GTWConfig::TimeZoneName, (bCurWsIgnoreDST ? "true" : "false"));
  }

  CStdString msg;
  if (uint32_t pCntLimit = GetGTWApp()->getSdoPointLimit(); pCntLimit == -1)
  {
    msg.Format("SDO Point Count Limit: unlimited");
  }
  else
  {
    msg.Format("SDO Point Count Limit: %d", pCntLimit);
  }
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_License, nullptr, "%s", msg.c_str());

  if (!GetGTWApp()->initBeforeCSV())
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "initBeforeCSV failed. Make sure INI file is correctly configured and in the proper location.");
    return FAIL_INIT_BEFORE_CSV;
  }
  GTWLibApi::DoLogFilterConfigCBfun(GtwLogger::SourceEnum::Source_SDG, GtwSysConfig::sdgCategoryMask(), GtwSysConfig::sdgSeverityMask());
  GetGTWApp()->SetStartupState(GTWSTARTUP_STATE_INI_LOADED);

  //  print some ini file params
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "Using Point Map CSV File: %s", (const char*)GTWConfig::PointMapFile);

  if (ENGINE_EXIT_FAIL_STATE_ENUM exitCode = StartInitOPCClassicServer(); exitCode != 0)
  {
    return exitCode;
  }

  /* if point data mapping file has been specified, then input the mapping data */
  if (GTWConfig::PointMapFile[0] != 0)
  {
    std::string pointMapFileName = GTWmain::GetIniRelativeFullFilePath(GTWConfig::PointMapFile, GtwSysConfig::getCurrentWorkSpacePath());
    try
    {
      if (!GetGTWApp()->GetPointMap()->loadPointData(GTWConfig::PointMapFile))
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_PointMap,
          nullptr, "TR_FAILED_TO_LOAD_POINTMAP", "Failed to load CSV file at: '{{arg1}}'", pointMapFileName.c_str());
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_PointMap, nullptr, "Failed to load CSV file at: %s", (const char*)pointMapFileName.c_str());
        return FAIL_LOAD_POINTMAP;
      }
      else
      {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_PointMap, nullptr, "Successfully loaded CSV file: %s", (const char*)pointMapFileName.c_str());
      }
    }
    catch (...)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_PointMap, nullptr, "Failed to load CSV file at: %s (may not be compatible with the INI file)", (const char*)pointMapFileName.c_str());
      return FAIL_LOAD_POINTMAP;
    }
  }
  GetGTWApp()->SetStartupState(GTWSTARTUP_STATE_CSV_LOADED);
  GetGTWApp()->SCLinit();
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "SCL Initialization Complete");
  GetGTWApp()->initAfterCSV();

#if USE_OPC_UA
  if (GTWOpcUaServer::getLicensed())
  {
    // Load just before SetInitialized so as not to miss any MDOs that need to be added
    // Once initialized, the Uponinsert will add them
    GetGTWApp()->loadOpcUAServerData();
  }
#endif

  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "SDG Initialization Complete\n");

  GetGTWApp()->SetInitialized(true);
  GetGTWApp()->SetStartupState(GTWSTARTUP_STATE_DONE);

  GTWmain::StartCheckForInputTimer();
  GTWmain::StartMdoUpdateRateTimer();
  GTWmain::StartLicenseCheckTimer();
  GTWmain::StartSSICheckTimer();

#if USE_OPC_44
  // allow delayed ready of OPC server
  GTWmain::StartOpcServerReadyTimer(GTWConfig::OpcServerReadyDelay);
  GTWmain::StartOpcServerDelaySendInitialEventsTimer(GTWConfig::OpcAEServerDelaySendInitialEvents + GTWConfig::OpcServerReadyDelay);
#endif

  GetGTWApp()->SetStartingUp(false);

  GTWmain::activateOPCclients();

  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "SDG is running");

#ifdef _DEBUG
#if SIMULATE_INTERNAL_POINT_DATA_CHANGES
  std::thread simulate_data_changes([]()
    {
      GetGTWApp()->SimulateDataChanges();
    }
  );
  simulate_data_changes.detach();
#endif
#endif

  return _SUCCESS;
}

void GTWLibApi::setEngineExitFailState(int retVal)
{
  HttpClientBase::setEngineExitFailState(retVal);
}

void GTWLibApi::sendLogInfo(const std::string &msg)
{
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_General, nullptr, "%s", msg.c_str());
}

/**********************************************************************************\
  Function :			GTWLibApi::EngineStartup
  Description : [none]
  Return :			bool	-
  Parameters :

  Note : [none]
\**********************************************************************************/
ENGINE_EXIT_FAIL_STATE_ENUM GTWLibApi::EngineStartup(APP_TYPE appType, bool isService, bool stopSDG, bool crashSDG, bool promptForINI, bool startMonitor,
  bool startMonitorLocation, const CStdString& monAddress, int monPort, CStdString cmdINIFileName)
{
  try
  {
    GtwWebConfig::LoadGtwConfigUsingMonitor();

    if (CommonApi::OpenDataBases() == false)
    {
      return FAIL_LOAD_DATABASES;
    }

    HttpServerCommon::SetDoSetLogFilterConfigCBfun(DoLogFilterConfigCBfun);
#if _WIN32    
    //WinIoTarg_createTimeZones();
#endif

    SimpleTimer::init();


    tmw::String errorLocation = GtwSysConfig::getSDGLogPath().c_str();
    if (!std::filesystem::exists((const char*)errorLocation))
      std::filesystem::create_directory((const char*)errorLocation);
    errorLocation.append("/TMWError_eng.dbg");

    // No Trace_Information | Trace_CDebug | Trace_Debug
    tmw::TMWBaseLibrary::SetTraceLevelFilter(tmw::TMWBaseLibrary::Trace_Exception | tmw::TMWBaseLibrary::Trace_Error | tmw::TMWBaseLibrary::Trace_Warn);
    tmw::TMWBaseLibrary::SetTraceCategoryFilter(tmw::TMWBaseLibrary::Trace_Category_All);

    tmw::Exception::SetErrorLogLocation((const char*)errorLocation);

    GtwLogger::SetLogType(GtwLogger::Engine);
    GtwLogger::RegisterAddLogEntry(OnAddLogEntry);

    GtwWebConfig::LoadGtwConfigUsingMonitor();
    InitTZ(); // uses TZDIR env var so must be after LoadGtwConfig()

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "Starting GTWEngine: pid=%d", GetPid());

#if TMWCNFG_USE_GATEWAY_DB
    RegisterCryptoFuncs(GTWCrypto_getAlgorithm, GTWCryptoWrapper_getKey, GTWCryptoWrapper_getKeyData);
#endif

    if (!isService && promptForINI)
    {
      if (PromptForINIFile(cmdINIFileName) == false)
      {
        return FAIL_NO_INI_FILE;
      }
    }

    //if (startMonitor && HttpClientBase::IsMonitorRunning() == false)
    //{
    //  HttpClientBase::StartMonitor();
    //  GtwOsSleep(10000);
    //}

    if (!monAddress.IsEmpty())
    {
      if (GtwWebConfig::SetMonitorLocation(monAddress, monPort) == false)
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "invalid monitor location specified");
        return FAIL_INVALID_MONITOR_LOCATION;
      }
    }

    tmwtargp_registerStartTimerFunc(gtwtarg_startTimer);
    tmwtargp_registerCancelTimerFunc(gtwtarg_cancelTimer);
    tmwtargp_registerGetSessionNameFunc(gtwtarg_getSessionName);
    tmwtargp_registerGetSectorNameFunc(gtwtarg_getSectorName);

    GtwSysConfig::SetRunningAsService(isService);
    GtwSysConfig::AppType() = appType;
    InitTZ(); // uses TZDIR env var so must be after LoadGtwConfig()

    tmw::TMWBaseLibrary::SetTraceLevelFilter(GtwSysConfig::t6SeverityMask());
    tmw::TMWBaseLibrary::SetTraceCategoryFilter(GtwSysConfig::t6CategoryMask());

    GetGTWApp()->CreateWorkQueue();

    if (stopSDG)
    {
      LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "send engine stop request");
      HttpClientBase::SendEngineStopRequest();
      LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "sleep for 5 seconds");
      GtwOsSleep(5000);
      LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "kill web monitor");
      killProcessByName("GTWWebMonitor.exe");
      return _SUCCESS;
    }

    CStdString iniFileName = GtwSysConfig::GetEngineINIFullName();

    std::string errMsg;
    VALIDATE_RESULT_ENUM bConfigres = GtwSysConfig::ValidateGtwConfig(errMsg);
    int cnt = 0;
    bool isErrorAlreadyDisplayed = false;
    const int retryCount = 15;
    while (bConfigres == VALIDATE_RESULT_ERROR)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Status_Bar, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
        "TR_ENG_STARTUP_ERROR_TIMEOUT", "Engine Startup Error: {{arg1}} (retry-cnt = {{arg2}}/{{arg3}})", errMsg.c_str(), std::to_string(cnt).c_str(), std::to_string(retryCount).c_str());
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Monitor, nullptr, "Engine Startup Error: %s (retry-cnt = %d/%d)", errMsg.c_str(), cnt, retryCount);

      GtwOsSleep(1000);
      bConfigres = GtwSysConfig::ValidateGtwConfig(errMsg);
      if (bConfigres != VALIDATE_RESULT_ERROR)
      {
        break;
      }
      if (cnt >= retryCount)
      {
        break;
      }
      ++cnt;
    }

    switch (bConfigres)
    {
    case VALIDATE_RESULT_SUCCESS:
      LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "Validate %s Success", GtwSysConfig::getConfigFileName());
      break;
    case VALIDATE_RESULT_NOT_DONE:
      LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Monitor, nullptr, "Validate %s Not done (disabled in %s)", GtwSysConfig::getConfigFileName(), GtwSysConfig::getConfigFileName());
      break;
    case VALIDATE_RESULT_WARNING:
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "Validate %s Warning: %s", GtwSysConfig::getConfigFileName(), errMsg.c_str());
      break;
    case VALIDATE_RESULT_ERROR:
      {
	      CStdString err = errMsg;
	      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr,
	        "TR_ENG_STARTUP_ERROR", "Engine Startup error: {{arg1}}.  Please contact Triangle Microworks customer support.", err.c_str());
	      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "Engine Startup error: %s", err.c_str());
        printf("Engine validate %s Error: %s\n", GtwSysConfig::getConfigFileName(), errMsg.c_str());
        return FAIL_VALIDATE;
      }
    case VALIDATE_RESULT_FAIL:
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Monitor, nullptr, "Engine validate %s Failure: %s", GtwSysConfig::getConfigFileName(), errMsg.c_str());
      printf("Engine validate %s Failure: %s\n", GtwSysConfig::getConfigFileName(), errMsg.c_str());
      return FAIL_VALIDATE;
    }

    if (ENGINE_EXIT_FAIL_STATE_ENUM rv = GetGTWApp()->StartHttpServer(errMsg); rv != _SUCCESS)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "Make sure no other application is using the web server port, use the SDG config tool to correct. Failed to start Engine web server on: %s  Additional Info:", GtwSysConfig::GetSDGHostAddr().c_str(), errMsg.c_str());
      return rv;
    }

#if USE_LICENSE
    // Initialize license manager
    CStdString appDir = getExecutablePath().string();
    CStdString appName = appDir + "/" + "tmwgtway.exe";
    GTWLicenseManager::InitLicManager(appName, nullptr);
    if (GTWLicenseManager::Get(nullptr)->GetIsLicensed() == false)
    {
      if (hasp_status_t status = ::UpdateKey2(GtwSysConfig::GetTrialLicenseString().c_str()); status != HASP_STATUS_OK && status != HASP_UPDATE_ALREADY_ADDED)
      {
        if (!isErrorAlreadyDisplayed)
        {
          GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_License, nullptr,
            "TR_LICENSE_ERROR", "License Manager error: {{arg1}}.  Please contact Triangle Microworks customer support.", "SCADA Data Gateway is not licensed - failed to create a trial license");
          isErrorAlreadyDisplayed = true; // we only want the popup once CR#19035
        }

        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_License, nullptr, "%s", "SDG Not licensed");
        return FAIL_CREATE_TRIAL_LICENSE;
      }

      if (GTWLicenseManager::Get(nullptr)->GetIsLicensed() == false)
      {
        // trial failed
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_License, nullptr,
          "TR_LICENSE_ERROR", "License Manager error: {{arg1}}.  Please contact Triangle Microworks customer support.", "SCADA Data Gateway is not licensed");
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_License, nullptr, "%s", "Not licensed");
       // return EXIT_FAIL_NO_LICENSE;
      }
    }
#endif


    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "SDG starting");
    if (!cmdINIFileName.IsEmpty())
    {
      iniFileName = cmdINIFileName;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "Loading INI file: %s", iniFileName.c_str());

    if (crashSDG)
    {
      GTWmain::crashSDG("");
    }

    if (ENGINE_EXIT_FAIL_STATE_ENUM err = setup(iniFileName); err != _SUCCESS)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "Error: Failed to start, check logs");
      return err;
    }

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Start_Stop, nullptr, "Running: %s", iniFileName.c_str());

    //GTWConfig::OpcUaSecurityCertificateAuthorityTrustDir = GtwSysConfig::getOPCUAServerTrustedCertificatePath().c_str();
    //GTWConfig::OpcUaSecurityCertificateAuthorityRejectDir = GtwSysConfig::getOPCUAServerRejectedCertificatePath().c_str();
    
    if (GtwSysConfig::redEnable() == true)
    {
      GetGTWApp()->setChannelActiveControlMdos(false);
    }

    // make sure this is called last
    GTWmain::SetIniCsvDirty(false);

  }
  catch (std::exception const& exc)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "std::Exception caught: %s", exc.what());
    return FAIL_EXCEPTION;
  }
  catch (tmw::Exception const& exc)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "tmw::Exception caught: %s", exc.getCharErrorMessageBlock());
    return FAIL_EXCEPTION;
  }
  catch (...)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "Severe exception caught");
    return FAIL_EXCEPTION;
  }

  HttpServerCommon::setEngineStarted(true);

  nlohmann::json args = {};
  args["refreshWebBrowser"] = true;
  BroadcastEventDTOMsg dto(DestinationMaskEnum::Destination_Command, BroadcastTypeEnum::Broadcast_Refresh_UI, "", "", nullptr, args);
  HttpServerCommon::_SendWebTextMessage(dto);

  GtwLogger::EnableStartupMessages(false);

  return(_SUCCESS);
}

void GTWLibApi::SetShuttingDown(std::string token)
{
  GetGTWApp()->SetShuttingDown(true, token);
}

/**********************************************************************************\
  Function :			GTWLibApi::EngineShutdown
  Description : [none]
  Return :			bool	-
  Parameters :

  Note : [none]
\**********************************************************************************/
ENGINE_EXIT_FAIL_STATE_ENUM GTWLibApi::EngineShutdown(bool failedToInit)
{
  try
  {
    if (!failedToInit)
    {
      GTWmain::ShutdownAppCommon();
#if USE_OPC_44
      // stop delayed ready of OPC server
      GTWmain::StopOpcServerReadyTimer();
      GTWmain::StopOpcServerDelaySendInitialEventsTimer();
#endif
      //SimpleTimer::destroy();
      GTWInitializer::destroy();
      //deleteProtocolStatics();
      GtwTimeZone::destroy();
      GtwTimer::DestroyAllTimerManagers();
    }
    else
    {
#if USE_LICENSE      
      SimpleTimer::stop_io();
      GTWLicenseManager::Shutdown();
      SimpleTimer::destroy();
#endif
    }
#if _WIN32    
    //WinIoTarg_cleanTimeZones();
    CoUninitialize();
#endif
  }
  catch (std::exception const& exc)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "EngineShutdown: std::Exception caught: %s", exc.what());
    GtwLogger::Destroy();
    return FAIL_EXCEPTION;
  }
  catch (tmw::Exception const& exc)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "EngineShutdown: tmw::Exception caught: %s", exc.getCharErrorMessageBlock());
    GtwLogger::Destroy();
    return FAIL_EXCEPTION;
  }
  catch (...)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Start_Stop, nullptr, "EngineShutdown: %s", "Severe exception caught");
    GtwLogger::Destroy();
    return FAIL_EXCEPTION;
  }

  GtwLogger::Destroy();
  CommonApi::CloseDataBases();
  
  // Clean up the MimeTypes singleton
  MimeTypes::cleanup();

  return(_SUCCESS);
}

void GTWLibApi::deleteProtocolStatics()
{
  if (GTWS104Protocol::gtws4pcl_protocol != nullptr)
  {
    delete GTWS104Protocol::gtws4pcl_protocol;
    GTWS104Protocol::gtws4pcl_protocol = nullptr;
  }
  if (GTWS101Protocol::gtws1pcl_protocol != nullptr)
  {
    delete GTWS101Protocol::gtws1pcl_protocol;
    GTWS101Protocol::gtws1pcl_protocol = nullptr;
  }
  if (GTWModbusSlaveProtocol::GTWModbusSlaveProtocol_protocol != nullptr)
  {
    delete GTWModbusSlaveProtocol::GTWModbusSlaveProtocol_protocol;
    GTWModbusSlaveProtocol::GTWModbusSlaveProtocol_protocol = nullptr;
  }
  if (GTWModbusMasterProtocol::GTWModbusMasterProtocol_protocol != nullptr)
  {
    delete GTWModbusMasterProtocol::GTWModbusMasterProtocol_protocol;
    GTWModbusMasterProtocol::GTWModbusMasterProtocol_protocol = nullptr;
  }
  if (GTWM104Protocol::gtwm4pcl_protocol != nullptr)
  {
    delete GTWM104Protocol::gtwm4pcl_protocol;
    GTWM104Protocol::gtwm4pcl_protocol = nullptr;
  }
  if (GTWM101Protocol::gtwm1pcl_protocol != nullptr)
  {
    delete GTWM101Protocol::gtwm1pcl_protocol;
    GTWM101Protocol::gtwm1pcl_protocol = nullptr;
  }
  if (GTWM103Protocol::GTWM103Protocol_protocol != nullptr)
  {
    delete GTWM103Protocol::GTWM103Protocol_protocol;
    GTWM103Protocol::GTWM103Protocol_protocol = nullptr;
  }
  if (GTWDnpSlaveProtocol::GTWDnpSlaveProtocol_protocol != nullptr)
  {
    delete GTWDnpSlaveProtocol::GTWDnpSlaveProtocol_protocol;
    GTWDnpSlaveProtocol::GTWDnpSlaveProtocol_protocol = nullptr;
  }
  if (GTWDnpMasterProtocol::GTWDnpMasterProtocol_protocol != nullptr)
  {
    delete GTWDnpMasterProtocol::GTWDnpMasterProtocol_protocol;
    GTWDnpMasterProtocol::GTWDnpMasterProtocol_protocol = nullptr;
  }
}

void GTWLibApi::SettingsShutdown()
{
  GTWmain::ShutdownAppCommon();
  GTWInitializer::destroy();
  //deleteProtocolStatics();
  GTWProtocol_Cleanup();
}
/**********************************************************************************\
  Function :			GTWLibApi::IsShuttingDown
  Description : [none]
  Return :			bool	-
  Parameters :

  Note : [none]
\**********************************************************************************/
bool GTWLibApi::IsShuttingDown()
{
  return GetGTWApp()->IsShuttingDown();
}

bool GTWLibApi::CanShutdown()
{
  if (!GtwSysConfig::gtwDoAuth())
  {
    return true;
  }

  std::string token = GetGTWApp()->GetShutdownToken();
  if (token == std::string(TmwCrypto::UP_NONE_AUTH_TOKEN()))
  {
    return true;
  }

  if (token != "")
  {
    USER_PERMISSION_MASK userPermissions;
    std::string userRole;
    std::string userName;
    if (HttpClientBase::IsTokenValidRequest(token, userPermissions, userRole, userName))
    {
      if ((userPermissions & UP_RW_CONFIG) != 0)
      {
        return true;
      }
    }
  }

  GetGTWApp()->SetShuttingDown(false, "");
  return false;
}


ENGINE_EXIT_FAIL_STATE_ENUM GTWLibApi::OpcServerRegistration(CStdString cmdLine, const char* serviceName, const char* serviceDescription)
{
#if USE_OPC_44

  // register as GTWEngine classic opc server
  GTWOpcServerCreate(true);
  if (GTWOpcServerInit(serviceName, serviceDescription))
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "GTWLibApi::OpcServerRegistration OPC server init failed\n");
    return FAIL_OPC_CLASSIC_START;
  }

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Start_Stop, nullptr, " GTWLibApi::OpcServerRegistration: %s", cmdLine.c_str());
  if (GTWOpcServerProcCmdLine((tstring)cmdLine) == 1)
  {
    if (cmdLine.Find("-regopcserver") != -1)
    {
      printf("GTWLibApi::OpcServerRegistration OPC Server Registration done\n");
      GTWOpcServerDestroy();
      CoUninitialize();
      return _SUCCESS;
    }
    if (cmdLine.Find("-unregopcserver") != -1)
    {
      printf("GTWLibApi::OpcServerRegistration OPC Server UnRegistration done\n");
      GTWOpcServerDestroy();
      CoUninitialize();
      return _SUCCESS;
    }
  }
  printf("GTWLibApi::OpcServerRegistration Nothing done... specify -regopcserver or -unregopcserver on command line\n");
  GTWOpcServerDestroy();
  CoUninitialize();
#endif
  return FAIL_REG_OPC;
}


void GTWLibApi::DoLogFilterConfigCBfun(GtwLogger::SourceEnum source, int categoryMask, int severityMask)
{

#if USE_OPC_44
  if (source == GtwLogger::SourceEnum::Source_SDG)
  {
    if ((GtwLogger::SDG_Category_OPC_DEEP & categoryMask) != 0)
    {
      if ((GTWOPCClient::IsClientLicensed() || GTWOPCAEClient::GetLicensed()) && (GTWOpcServerCallBack::IsLicensed() || GTWmain::getOPCAEServerLicensed()))
      {
        OpcServer::EnableSoftingOPCTraceToFile(true); // enables OPC classic server file logging to HttpServerBase::GetSDGDataPath() + "\\sdg_opc_softingserver_log
      }
      if ((GTWOPCClient::IsClientLicensed() || GTWOPCAEClient::GetLicensed()) && !GTWOpcServerCallBack::IsLicensed() && !GTWmain::getOPCAEServerLicensed())
      {
        OpcClient::EnableSoftingOPCTraceToFile(true); // enables OPC classic server file logging to HttpServerBase::GetSDGDataPath() + "\\sdg_opc_softingclient_log"
      }
    }
    else
    {
      if ((GTWOPCClient::IsClientLicensed() || GTWOPCAEClient::GetLicensed()) && (GTWOpcServerCallBack::IsLicensed() || GTWmain::getOPCAEServerLicensed()))
      {
        OpcServer::EnableSoftingOPCTraceToFile(false); // enables OPC classic server file logging to HttpServerBase::GetSDGDataPath() + "\\sdg_opc_softingserver_log
      }
      if ((GTWOPCClient::IsClientLicensed() || GTWOPCAEClient::GetLicensed()) && !GTWOpcServerCallBack::IsLicensed() && !GTWmain::getOPCAEServerLicensed())
      {
        OpcClient::EnableSoftingOPCTraceToFile(false); // enables OPC classic server file logging to HttpServerBase::GetSDGDataPath() + "\\sdg_opc_softingclient_log"
      }
    }
  }
#endif
}

bool GTWLibApi::GetMonConfig(const std::string& monitor, nlohmann::json& config)
{
  HttpClientResponse response;
  int err_code;
  if (bool bOK = HttpClientBase::MakeRequest(GtwSysConfig::gtwUseLocalHostForEngineAndMonitorComms() == false ? monitor : GtwSysConfig::localHost(), "GET", "/rest/get_config", "", "", 20, response, err_code); bOK && response.status_code == "200 OK")
  {
    config = nlohmann::json::parse(response.content);
    return true;
  }
  return false;
}
bool GTWLibApi::PutMonConfig(const std::string& monitor, nlohmann::json& config)
{
  HttpClientResponse response;
  int err_code;
  if (bool bOK = HttpClientBase::MakeRequest(GtwSysConfig::gtwUseLocalHostForEngineAndMonitorComms() == false ? monitor : GtwSysConfig::localHost(), "POST", "/rest/set_config_json?restartFlag=true&validateFlag=true", config.dump(), "", 20, response, err_code); bOK && response.status_code == "200 OK")
  {
    return true;
  }
  return false;
}

bool GTWLibApi::GetLocalConfig(nlohmann::json& config, std::string& message)
{
  std::filesystem::path myFile = std::filesystem::path(GtwSysConfig::getConfigPath()) / std::filesystem::path(GtwSysConfig::getConfigFileName());

  if (std::filesystem::exists(myFile.string().c_str()))
  {
    try
    {
      std::filesystem::path myFile = std::filesystem::path(GtwSysConfig::getConfigPath()) / std::filesystem::path(GtwSysConfig::getConfigFileName());

      if (std::filesystem::exists(myFile.string().c_str()))
      {
        sdg_os_utils::gtw_ifstream configStream(myFile.string());
        try
        {
          config = nlohmann::json::parse(configStream);
        }
        catch (std::exception& e)
        {
          message = e.what();
          return false;
        }
        configStream.close();
      }
      else
      {
        HttpServerCommon::genConfig(config, true, false);
      }

    }
    catch (std::exception& e)
    {
      message = e.what();
      return false;
    }
  }

  return true;
}


bool GTWLibApi::PutLocalConfig(nlohmann::json& config, std::string& message)
{
  std::filesystem::path myFile = std::filesystem::path(GtwSysConfig::getConfigPath()) / std::filesystem::path(GtwSysConfig::getConfigFileName());

  try 
  {
    sdg_os_utils::gtw_ofstream configStream(myFile.string());
    configStream << config.dump(2);
    configStream.close();
  }
  catch (std::exception const& e) {
    message = e.what();
    return false;
  }

  return true;
}


bool GTWLibApi::generateX509(const std::string& certFileName, const std::string& keyFileName,
  long daysValid,
  long bits_length,
  const std::string& country,
  const std::string& company,
  const std::string& common_name,
  const std::string& email,
  const std::string& hostname
)
{
  bool result = false;

  std::unique_ptr<BIO, void (*)(BIO*)> certFile{ BIO_new_file(certFileName.data(), "wb"), BIO_free_all };
  std::unique_ptr<BIO, void (*)(BIO*)> keyFile{ BIO_new_file(keyFileName.data(), "wb"), BIO_free_all };

  if (certFile && keyFile)
  {
    std::unique_ptr<RSA, void (*)(RSA*)> rsa{ RSA_new(), RSA_free };
    std::unique_ptr<BIGNUM, void (*)(BIGNUM*)> bn{ BN_new(), BN_free };

    BN_set_word(bn.get(), RSA_F4);
    int rsa_ok = RSA_generate_key_ex(rsa.get(), bits_length, bn.get(), nullptr);

    if (rsa_ok == 1)
    {
      // --- cert generation ---
      std::unique_ptr<X509, void (*)(X509*)> cert{ X509_new(), X509_free };
      std::unique_ptr<EVP_PKEY, void (*)(EVP_PKEY*)> pkey{ EVP_PKEY_new(), EVP_PKEY_free };

      // The RSA structure will be automatically freed when the EVP_PKEY structure is freed.
      EVP_PKEY_assign(pkey.get(), EVP_PKEY_RSA, reinterpret_cast<char*>(rsa.release()));
      ASN1_INTEGER_set(X509_get_serialNumber(cert.get()), 1); // serial number

      X509_gmtime_adj(X509_get_notBefore(cert.get()), 0); // now
      X509_gmtime_adj(X509_get_notAfter(cert.get()), daysValid * 24 * 3600); // accepts secs

      X509_set_pubkey(cert.get(), pkey.get());

      // 1 -- X509_NAME may disambiguate with wincrypt.h
      // 2 -- DO NO FREE the name internal pointer
      X509_name_st* name = X509_get_subject_name(cert.get());

      if (country != "")
      {
        X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, (const unsigned char*)country.c_str(), -1, -1, 0);
      }
      if (company != "")
      {
        X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, (const unsigned char*)company.c_str(), -1, -1, 0);
      }
      if (common_name != "")
      {
        X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, (const unsigned char*)common_name.c_str(), -1, -1, 0);
      }
      if (email != "")
      {
        X509_NAME_add_entry_by_txt(name, "emailAddress", MBSTRING_ASC, (const unsigned char*)email.c_str(), -1, -1, 0);
      }


      std::string sn = http_user_db::genSalt();

      if (sn != "")
      {
        X509_NAME_add_entry_by_txt(name, "serialNumber", MBSTRING_ASC, (const unsigned char*)sn.c_str(), -1, -1, 0);
      }

      X509_set_issuer_name(cert.get(), name);
      X509_sign(cert.get(), pkey.get(), EVP_sha256()); // some hash type here


      int ret = PEM_write_bio_PrivateKey(keyFile.get(), pkey.get(), nullptr, nullptr, 0, nullptr, nullptr);
      int ret2 = PEM_write_bio_X509(certFile.get(), cert.get());

      result = (ret == 1) && (ret2 == 1); // OpenSSL return codes
    }
  }

  return result;
}


// Add Subject Alternative Name (SAN) extension does not work needs fixing


//bool GTWLibApi::generateX509(const std::string& certFileName, const std::string& keyFileName,
//  long daysValid,
//  long bits_length,
//  const std::string& country,
//  const std::string& company,
//  const std::string& common_name,
//  const std::string& email,
//  const std::string& hostname
//)
//{
//  bool result = false;
//  std::unique_ptr<BIO, void (*)(BIO*)> certFile{ BIO_new_file(certFileName.data(), "wb"), BIO_free_all };
//  std::unique_ptr<BIO, void (*)(BIO*)> keyFile{ BIO_new_file(keyFileName.data(), "wb"), BIO_free_all };
//  if (certFile && keyFile)
//  {
//    std::unique_ptr<RSA, void (*)(RSA*)> rsa{ RSA_new(), RSA_free };
//    std::unique_ptr<BIGNUM, void (*)(BIGNUM*)> bn{ BN_new(), BN_free };
//    BN_set_word(bn.get(), RSA_F4);
//    int rsa_ok = RSA_generate_key_ex(rsa.get(), bits_length, bn.get(), nullptr);
//    if (rsa_ok == 1)
//    {
//      std::unique_ptr<X509, void (*)(X509*)> cert{ X509_new(), X509_free };
//      std::unique_ptr<EVP_PKEY, void (*)(EVP_PKEY*)> pkey{ EVP_PKEY_new(), EVP_PKEY_free };
//      EVP_PKEY_assign(pkey.get(), EVP_PKEY_RSA, reinterpret_cast<char*>(rsa.release()));
//      ASN1_INTEGER_set(X509_get_serialNumber(cert.get()), 1);
//      X509_gmtime_adj(X509_get_notBefore(cert.get()), 0);
//      X509_gmtime_adj(X509_get_notAfter(cert.get()), daysValid * 24 * 3600);
//      X509_set_pubkey(cert.get(), pkey.get());
//
//      X509_name_st* name = X509_get_subject_name(cert.get());
//      if (!country.empty())
//      {
//        X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, (const unsigned char*)country.c_str(), -1, -1, 0);
//      }
//      if (!company.empty())
//      {
//        X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, (const unsigned char*)company.c_str(), -1, -1, 0);
//      }
//      if (!common_name.empty())
//      {
//        X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, (const unsigned char*)common_name.c_str(), -1, -1, 0);
//      }
//      if (!email.empty())
//      {
//        X509_NAME_add_entry_by_txt(name, "emailAddress", MBSTRING_ASC, (const unsigned char*)email.c_str(), -1, -1, 0);
//      }
//      std::string sn = http_user_db::genSalt();
//      if (!sn.empty())
//      {
//        X509_NAME_add_entry_by_txt(name, "serialNumber", MBSTRING_ASC, (const unsigned char*)sn.c_str(), -1, -1, 0);
//      }
//      X509_set_issuer_name(cert.get(), name);
//
//      // Add X509v3 extensions
//      std::unique_ptr<X509V3_CTX, void(*)(X509V3_CTX*)> ctx{ new X509V3_CTX, [](X509V3_CTX* p) { delete p; } };
//      X509V3_set_ctx_nodb(ctx.get());
//      X509V3_set_ctx(ctx.get(), cert.get(), cert.get(), nullptr, nullptr, 0);
//
//      // Add Subject Alternative Name (SAN) extension
//      if (!hostname.empty())
//      {
//        std::string san_string = "DNS:" + hostname;
//        std::unique_ptr<X509_EXTENSION, void(*)(X509_EXTENSION*)> ext{
//          X509V3_EXT_conf_nid(nullptr, ctx.get(), NID_subject_alt_name, san_string.c_str()),
//          X509_EXTENSION_free
//        };
//        if (ext)
//        {
//          X509_add_ext(cert.get(), ext.get(), -1);
//        }
//      }
//
//      X509_sign(cert.get(), pkey.get(), EVP_sha256());
//      int ret = PEM_write_bio_PrivateKey(keyFile.get(), pkey.get(), nullptr, nullptr, 0, nullptr, nullptr);
//      int ret2 = PEM_write_bio_X509(certFile.get(), cert.get());
//      result = (ret == 1) && (ret2 == 1);
//    }
//  }
//  return result;
//}

