// GTWODBCQueryEditor.cpp: implementation of the GTWODBCQueryEditor class.
//
//////////////////////////////////////////////////////////////////////
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

#include "GTWODBCQueryEditor.h"
#include "gateway/GTWLib/WinEventManager.h"
#include "gateway/GTWLib/GTWODBCClient.h"
#include "gateway/GTWLib/GTWODBCQuery.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

GTWODBCQueryEditor::GTWODBCQueryEditor(const EditorCommandDTO &dto, GTWCollectionMember *pEditableObject, GTWODBCClient *pODBCClient,TMWTYPES_USHORT clientIndex, bool bAddMode)
:
GTWBaseEditor(dto, pEditableObject,bAddMode,-1,-1,-1)
{
  m_bQueryChanged = false;
  m_pODBCClient = pODBCClient;
  m_iClientIndex = clientIndex;
  getQueryIndex();
  GetDefaults();
}

GTWODBCQueryEditor::~GTWODBCQueryEditor()
{

}

void GTWODBCQueryEditor::GetDefaults()
{
  m_sQueryAliasName = GTWConfig::ODBCQueryAliasName.TMWParam_get_default_string();
  m_sQuery = GTWConfig::ODBCQuery.TMWParam_get_default_string();
  m_bODBCQueryAlwaysRefreshRS = GTWConfig::ODBCQueryAlwaysRefreshRS.TMWParam_bool_get_default_value();
}

TMWTYPES_BOOL GTWODBCQueryEditor::BuildSelPopupMenu(int *id,CMenuEntryArray *pMenuEntries)
{
  GTWTYPES_PROTOCOL protocol = GetProtocol();
  CMenuEntry menuEntry1("Edit ODBC Query","Edit a Gateway ODBC Query","Edit ODBC Query text",GetEditableObject(),++(*id),MENU_CMD_EDIT);
  pMenuEntries->push_back(menuEntry1);

  CMenuEntry menuEntry2("Delete ODBC Query","Delete a Gateway ODBC Query","Delete ODBC Query text",GetEditableObject(),++(*id),MENU_CMD_DELETE);
  pMenuEntries->push_back(menuEntry2);

  //CMenuEntry menuEntry3("Add Data Type","Add a Data Type","Add Data Type text",GetEditableObject(),++(*id),MENU_CMD_ADD_DATA_TYPE);
  //pMenuEntries->push_back(menuEntry3);

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::MiscCommand(MENUENTRY_EDIT_CMD cmd)
{
  return TMWDEFS_FALSE;
}

void GTWODBCQueryEditor::DeleteINIparms()
{
  GTWODBCQuery *pODBCQuery = (GTWODBCQuery*)GetEditableObject();

  GetDefaults();
  SaveObject();

  GTWConfig::ODBCQueryAliasName.TMWParam_mark_element_as_not_being_specified(GetClientIndex(),GetQueryIndex());
  GTWConfig::ODBCQuery.TMWParam_mark_element_as_not_being_specified(GetClientIndex(),GetQueryIndex());
}

TMWTYPES_BOOL GTWODBCQueryEditor::DeleteObject(GTWCollectionMember **pRefreshMember, bool bAskIfOk /*= true*/)
{
  GTWODBCQuery* pODBCQuery = (GTWODBCQuery*)GetEditableObject();
  GTWCollectionBase* pClctn = pODBCQuery->GetParentCollection();
  if (pClctn->RemoveCollectionMember(pODBCQuery))
  {
    DeleteINIparms();

    pODBCQuery->DeleteCollectionMember();
    
    return TMWDEFS_TRUE;
  }
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::AddQuery(GTWODBCClient *pODBCClient, BOOL bCreateTags)
{
  GTWCollectionBase *pClctn;

  pClctn = m_pODBCClient->GetMemberCollection();
  GTWODBCQuery *pQuery; 
  GTWDEFS_STAT stat = GTWODBCQuery::CreateQuery(m_pODBCClient->getObIndex(), GetQueryIndex(), &pQuery);
  if (stat == GTWDEFS_STAT_SUCCESS)
  {
    SetEditableObject(pQuery);
    //pQuery->SetBaseEditor(this);

    if (pClctn->InsertCollectionMember(GetEditableObject()) == false)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(), "TR_ODBC_QUERY_ADD_DUPLICATE_ERROR", "The SQL query could not be added. (duplicate?)");
      return TMWDEFS_FALSE;
    }
    return TMWDEFS_TRUE;
  }

  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::AddObject(MENUENTRY_EDIT_CMD cmd)
{
  return TMWDEFS_TRUE;
}

CStdString GTWODBCQueryEditor::GetDescription(void)
{
  CStdString str;
  str.Format("Query = %s,AliasName = %s",m_sQuery,m_sQueryAliasName);
  return str;
}

TMWTYPES_BOOL GTWODBCQueryEditor::EditObject()
{
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::ModifyObject(void)
{
  GTWODBCQuery *pODBCQuery = (GTWODBCQuery*)GetEditableObject();

  if (pODBCQuery && pODBCQuery->IsA("GTWODBCQuery"))
  {
    SaveObject();
    if (m_bQueryChanged == true)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(), "TR_ODBC_QUERY_CHANGE", "The SQL query has changed; as a result query tags(MDOs) may be re-created and any mappings may be lost");
      //pODBCQuery->RemoveODBCQueryTags(true);
      pODBCQuery->CreateODBCQueryTags(true);
    }
  }
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::SaveObject()
{
  TMWTYPES_USHORT index = 0;
  if (getAddMode())
  {
    index = GetNextQueryIndex();
  }
  else
  {
    index = GetQueryIndex();
  }

  GTWConfig::ODBCQueryAliasName.SetAt((TMWTYPES_USHORT)(GTKTPARM_CLIENT_QUERY_INDEX(GetClientIndex(),index)), m_sQueryAliasName);
  GTWConfig::ODBCQuery.SetAt((TMWTYPES_USHORT)(GTKTPARM_CLIENT_QUERY_INDEX(GetClientIndex(), index)), m_sQuery);
  GTWConfig::ODBCQueryAlwaysRefreshRS[(TMWTYPES_USHORT)(GTKTPARM_CLIENT_QUERY_INDEX(GetClientIndex(), index))] = m_bODBCQueryAlwaysRefreshRS;

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::LoadObject()
{
  m_sQueryAliasName = GTWConfig::ODBCQueryAliasName(GetClientIndex(),GetQueryIndex());
  m_sQuery = GTWConfig::ODBCQuery(GetClientIndex(),GetQueryIndex());
  m_bODBCQueryAlwaysRefreshRS = GTWConfig::ODBCQueryAlwaysRefreshRS(GetClientIndex(), GetQueryIndex());

  return TMWDEFS_TRUE;
}
TMWTYPES_BOOL GTWODBCQueryEditor::WebReadObject(nlohmann::json &pt, bool bEditAtRuntime)
{
  GTWBaseEditor::WebReadObject(pt, bEditAtRuntime);

  if (!m_bAddMode && LoadObject() == TMWDEFS_FALSE)
    return false;

  bool isReadOnly = !m_bAddMode;
  bool readOnly = true;

  try
  {
    nlohmann::json schema;
    nlohmann::json children;

    AddTMWParamEditorField(schema, children, &GTWConfig::ODBCQueryAliasName,
      m_sQueryAliasName,
      (EDITOR_CONTROL_TYPE::TEXT),
      "",
      true, "", "", &isReadOnly
    );

    AddTMWParamEditorField(schema, children, &GTWConfig::ODBCQuery,
      m_sQuery,
      (EDITOR_CONTROL_TYPE::TEXT_MULTI_LINE),
      "",
      true
    );

    AddTMWParamEditorField(schema, children, &GTWConfig::ODBCQueryAlwaysRefreshRS,
      std::to_string(m_bODBCQueryAlwaysRefreshRS),
      (EDITOR_CONTROL_TYPE::CHECKBOX),
      "",
      false
    );

    AddHiddenEditorField(schema, children,
      "ODBCClient",
      m_pODBCClient->GetFullName()
    );

    AddHiddenEditorField(schema, children,
                         "objectName",
                         m_dto.objectName
    );

    AddEditorField(schema, children,
      "tableList",
      "TR_TABLE_LIST",
      "Table List",
      "",
      "TR_TABLE_LIST_DESC",
      "Specifies the list of tables",
      (EDITOR_CONTROL_TYPE::COMBOBOX),
      ListTables(false, false).dump(),
      false,
      "",
      "",
      GTW_RESOURCE_UI_ID,
      ""
    );

    AddTMWParamEditorField(schema, children, &GTWConfig::ODBCConnectionString,
      m_pODBCClient->GetConnectionString(),
      (EDITOR_CONTROL_TYPE::TEXT_MULTI_LINE),
      "",
      true, "", "", &readOnly
    );

    AddEditorField(schema, children,
      "tableInfo",
      "TR_TABLE_INFO",
      "Table Info",
      "",
      "TR_TABLE_INFO_DESC",
      "Specifies the table information",
      (EDITOR_CONTROL_TYPE::GRID),
      "",
      false,
      "",
      "",
      GTW_RESOURCE_UI_ID,
      ""
    );

    AddEditorField(schema, children,
      "executeSql",
      "TR_EXECUTE_SQL",
      "Execute/Test Sql Query",
      "",
      "TR_EXECUTE_SQL_DESC",
      "Execute/Test Sql Query",
      (EDITOR_CONTROL_TYPE::ACTION),
      "",
      false,
      "",
      "",
      GTW_RESOURCE_UI_ID,
      ""
    );

    AddEditorField(schema, children,
      "queryResults",
      "TR_QUERY_RESULTS",
      "Query Results",
      "",
      "TR_QUERY_RESULT_DESC",
      "Specifies the Query Results",
      (EDITOR_CONTROL_TYPE::GRID),
      "",
      false,
      "",
      "",
      GTW_RESOURCE_UI_ID,
      ""
    );

    std::string json = schema.dump();
    pt["objectDataJson"] = json;
    pt["editorType"] = "GTWODBCQueryEditor";
    pt += nlohmann::json::object_t::value_type("children", children);
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_ODBC, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::ValidateObject()
{
  
  if (m_sQueryAliasName.Find('.') != -1)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Editor, GetDTOToken(), "TR_ODBC_QUERY_NO_PERIOD", "The ODBC Query name can not have a '.' in it.");
    return TMWDEFS_FALSE;
  }

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWODBCQueryEditor::UpdateObject()
{
  try
  {
    m_sQueryAliasName = m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::ODBCQueryAliasName)].get<std::string>().c_str();
    if (m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::ODBCQuery)].get<std::string>() != GTWConfig::ODBCQuery(GetClientIndex(),GetQueryIndex()))
    {
      m_bQueryChanged = true;
    }
    m_sQuery = m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::ODBCQuery)].get<std::string>().c_str();
    m_bODBCQueryAlwaysRefreshRS = GetBoolValueFromString(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::ODBCQueryAlwaysRefreshRS)].get<std::string>().c_str());

    if (!ValidateObject())
    {
      return TMWDEFS_FALSE;
    }
    if (getAddMode() == TMWDEFS_FALSE)
    {
      ModifyObject();
    }
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_ODBC, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

nlohmann::json GTWODBCQueryEditor::TryListTables(bool bViews, bool bSystemTables)
{
  CStdString names;
  nlohmann::json ListTablesjson;

  if (!m_pODBCClient->GetDbPtr())
  {
    return NULL;
  }

  tmw::Array<TMWODBC::tmwODBC::TableInfo> tables;
  m_pODBCClient->GetDbPtr()->GetTables(NULL, "TABLE", tables);

  for (unsigned int i = 0; i < tables.size(); i++)
  {
    names.Format("%s", (const char*)tables[i].name);
    nlohmann::json child;
    child[(const char*)names] = (const char*)names;
    ListTablesjson.push_back(child);
  }

  return ListTablesjson;
}
nlohmann::json GTWODBCQueryEditor::ListTables(bool bViews, bool bSystemTables)
{
  try
  {
    return TryListTables(bViews, bSystemTables);
  }
  catch (tmw::Exception &ex)
  {
    std::string message = ex.getErrorMessage(0);
    try
    {
      // close/open db and try it again in case the db connection was lost
      m_pODBCClient->CloseDataBase();
      m_pODBCClient->OpenDataBase();

      return TryListTables(bViews, bSystemTables);
    }
    catch (tmw::Exception& ex)
    {

      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
        "TR_ODBC_LIST_TABLES_EXCEPTION_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, ListTables::Exception caught: {{arg3}}",
        GTWConfig::ODBCaliasName(GetClientIndex()),
        GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()),
        ex.getCharErrorMessageBlock());
    }

  }
  catch (...)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
                                "TR_ODBC_LIST_TABLES_SEVERE_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ListTables::Exception caught",
                                GTWConfig::ODBCaliasName(GetClientIndex()),
                                GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()));
  }
  return NULL;
}

nlohmann::json GTWODBCQueryEditor::ShowTable(const std::string &stable)
{
  try
  {
    nlohmann::json tableInfoJsonSource;
    nlohmann::json columnsJson;
    nlohmann::json childColumns;
    nlohmann::json dataJson;

    childColumns["field"] = "name";
    childColumns["header"] = "NAME";
    columnsJson.push_back(childColumns);
    childColumns["field"] = "type";
    childColumns["header"] = "TYPE";
    columnsJson.push_back(childColumns);
    childColumns["field"] = "precision";
    childColumns["header"] = "PRECISION";
    columnsJson.push_back(childColumns);
    childColumns["field"] = "scale";
    childColumns["header"] = "SCALE";
    columnsJson.push_back(childColumns);
    childColumns["field"] = "nullable";
    childColumns["header"] = "NULLABLE";
    columnsJson.push_back(childColumns);
    tableInfoJsonSource += nlohmann::json::object_t::value_type("columns", columnsJson);

    if (stable.length() == 0)
    {
      tableInfoJsonSource += nlohmann::json::object_t::value_type("data", "[]");
      return tableInfoJsonSource;
    }

    if (!m_pODBCClient->IsDataBaseOpen() && !m_pODBCClient->OpenDataBase())
    {
      //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(), "TR_ODBC_OPENDB_FAILED", "Database is not open");
      return NULL;
    }

    tmw::Array<TMWODBC::tmwODBC::ColumnInfo> columns;

    m_pODBCClient->GetDbPtr()->GetTableColumns(stable.c_str(), NULL, columns);
    for (unsigned int x = 0; x < columns.size(); x++)
    {
      nlohmann::json childData;
      childData["name"] = CStdString((const char*)columns[x].name);
      childData["type"] = CStdString((const char*)columns[x].type);
      CStdString temp;

      temp.Format("%d", columns[x].width);
      childData["precision"] = temp;

      temp.Format("%d", columns[x].scale);
      childData["scale"] = temp;
      temp.Format("%d", columns[x].isNullable);
      childData["nullable"] = temp;
      childData["draggableField"] = CStdString((const char*)columns[x].name) + " ";
      dataJson.push_back(childData);
    }

    tableInfoJsonSource += nlohmann::json::object_t::value_type("data", dataJson);
    return tableInfoJsonSource;
  }
  catch (tmw::Exception &ex)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
                                "TR_ODBC_SHOW_TABLES_EXCEPTION_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, ShowTable::Exception caught: {{arg3}}",
                                GTWConfig::ODBCaliasName(GetClientIndex()),
                                GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()),
                                ex.getCharErrorMessageBlock());
  }
  catch (...)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
                                "TR_ODBC_SHOW_TABLES_SEVERE_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ShowTable::Exception caught",
                                GTWConfig::ODBCaliasName(GetClientIndex()),
                                GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()));
  }
  return NULL;  
}

nlohmann::json GTWODBCQueryEditor::ExecuteSql(const std::string& sQuery, const std::string& queryAlias, bool& bQueryOk)
{
  try
  {
    bQueryOk = false;
    nlohmann::json sqlJsonSource;
    nlohmann::json columnsJson;
    nlohmann::json childColumns;
    nlohmann::json dataJson;

    if (!m_pODBCClient->IsDataBaseOpen() && !m_pODBCClient->OpenDataBase())
    {
      //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(), "TR_ODBC_OPENDB_FAILED", "Database is not open");
      return NULL;
    }

    if (m_pODBCClient->GetDbPtr()->Prepare(sQuery.c_str()) > 0)
    {
      m_pODBCClient->GetDbPtr()->MapParameters();
    }

    GTWODBCQuery* pODBCQuery = GTWODBCClient::getODBCQuery(m_pODBCClient->GetAliasName(), queryAlias.c_str());
    for (int iCol = 0; iCol < m_pODBCClient->GetDbPtr()->GetNumParams(); iCol++)
    {
      if (pODBCQuery == nullptr)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
          "TR_ODBC_FAILED_TO_FIND_QUERY", "Failed to find Query");
        return NULL;
      }

      TMWODBC::tmwODBC::SQLParameterInfo& param = m_pODBCClient->GetDbPtr()->Param(iCol);
      CStdString paramName = pODBCQuery->GetFullName() + "." + m_pODBCClient->GetDbPtr()->GetParamName(iCol);

      GTWMasterDataObject* pParamMdo;

      GTWDEFS_STAT status = GetGTWApp()->findMdo(paramName, &pParamMdo);
      if (status == GTWDEFS_STAT_SUCCESS || pParamMdo != nullptr)
      {
        pODBCQuery->SetParamValueFromMdo(m_pODBCClient->GetDbPtr(), pParamMdo, iCol);
      }
      else
      {
        //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_ODBC, nullptr,  "%s failed to SetParamValueFromMdo\n", this->GetFullName().c_str());
      }
      //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_ODBC, nullptr,  "%s Param%d=%d\n", this->GetFullName().c_str(), iCol, param.m_lVal);
    }

    //ODBC_QUERY_STATUS qStatus = pODBCQuery->ExecuteQuery(0);

    m_pODBCClient->GetDbPtr()->ExecutePrepared();

    // Here are assume that if a NextResultSet is available, then the query worked
    bool bGotNextResult = false;
    bool didCols = false;

    TMWODBC::PropertyCollection resultProps;
    while (m_pODBCClient->GetDbPtr()->NextResultSet())
    {
      bGotNextResult = true;
      if (m_pODBCClient->GetDbPtr()->HasResults())
      {
        while (m_pODBCClient->GetDbPtr()->Fetch(&resultProps))
        {
          if (!didCols)
          {
            didCols = true;
            for (unsigned int i = 0; i < resultProps.size(); i++)
            {
              CStdString filedName = resultProps.GetAt(i)->GetName();
              childColumns["field"] = filedName;
              childColumns["header"] = filedName;
              columnsJson.push_back(childColumns);
            }
            sqlJsonSource += nlohmann::json::object_t::value_type("columns", columnsJson);
          }

          nlohmann::json childData;
          tmw::String temp;
          for (unsigned int i = 0; i < resultProps.size(); i++)
          {
            CStdString filedName = resultProps.GetAt(i)->GetName();
            if (!resultProps.GetAt(i)->IsNull())
              resultProps.GetAt(i)->GetValue()->GetValueAsString(temp);
            else
              temp = "(NULL)";
            childData[filedName] = CStdString((const char*)temp);
          }
          dataJson.push_back(childData);
        }
      }
    }

    bQueryOk = didCols || bGotNextResult;
    if (didCols)
    {
      sqlJsonSource += nlohmann::json::object_t::value_type("data", dataJson);
      return sqlJsonSource;
    }
    else if (bGotNextResult)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Information, GtwLogger::SDG_Category_ODBC, nullptr,
        "TR_ODBC_QUERY_SUCCEEDED", "ODBC: DB:{{arg1}}, Q:{{arg2}} succeeded",
        GTWConfig::ODBCaliasName(GetClientIndex()),
        GTWConfig::ODBCQueryAliasName(GetClientIndex()));

      return nullptr;
    }
    else
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Warning, GtwLogger::SDG_Category_ODBC, nullptr, "TR_ODBC_RECORD_SET_IS_EMPTY", "ODBC: DB:{{arg1}}, Q:{{arg2}}, Record set is empty: {{arg3}}",
        GTWConfig::ODBCaliasName(GetClientIndex()),
        GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()),
        (const char*)sQuery.c_str());


      LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_ODBC, nullptr, "ODBC: DB:%s, Q:%s, Record set is empty: %s",
        GTWConfig::ODBCaliasName(GetClientIndex()),
        GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()),
        (const char*)sQuery.c_str());
    }
  }
  catch (const tmw::Exception& ex)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
      "TR_ODBC_EXECUTE_EXCEPTION_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, ExecuteSql::Exception caught: {{arg3}}",
      GTWConfig::ODBCaliasName(GetClientIndex()),
      GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()),
      ex.getCharErrorMessageBlock());
  }
  catch (...)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(),
      "TR_ODBC_EXECUTE_SEVERE_FAILED", "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ExecuteSql::Exception caught",
      GTWConfig::ODBCaliasName(GetClientIndex()),
      GTWConfig::ODBCQueryAliasName(GetClientIndex(), GetQueryIndex()));

  }
  return NULL;
}
