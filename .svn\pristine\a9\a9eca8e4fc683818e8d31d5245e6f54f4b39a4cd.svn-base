﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="ARM32_Debug|VisualGDB">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|Win32">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|x64">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|VisualGDB">
      <Configuration>ARM32_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|Win32">
      <Configuration>ARM32_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|x64">
      <Configuration>ARM32_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|VisualGDB">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|Win32">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|x64">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|VisualGDB">
      <Configuration>ARM64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|Win32">
      <Configuration>ARM64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|x64">
      <Configuration>ARM64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Clang_Tidy|VisualGDB">
      <Configuration>Clang_Tidy</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Clang_Tidy|Win32">
      <Configuration>Clang_Tidy</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Clang_Tidy|x64">
      <Configuration>Clang_Tidy</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|VisualGDB">
      <Configuration>Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|VisualGDB">
      <Configuration>Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|VisualGDB">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|Win32">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|x64">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|VisualGDB">
      <Configuration>RHx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|Win32">
      <Configuration>RHx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|x64">
      <Configuration>RHx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|VisualGDB">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|Win32">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|x64">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|VisualGDB">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|Win32">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|x64">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|VisualGDB">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|Win32">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|x64">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|VisualGDB">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|Win32">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|x64">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|VisualGDB">
      <Configuration>UBx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|Win32">
      <Configuration>UBx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|x64">
      <Configuration>UBx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|VisualGDB">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|Win32">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|x64">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3EE97845-F534-49DE-BC8A-ABCA84AF34A2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>GTWOsUtils</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <RunCodeAnalysis>true</RunCodeAnalysis>
    <EnableClangTidyCodeAnalysis>true</EnableClangTidyCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(ProjectDir)..\..\bin\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bin_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <EnableAddressSanitizer>true</EnableAddressSanitizer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Debug</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Release</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Release</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Release</GNUConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <GNUConfigurationType>Release</GNUConfigurationType>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\openssl\inc32;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\openssl\incWin64;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_EXPERIMENTAL_FILESYSTEM_DEPRECATION_WARNING;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;WIN32;WIN64;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_DEBUG;_WINDOWS;_USRDLL;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>
      </MinimalRebuild>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib;cLibrary.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bind_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_WINDOWS;_USRDLL;WIN32;NDEBUG;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bin</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
      <Outputs>
      </Outputs>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;GTWOSUTILS_EXPORTS;_WINDOWS;_USRDLL;WIN32;WIN64;NDEBUG;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;..\..;..\..\thirdPartyCode\;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\openssl\incWin64;$(ProjectDir)\..\..\thirdPartyCode\asio\include;..\..\TMW61850;$(ProjectDir)..\..\TMW61850\cLibrary\include;$(ProjectDir)..\..\TMW61850\Common\vendor;$(ProjectDir)..\..\TMW61850\cLibrary\src\tmwtarg\windows;..\..\tmwscl\tmwtarg;..\..\tmwscl\tmwtarg\WinIoTarg</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>
      </MultiProcessorCompilation>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;Comdlg32.lib;pdh.lib;shell32.lib;Advapi32.lib</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>../../bin_x64</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Lib>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../thirdPartyCode/openssl/incLnx64;../../tmwscl/tmwtarg/LinIoTarg;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript />
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB20x64_Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB20x64_Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB22x64_Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB22x64_Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM64_Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM64_Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM32_Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM32_Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM32_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM32_Release;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <Optimization>O3</Optimization>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM64_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/ARM64_Release;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalOptions>-fno-omit-frame-pointer %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UBx64_Sanitize/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UBx64_Sanitize;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/RHx64_Debug/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/RHx64_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;.;..;../..;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../thirdPartyCode/TimeZone/cctz/include;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UBx64_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/Debug;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;..;../..;../../thirdPartyCode/asio/include;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB20x64_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB20x64_Release;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;..;../..;../../thirdPartyCode/asio/include;../../thirdPartyCode;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;../../TMW61850/cLibrary/src/tmwtarg/linux;../../TMW61850/cLibrary/include;../../TMW61850/Common/vendor;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <CPPLanguageStandard>GNUPP1Z</CPPLanguageStandard>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CLanguageStandard>
      </CLanguageStandard>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB22x64_Release/zlib.a;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../thirdPartyCode/ZipLib/Source/ZipLib/extlibs/zlib/VisualGDB/UB22x64_Release;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="alphanum.hpp" />
    <ClInclude Include="ConfigIntegrityChecker.h" />
    <ClInclude Include="ConfigValue.h" />
    <ClInclude Include="cpuMemStats.h" />
    <ClInclude Include="crashtest.h" />
    <ClInclude Include="DirectoryLockChecker.h" />
    <ClInclude Include="DynamicBoolArray.h" />
    <ClInclude Include="entities.h" />
    <ClInclude Include="filesystem.hpp" />
    <ClInclude Include="findfirst.h" />
    <ClInclude Include="FixedPoint.h" />
    <ClInclude Include="GetWinVer.h" />
    <ClInclude Include="GtwConfiguration.h" />
    <ClInclude Include="gtwdefs.h" />
    <ClInclude Include="GTWOSEnumConversions.h" />
    <ClInclude Include="gtwexpnd.h" />
    <ClInclude Include="GtwFileLogger.h" />
    <ClInclude Include="GtwLogger.h" />
    <ClInclude Include="GtwOsDateTime.h" />
    <ClInclude Include="GtwOsMutex.h" />
    <ClInclude Include="GtwOsSync.h" />
    <ClInclude Include="GtwOsUtils.h" />
    <ClInclude Include="GTWOsUtilsDll.h" />
    <ClInclude Include="GtwVariant.h" />
    <ClInclude Include="GtwSysConfig.h" />
    <ClInclude Include="GtwTime.h" />
    <ClInclude Include="gtw_fstream.h" />
    <ClInclude Include="icmp_header.hpp" />
    <ClInclude Include="icmp_pinger.h" />
    <ClInclude Include="IpAddrUtils.h" />
    <ClInclude Include="ipv4_header.hpp" />
    <ClInclude Include="NetworkUtils.h" />
    <ClInclude Include="pevents.h" />
    <ClInclude Include="regex.h" />
    <ClInclude Include="RestartAPI.h" />
    <ClInclude Include="ServiceBase.h" />
    <ClInclude Include="ServiceController.h" />
    <ClInclude Include="simple_timer.h" />
    <ClInclude Include="spec.h" />
    <ClInclude Include="SpecificConfigs.h" />
    <ClInclude Include="StdAfx.h" />
    <ClInclude Include="StdString.h" />
    <ClInclude Include="ThreadPool.h" />
    <ClInclude Include="TimerManager.h" />
    <ClInclude Include="TmwAsymCrypto.h" />
    <ClInclude Include="TMWDoubleLinkedList.h" />
    <ClInclude Include="tmwloadsystemlibrary.h" />
    <ClInclude Include="TMWLockable.h" />
    <ClInclude Include="TMWStringUtils.h" />
    <ClInclude Include="TMWVectorTemplate.h" />
    <ClInclude Include="TranslationReader.h" />
    <ClInclude Include="UnicodeConverter.h" />
    <ClInclude Include="utf8.h" />
    <ClInclude Include="utf8\checked.h" />
    <ClInclude Include="utf8\core.h" />
    <ClInclude Include="utf8\unchecked.h" />
    <ClInclude Include="WorkspaceMD5Verifier.h" />
    <ClInclude Include="zipHelper.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="alphanum.cpp" />
    <ClCompile Include="ConfigIntegrityChecker.cpp" />
    <ClCompile Include="ConfigValue.cpp" />
    <ClCompile Include="cpuMemStats.cpp" />
    <ClCompile Include="crashtest.cpp" />
    <ClCompile Include="DirectoryLockChecker.cpp" />
    <ClCompile Include="entities.cpp" />
    <ClCompile Include="findfirst.cpp" />
    <ClCompile Include="GetWinVer.cpp" />
    <ClCompile Include="GtwConfiguration.cpp" />
    <ClCompile Include="gtwdefs.cpp" />
    <ClCompile Include="GTWOSEnumConversions.cpp" />
    <ClCompile Include="gtwexpnd.cpp" />
    <ClCompile Include="GtwFileLogger.cpp" />
    <ClCompile Include="GtwLogger.cpp" />
    <ClCompile Include="GtwOsDateTime.cpp" />
    <ClCompile Include="GtwOsMutex.cpp" />
    <ClCompile Include="GtwOsUtils.cpp" />
    <ClCompile Include="GtwVariant.cpp" />
    <ClCompile Include="GtwSysConfig.cpp" />
    <ClCompile Include="GtwTime.cpp" />
    <ClCompile Include="gtw_fstream.cpp" />
    <ClCompile Include="icmp_pinger.cpp" />
    <ClCompile Include="IpAddrUtils.cpp" />
    <ClCompile Include="pevents.cpp" />
    <ClCompile Include="regex.cpp" />
    <ClCompile Include="RestartAPI.cpp" />
    <ClCompile Include="ServiceBase.cpp" />
    <ClCompile Include="ServiceController.cpp" />
    <ClCompile Include="simple_timer.cpp" />
    <ClCompile Include="spec.cpp" />
    <ClCompile Include="StdAfx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Clang_Tidy|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="TimerManager.cpp" />
    <ClCompile Include="TmwAsymCrypto.cpp" />
    <ClCompile Include="TMWDoubleLinkedList.cpp" />
    <ClCompile Include="TMWLoadSystemLibrary.cpp" />
    <ClCompile Include="TMWLockable.cpp" />
    <ClCompile Include="TMWStringUtils.cpp" />
    <ClCompile Include="TranslationReader.cpp" />
    <ClCompile Include="UnicodeConverter.cpp" />
    <ClCompile Include="WorkspaceMD5Verifier.cpp" />
    <ClCompile Include="zipHelper.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="GTWOsUtils-ARM32_Debug.vgdbsettings" />
    <None Include="GTWOsUtils-ARM32_Release.vgdbsettings" />
    <None Include="GTWOsUtils-ARM64_Debug.vgdbsettings" />
    <None Include="GTWOsUtils-ARM64_Release.vgdbsettings" />
    <None Include="GTWOsUtils-Debug.vgdbsettings" />
    <None Include="GTWOsUtils-RHx64_Debug.vgdbsettings" />
    <None Include="GTWOsUtils-RHx64_Release.vgdbsettings" />
    <None Include="GTWOsUtils-UB22x64_Debug.vgdbsettings" />
    <None Include="GTWOsUtils-UB22x64_Release.vgdbsettings" />
    <None Include="GTWOsUtils-UBx64_Release.vgdbsettings" />
    <None Include="GTWOsUtils-UBx64_Sanitize.vgdbsettings" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\thirdPartyCode\TimeZone\cctz\cctz.vcxproj">
      <Project>{f9ffb5ac-8ffd-4db1-b843-bf1081c4be2a}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\thirdPartyCode\ZipLib\Source\ZipLib\extlibs\zlib\zlib.vcxproj">
      <Project>{baeb16b3-db4c-432f-9e6a-2acadea0691d}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\Common\TMWBaseCPP\TMWBaseCPP.vcxproj">
      <Project>{16db0ccf-7073-4d70-806a-c6091de882cb}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\Common\TMWBase\TMWBase.vcxproj">
      <Project>{cbe7f2cb-d041-41ea-bd32-4b0a9bab8b9c}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\Common\vendor\cLibrary\cLibrary.vcxproj">
      <Project>{1a77cd26-d59a-4ccd-afd1-a9e896850aaf}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\i61850\TMW61850\TMW61850.vcxproj">
      <Project>{ab5351e5-d437-4927-a294-09ce6e3b191e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\Tase2\TMWTase2\TMWTase2.vcxproj">
      <Project>{1c6aa9f9-0837-0405-3150-f2ac1d7c3993}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\tmwscl\utils\utils.vcxproj">
      <Project>{e3af65ec-ac33-4170-bad2-d832eb1e2d37}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="CStdString.natvis" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>