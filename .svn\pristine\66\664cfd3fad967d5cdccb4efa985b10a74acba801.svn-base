﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="ARM32_Debug|VisualGDB">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|Win32">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|x64">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|VisualGDB">
      <Configuration>ARM32_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|Win32">
      <Configuration>ARM32_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|x64">
      <Configuration>ARM32_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|VisualGDB">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|Win32">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|x64">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|VisualGDB">
      <Configuration>ARM64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|Win32">
      <Configuration>ARM64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|x64">
      <Configuration>ARM64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|VisualGDB">
      <Configuration>Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|VisualGDB">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|Win32">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|x64">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|VisualGDB">
      <Configuration>RHx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|Win32">
      <Configuration>RHx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|x64">
      <Configuration>RHx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|VisualGDB">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|Win32">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|x64">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|VisualGDB">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|Win32">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|x64">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|VisualGDB">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|Win32">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|x64">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|VisualGDB">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|Win32">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|x64">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|VisualGDB">
      <Configuration>UBx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|Win32">
      <Configuration>UBx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|x64">
      <Configuration>UBx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|VisualGDB">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|Win32">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|x64">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{64E790DD-F6C5-47D8-832A-80E4D5523F33}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'" Label="Configuration">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildTool>Make</RemoteBuildTool>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <RemoteBuildTool>Make</RemoteBuildTool>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <EnableAddressSanitizer>true</EnableAddressSanitizer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <GNUConfigurationType>Release</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <GNUConfigurationType>Release</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <GNUConfigurationType>Release</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <GNUConfigurationType>Release</GNUConfigurationType>
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <GNUTargetType>DynamicLibrary</GNUTargetType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">$(ProjectDir)..\..\bind\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(ProjectDir)..\..\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(ProjectDir)..\..\bin_x64\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone/cctz/include;../../thirdPartyCode/TimeZone;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
      <CustomPostCompileTool>
      </CustomPostCompileTool>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux/haspvlib_arm64_102099.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_arm64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
      <CustomPostCompileTool>
      </CustomPostCompileTool>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux/haspvlib_armhf_102099.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_armhf_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
      <CustomPostCompileTool>
      </CustomPostCompileTool>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux/haspvlib_armhf_102099.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_armhf_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
      <CustomPostCompileTool>
      </CustomPostCompileTool>
      <Optimization>O3</Optimization>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux/haspvlibarm64_102099.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_arm64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;DEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;../../thirdPartyCode/TimeZone;../../thirdPartyCode/TimeZone/cctz/include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LINUX;NDEBUG;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;USE_STANDALONE_ASIO;ASIO_STANDALONE;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalOptions>-Wno-deprecated-declarations %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>hasp_linux_x86_64_102099;pthread;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\inc32;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(OutDir);$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>
      </MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>__DllMainCRTStartup%4012;%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\incWin64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(OutDir);$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Async</ExceptionHandling>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0000</Culture>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ForceSymbolReferences>%(ForceSymbolReferences)</ForceSymbolReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <EntryPointSymbol>
      </EntryPointSymbol>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;..\..\thirdPartyCode\openssl\inc32;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>true</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <ExceptionHandling>Async</ExceptionHandling>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;hasp_windows_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(OutDir);$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <ClCompile>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\incWin64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;GTWLICLIB_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_CRT_SECURE_NO_WARNINGS;GTWSNLicUtils_EXPORTS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <GenerateXMLDocumentationFiles>true</GenerateXMLDocumentationFiles>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <ExceptionHandling>Async</ExceptionHandling>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <DisableSpecificWarnings>4251</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;hasp_windows_x64_102099.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>$(OutDir);$(ProjectDir)..\..\Sentinel\SNLicManager\SNLicUtils\VendorLibs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>..\..\..\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\TMW61850\Common\TMWBaseCPP\TMWBaseCPP.vcxproj">
      <Project>{16db0ccf-7073-4d70-806a-c6091de882cb}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\ThirdParty\expat\expat_static.vcxproj">
      <Project>{669ecc78-4683-4eb4-96d7-3eed914fcd2f}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWOsUtils\GTWOsUtils.vcxproj">
      <Project>{3ee97845-f534-49de-bc8a-abca84af34a2}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ItemGroup>
    <ClCompile Include="Crypto.cpp" />
    <ClCompile Include="errorprinter.cpp" />
    <ClCompile Include="GtwLicInspect.cpp" />
    <ClCompile Include="GTWSNLicUtils.cpp">
      <ShowIncludes Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</ShowIncludes>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)\..\..\thirdPartyCode\asio\include;$(ProjectDir)..\..\;$(ProjectDir)..\..\TMW61850;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\incWin64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">../../thirdPartyCode/asio/include;../../;../../TMW61850/;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <ClCompile Include="haspbase.cpp" />
    <ClCompile Include="haspdiag.cpp" />
    <ClCompile Include="haspenc64.cpp" />
    <ClCompile Include="haspfeature.cpp" />
    <ClCompile Include="haspfile.cpp" />
    <ClCompile Include="hasphandle.cpp" />
    <ClCompile Include="hasphasp.cpp" />
    <ClCompile Include="haspimpl.cpp" />
    <ClCompile Include="haspinfo.cpp" />
    <ClCompile Include="hasplegacy.cpp" />
    <ClCompile Include="hasplock.cpp" />
    <ClCompile Include="haspmain.cpp" />
    <ClCompile Include="haspmap.cpp" />
    <ClCompile Include="hasptime.cpp" />
    <ClCompile Include="haspversion.cpp" />
    <ClCompile Include="KeyManager.cpp" />
    <ClCompile Include="KeyManagerImpl.cpp" />
    <ClCompile Include="XMLHelperTimeStart.cpp" />
    <ClCompile Include="XMLHelperTotalTime.cpp" />
    <ClInclude Include="Crypto.h" />
    <ClInclude Include="errorprinter.h" />
    <ClInclude Include="GtwLicInspect.h" />
    <ClCompile Include="Feature.cpp" />
    <ClCompile Include="HaspInfoXMLContext.cpp" />
    <ClCompile Include="HaspInfoXMLEventHandler.cpp" />
    <ClCompile Include="HaspInfoXMLReadAdaptors.cpp" />
    <ClCompile Include="HaspInfoXMLReader.cpp" />
    <ClCompile Include="haspinfoxmltags.cpp" />
    <ClCompile Include="XMLHelperDisabled.cpp" />
    <ClCompile Include="XMLHelperExpDate.cpp" />
    <ClCompile Include="XMLHelperFeature.cpp" />
    <ClCompile Include="XMLHelperHasp.cpp" />
    <ClCompile Include="XMLHelperHaspInfo.cpp" />
    <ClCompile Include="XMLHelperID.cpp" />
    <ClCompile Include="XMLHelperLicense.cpp" />
    <ClCompile Include="XMLHelperLicenseType.cpp" />
    <ClCompile Include="XMLHelperProduct.cpp" />
    <ClInclude Include="Feature.h" />
    <ClInclude Include="GTWLicLibDll.h" />
    <ClInclude Include="GTWSNLicUtils.h" />
    <ClInclude Include="haspdiag.h" />
    <ClInclude Include="HaspInfoXMLContext.h" />
    <ClInclude Include="HaspInfoXMLEventHandler.h" />
    <ClInclude Include="HaspInfoXMLReadAdaptors.h" />
    <ClInclude Include="HaspInfoXMLReader.h" />
    <ClInclude Include="haspinfoxmltags.h" />
    <ClInclude Include="hasp_api.h" />
    <ClInclude Include="hasp_api_cpp.h" />
    <ClInclude Include="hasp_api_cpp_.h" />
    <ClInclude Include="IKeyManager.h" />
    <ClInclude Include="KeyManager.h" />
    <ClInclude Include="KeyManagerImpl.h" />
    <ClInclude Include="KeyManagerLoader.h" />
    <ClInclude Include="KeyManagerPtr.h" />
    <ClInclude Include="LibraryLoader.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="SNNative.h" />
    <ClInclude Include="vendor_code.h" />
    <ClInclude Include="XMLHelperDisabled.h" />
    <ClInclude Include="XMLHelperExpDate.h" />
    <ClInclude Include="XMLHelperFeature.h" />
    <ClInclude Include="XMLHelperHasp.h" />
    <ClInclude Include="XMLHelperHaspInfo.h" />
    <ClInclude Include="XMLHelperID.h" />
    <ClInclude Include="XMLHelperLicense.h" />
    <ClInclude Include="XMLHelperLicenseType.h" />
    <ClInclude Include="XMLHelperProduct.h" />
    <ClInclude Include="XMLHelperTimeStart.h" />
    <ClInclude Include="XMLHelperTotalTime.h" />
    <None Include="GTWSNLicUtils-ARM32_Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-ARM32_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-ARM64_Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-ARM64_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-RHx64_Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-RHx64_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-UB20x64_Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-UB20x64_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-UB22x64_Debug.vgdbsettings" />
    <None Include="GTWSNLicUtils-UB22x64_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-UBx64_Release.vgdbsettings" />
    <None Include="GTWSNLicUtils-UBx64_Sanitize.vgdbsettings" />
  </ItemGroup>
</Project>