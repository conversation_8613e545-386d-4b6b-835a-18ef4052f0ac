

services:
  # Main SDG application container
  sdg:
    build:
      context: .
      dockerfile: Dockerfile.sdg
      args:
        SDG_PACKAGE: ${SDG_PACKAGE}
        HASP_PACKAGE: ${HASP_PACKAGE}
    container_name: sdg-application
    platform: linux/amd64
    hostname: ${SDG_HOSTNAME:-sdg-gateway}
    domainname: ${SDG_DOMAINNAME:-local}
    privileged: true
    cap_add:
      - SYS_ADMIN
      - SYS_PTRACE
      - MKNOD
      - SYS_RAWIO
    security_opt:
      - seccomp:unconfined
      - apparmor:unconfined
    devices:
      - /dev/urandom:/dev/urandom
      - /dev/fuse:/dev/fuse
# Option 2: RTE and licenses inside container with tmpfs storage
    ports:
      - "58090:58090"
      - "58080:58080"
      - "4885:4885"
      - "1947:1947"
      - "102-103:102-103"
      - "2404-2406:2404-2406"
      - "20000-20003:20000-20003"
      - "502-503:502-503"
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - sdg_data:/opt/sdg/data
      - hasplm_data:/var/hasplm
      - hasplm_config:/etc/hasplm
    tmpfs:
      - /run
      - /run/lock
      - /tmp
    environment:
      - container=docker
      # SDG Configuration
      - SDG_ADMIN_PASSWORD=${SDG_ADMIN_PASSWORD:-passwordA1.}
      - SDG_HOST_IP=${SDG_HOST_IP:-127.0.0.1}
      - SDG_USE_LOCALHOST_COMMS=${SDG_USE_LOCALHOST_COMMS:-true}

      # Network licensing configuration (bootstrap license always installed)
      - HASP_LICENSE_SERVER=${HASP_LICENSE_SERVER}
      # GTW Supervisor configuration (optional - can also use config file)
      - GTW_MAX_RESTART_ATTEMPTS=${GTW_MAX_RESTART_ATTEMPTS:-5}
      - GTW_RESTART_DELAY=${GTW_RESTART_DELAY:-5}
      - GTW_HEALTH_CHECK_INTERVAL=${GTW_HEALTH_CHECK_INTERVAL:-5}
      - GTW_LOG_DIR=${GTW_LOG_DIR:-/var/log}
    # Use image default CMD (start-sdg.sh)
    # command: /usr/sbin/init
    # stop_signal: SIGRTMIN+3
    extra_hosts:
      - "license-server:${LICENSE_SERVER_IP:-127.0.0.1}"
    networks:
      - sdg-network
    restart: unless-stopped

volumes:
  sdg_data:
    driver: local
  hasplm_data:
    driver: local
  hasplm_config:
    driver: local
  sdg_config:
    driver: local

networks:
  sdg-network:
    driver: bridge
