#!/bin/bash
# Script to configure GTW supervisor settings

set -e

CONFIG_FILE="/opt/sdg/data/supervisor.conf"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}GTW Supervisor Configuration Manager${NC}"
echo "===================================="

show_current_config() {
    echo "Current configuration:"
    if [ -f "$CONFIG_FILE" ]; then
        echo -e "${GREEN}Config file exists: $CONFIG_FILE${NC}"
        cat "$CONFIG_FILE"
    else
        echo -e "${YELLOW}No config file found. Using defaults.${NC}"
    fi
    echo ""
    
    # Show current effective configuration
    if command -v /usr/local/bin/supervisor-gtw.sh >/dev/null 2>&1; then
        /usr/local/bin/supervisor-gtw.sh config
    fi
}

create_config() {
    local max_restarts="${1:-5}"
    local restart_delay="${2:-5}"
    local health_interval="${3:-5}"
    local log_dir="${4:-/var/log}"
    
    echo "Creating configuration file: $CONFIG_FILE"
    
    # Ensure directory exists
    mkdir -p "$(dirname "$CONFIG_FILE")"
    
    cat > "$CONFIG_FILE" << EOF
# GTW Supervisor Configuration
# This file is sourced by supervisor-gtw.sh
# Environment variables take precedence over these settings

# Maximum number of restart attempts per hour per service
MAX_RESTART_ATTEMPTS=$max_restarts

# Delay in seconds between restart attempts
RESTART_DELAY=$restart_delay

# Health check interval in seconds
HEALTH_CHECK_INTERVAL=$health_interval

# Log directory
LOG_DIR="$log_dir"

# Created: $(date)
EOF
    
    echo -e "${GREEN}Configuration file created successfully${NC}"
    echo "Contents:"
    cat "$CONFIG_FILE"
}

interactive_config() {
    echo "Interactive configuration setup:"
    echo ""
    
    # Get current values or defaults
    local current_max=5
    local current_delay=5
    local current_interval=5
    local current_log="/var/log"
    
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE" 2>/dev/null || true
        current_max=${MAX_RESTART_ATTEMPTS:-5}
        current_delay=${RESTART_DELAY:-5}
        current_interval=${HEALTH_CHECK_INTERVAL:-5}
        current_log=${LOG_DIR:-/var/log}
    fi
    
    echo -n "Max restart attempts per hour [$current_max]: "
    read max_restarts
    max_restarts=${max_restarts:-$current_max}
    
    echo -n "Restart delay in seconds [$current_delay]: "
    read restart_delay
    restart_delay=${restart_delay:-$current_delay}
    
    echo -n "Health check interval in seconds [$current_interval]: "
    read health_interval
    health_interval=${health_interval:-$current_interval}
    
    echo -n "Log directory [$current_log]: "
    read log_dir
    log_dir=${log_dir:-$current_log}
    
    echo ""
    echo "Configuration summary:"
    echo "  Max restart attempts: $max_restarts"
    echo "  Restart delay: ${restart_delay}s"
    echo "  Health check interval: ${health_interval}s"
    echo "  Log directory: $log_dir"
    echo ""
    echo -n "Save this configuration? [Y/n]: "
    read confirm
    
    if [[ "$confirm" =~ ^[Nn] ]]; then
        echo "Configuration cancelled"
        exit 0
    fi
    
    create_config "$max_restarts" "$restart_delay" "$health_interval" "$log_dir"
}

remove_config() {
    if [ -f "$CONFIG_FILE" ]; then
        echo -e "${YELLOW}Removing configuration file: $CONFIG_FILE${NC}"
        rm -f "$CONFIG_FILE"
        echo "Configuration file removed. Will use defaults."
    else
        echo "No configuration file to remove"
    fi
}

case "${1:-show}" in
    "show")
        show_current_config
        ;;
    "create")
        if [ $# -eq 1 ]; then
            interactive_config
        else
            create_config "$2" "$3" "$4" "$5"
        fi
        ;;
    "edit")
        if [ -f "$CONFIG_FILE" ]; then
            if command -v nano >/dev/null 2>&1; then
                nano "$CONFIG_FILE"
            elif command -v vi >/dev/null 2>&1; then
                vi "$CONFIG_FILE"
            else
                echo "No editor available. Config file location: $CONFIG_FILE"
                echo "You can edit it manually or use: docker exec -it container_name vi $CONFIG_FILE"
            fi
        else
            echo "No config file exists. Use 'create' command first."
        fi
        ;;
    "remove")
        remove_config
        ;;
    "preset")
        case "$2" in
            "fast")
                create_config 30 9 9 "/var/log"
                echo -e "${GREEN}Fast preset applied: 9s health checks, 9s restart delay, 30 max restarts${NC}"
                ;;
            "normal")
                create_config 15 15 15 "/var/log"
                echo -e "${GREEN}Normal preset applied: 15s health checks, 15s restart delay, 15 max restarts (balanced default)${NC}"
                ;;
            "conservative")
                create_config 9 30 45 "/var/log"
                echo -e "${GREEN}Conservative preset applied: 45s health checks, 30s restart delay, 9 max restarts${NC}"
                ;;
            *)
                echo "Available presets: fast, normal, conservative"
                exit 1
                ;;
        esac
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND] [OPTIONS]"
        echo ""
        echo "Commands:"
        echo "  show                    Show current configuration (default)"
        echo "  create                  Interactive configuration setup"
        echo "  create MAX DELAY INT    Create config with specific values"
        echo "  edit                    Edit configuration file"
        echo "  remove                  Remove configuration file"
        echo "  preset PRESET           Apply preset configuration"
        echo "  help                    Show this help"
        echo ""
        echo "Presets:"
        echo "  fast         9s health checks, 9s restart delay, 30 max restarts"
        echo "  normal       15s health checks, 15s restart delay, 15 max restarts (balanced default)"
        echo "  conservative 45s health checks, 30s restart delay, 9 max restarts"
        echo ""
        echo "Examples:"
        echo "  $0 create                    # Interactive setup"
        echo "  $0 create 10 3 2 /var/log    # Create with specific values"
        echo "  $0 preset fast               # Apply fast preset"
        echo ""
        echo "Configuration file: $CONFIG_FILE"
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
