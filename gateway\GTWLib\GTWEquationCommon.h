/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWEquationCommon.h                                         */
/* DESCRIPTION:  Class definition for arithmetic equations                   */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 1 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#ifndef GTWEquationCommon_DEFINITIONS
#define GTWEquationCommon_DEFINITIONS

#include "gateway/GTWLib/tmwtoken.h"
#include "GTWConverter.h"

class GTWEquationFunctionConverter;
class GTWEquationDataObject;

/***************************************************************************/
/*                                                                         */
/*                            TYPE DEFINITIONS                             */
/*                                                                         */
/***************************************************************************/
enum GTWEQUAT_STATE
{
  GS_START,          /* before any functions, symbols, or constant        */
  GS_IN_FUNCTION,    /* waiting for argument to a function, ) allowed     */
  GS_AFTER_COMMA,    /* waiting for argument to a function, ) not allowed */
  GS_IN_PAR_PHRASE,
  GS_IN_OPER_EXPR,
  GS_IN_COMMENT,      
  GS_WTNG_FOR_LPAR,
  GS_WTNG_FOR_END,
  GS_WTNG_FOR_COMMA,
  GS_WTNG_FOR_OPER,
  GS_WTNG_FOR_RPAR,
  GS_NUM_STATES
};

/*****************************************************************************/

enum GTWEQUAT_ACTION
{
  GA_SAME,
  GA_WARN_CTRL,
  GA_UNEX_END,
  GA_UNEX_CMNT,
  GA_BAD_TOKEN,
  GA_OPERATOR,
  GA_SYMBOL,
  GA_STRING,
  GA_CMNT_END,
  GA_OPER_TEST,
  GA_END,
};

/*****************************************************************************/

enum GTWEquationArgument_TYPE
{
  GTWEquationArgument_TYPE_NONE,
  GTWEquationArgument_TYPE_MDO,
  GTWEquationArgument_TYPE_FUNC,
  GTWEquationArgument_TYPE_CONSTANT,
};

/***************************************************************************/

typedef GTWReadConverterTemplate<bool>* GTWEQLGC_BOOL_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_SHORT>* GTWEQATH_SHORT_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_USHORT>* GTWEQATH_USHORT_CNVTR_PTR;

typedef GTWReadConverterTemplate<CStdString> *GTWEQATH_STRING_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWDTIME> *GTWEQATH_TIME_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_DOUBLE> *GTWEQATH_DOUBLE_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_UINT>  *GTWEQATH_ULONG_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_INT64>* GTWEQATH_INT64_CNVTR_PTR;
typedef GTWReadConverterTemplate<TMWTYPES_UINT64>* GTWEQATH_UINT64_CNVTR_PTR;

/***************************************************************************/

struct GTWEQUAT_FUNC_DESCRIPTOR
{
  const char *pIdentifier;
  const char *pExampleText;
  const char *pHelpText;
  GTWEquationFunctionConverter *(*pAllocCnvtr)(GTWEquationDataObject *pEQOB);
};

struct GTWEQUAT_OPER_DESCRIPTOR 
{
  const char *pIdentifier;
  const char *pOperExampleText;
  const char *pOperHelpText;
  int                       precedenceOrder;
  GTWEQUAT_FUNC_DESCRIPTOR *pFuncDescriptpr;
};

extern GTWEQUAT_FUNC_DESCRIPTOR baseDescriptor;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_setVQTFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_setVQTFunctionSync;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_setVQFunctionPeriodic;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_ifFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_ifDelayFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_iftFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_sumFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_subtractFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_multiplyFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_divideFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_MakeFloatFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_UShortToInt64;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_UShortToUInt64;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_UShortToFloat64;
;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_MakeDoublePointFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_concatFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_DoCROBFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_DoCROBwCountFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_IPAddrToNumFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToDouble;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToFloat;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToLong;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToULong;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToShort;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToUShort;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToChar;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToUChar;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToBool;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToInt64;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcnv_convertToUInt64;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_LogicalAndFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseAndFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_LogicalOrFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_LogicalXorFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseOrFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseGetFlagsFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseGetQualityFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_GetGatewayQualityFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_notFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseNotFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_TestBitFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_mkDblFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitStringFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_WBitStringFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_RandomFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_RampFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SineFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SawFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SquareFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SquareDTFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_PulseFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_PulseFunction2;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_PulseOnEventFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_CountChangeFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_OnChangeFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_OnStrChangeFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_OnNumChangeFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_AnyChangedFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_RoundFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_EqualsFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_StringCompareFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_NotEqualsFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_GreaterThanOrEqualsFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_LessThanOrEqualsFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_GreaterThanFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqcmp_LessThanFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_SteadyFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_gettimeFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_time_to_msFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_time_from_msFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_TimeAsStringFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqath_OPCCommandUserPwdFunction;

extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_degToRadFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_radToDegFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_sineFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_cosineFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_tanFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_absFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_sqrtFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_powFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_expFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_logFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_maxFunction;
extern GTWEQUAT_FUNC_DESCRIPTOR gtweqmath_minFunction;

#endif // GTWEquationCommon_DEFINITIONS

