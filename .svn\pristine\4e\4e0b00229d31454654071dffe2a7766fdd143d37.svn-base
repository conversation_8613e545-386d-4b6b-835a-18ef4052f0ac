﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="ARM32_Debug|VisualGDB">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|Win32">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Debug|x64">
      <Configuration>ARM32_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|VisualGDB">
      <Configuration>ARM32_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|Win32">
      <Configuration>ARM32_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM32_Release|x64">
      <Configuration>ARM32_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|VisualGDB">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|Win32">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Debug|x64">
      <Configuration>ARM64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|VisualGDB">
      <Configuration>ARM64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|Win32">
      <Configuration>ARM64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ARM64_Release|x64">
      <Configuration>ARM64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|VisualGDB">
      <Configuration>Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|VisualGDB">
      <Configuration>Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|VisualGDB">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|Win32">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Debug|x64">
      <Configuration>RHx64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|VisualGDB">
      <Configuration>RHx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|Win32">
      <Configuration>RHx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RHx64_Release|x64">
      <Configuration>RHx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|VisualGDB">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|Win32">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Debug|x64">
      <Configuration>UB20x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|VisualGDB">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|Win32">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB20x64_Release|x64">
      <Configuration>UB20x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|VisualGDB">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|Win32">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Debug|x64">
      <Configuration>UB22x64_Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|VisualGDB">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|Win32">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UB22x64_Release|x64">
      <Configuration>UB22x64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|VisualGDB">
      <Configuration>UBx64_Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|Win32">
      <Configuration>UBx64_Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Release|x64">
      <Configuration>UBx64_Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|VisualGDB">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|Win32">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="UBx64_Sanitize|x64">
      <Configuration>UBx64_Sanitize</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AC3AD2D0-D16D-45F9-84AE-20B76C32E579}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>GTWSettings</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bind_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bin\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64</LibraryPath>
    <OutDir>$(ProjectDir)..\..\bin_x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgARM32</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgARM64</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <EnableAddressSanitizer>true</EnableAddressSanitizer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgBuild</RemoteBuildHost>
    <GNUConfigurationType>Release</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgU20build</RemoteBuildHost>
    <GNUConfigurationType>Release</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgU22build</RemoteBuildHost>
    <GNUConfigurationType>Release</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
    <GNUToolchainPrefix />
    <GNUCompilerType>GCC</GNUCompilerType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <ToolchainID>com.sysprogs.toolchain.default-gcc</ToolchainID>
    <ToolchainVersion />
    <RemoteBuildHost>alias:sdgRhBuild</RemoteBuildHost>
    <GNUConfigurationType>Release</GNUConfigurationType>
    <OutDir>$(ProjectDir)..\..\$(Configuration)\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;..\..\thirdPartyCode\openssl\inc32;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>libcrypto.lib;libssl.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bind;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <ShowProgress>NotSet</ShowProgress>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>
      </AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\incWin64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;_DEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;USE_TMWDTIME;USE_XML_WRITE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ShowIncludes>false</ShowIncludes>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild>
      </MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <AdditionalLibraryDirectories>..\..\bind_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;NDEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;..\..\thirdPartyCode\openssl\inc32;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\bin;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;USE_STANDALONE_ASIO;ASIO_STANDALONE;WIN32;WIN64;NDEBUG;_CONSOLE;_CRT_SECURE_NO_DEPRECATE;TMW_USE_GATEWAY_DEFINES;TMW_PRIVATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>..\..\TMW61850;.\;..\..\;$(ProjectDir)\..\..\tmwscl\tmwtarg\WinIoTarg;..\..\thirdPartyCode\openssl\incWin64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>..\..\bin_x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <DelayLoadDLLs>%(DelayLoadDLLs)</DelayLoadDLLs>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript />
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UB20x64_Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UB20x64_Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;tbb;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UB22x64_Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UB22x64_Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>tbb;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../ARM64_Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../ARM64_Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>tbb;pthread;hasp_linux_arm64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../ARM32_Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../ARM32_Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_armhf_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../ARM32_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../ARM32_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_armhf_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../ARM64_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../ARM64_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>tbb;pthread;hasp_linux_arm64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
      <Optimization>O3</Optimization>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UBx64_Sanitize/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UBx64_Sanitize;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
      <AdditionalOptions> -fsanitize=address %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
      <AdditionalOptions>-fno-omit-frame-pointer %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../RHx64_Debug/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../RHx64_Debug;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;DEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UBx64_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UBx64_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg;</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UB20x64_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UB20x64_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>tbb;ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg;</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../UB22x64_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../UB22x64_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;tbb;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg;</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">
    <Link>
      <AdditionalLinkerInputs>../../RHx64_Release/cLibrary.so;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>../../RHx64_Release;../../Sentinel/SNLicManager/SNLicUtils/VendorLibs/Linux;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>ssl;crypto;pthread;hasp_linux_x86_64_102099;ToolkitUA;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>
      </LinkerScript>
      <ExtraRPATH>/usr/lib/tmw/sdg</ExtraRPATH>
      <RemoveUnusedSections>ex</RemoveUnusedSections>
    </Link>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile>
      <PrecompiledHeaderName>stdafx.h</PrecompiledHeaderName>
      <AdditionalIncludeDirectories>../../TMW61850;.;../..;../../tmwscl/tmwtarg/LinIoTarg;../../thirdPartyCode/openssl/incLnx64;..\..\thirdPartyCode\TimeZone;..\..\thirdPartyCode\TimeZone\cctz\include;..\..\thirdPartyCode\asio\include;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_STANDALONE_ASIO;ASIO_STANDALONE;NDEBUG;_LINUX;TMW_PRIVATE;TMW_USE_GATEWAY_DEFINES;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <PositionIndependentCode>true</PositionIndependentCode>
      <AdditionalCPPOptions>-fpermissive</AdditionalCPPOptions>
      <CPPLanguageStandard>CPP1Z</CPPLanguageStandard>
      <AdditionalCOptions>/showIncludes</AdditionalCOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\GTWOsUtils\ParseCmdLine.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="stdafx.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="GTWSettings.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM32_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ARM64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Sanitize|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Debug|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UBx64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB20x64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='UB22x64_Release|VisualGDB'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RHx64_Release|VisualGDB'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="GTWSettings-ARM32_Debug.vgdbsettings" />
    <None Include="GTWSettings-ARM32_Release.vgdbsettings" />
    <None Include="GTWSettings-ARM64_Debug.vgdbsettings" />
    <None Include="GTWSettings-ARM64_Release.vgdbsettings" />
    <None Include="GTWSettings-Debug.vgdbsettings" />
    <None Include="GTWSettings-LinDebugArm.vgdbsettings" />
    <None Include="GTWSettings-RHx64_Debug.vgdbsettings" />
    <None Include="GTWSettings-RHx64_Release.vgdbsettings" />
    <None Include="GTWSettings-UB20x64_Debug.vgdbsettings" />
    <None Include="GTWSettings-UB20x64_Release.vgdbsettings" />
    <None Include="GTWSettings-UB22x64_Debug.vgdbsettings" />
    <None Include="GTWSettings-UB22x64_Release.vgdbsettings" />
    <None Include="GTWSettings-UBx64_Release.vgdbsettings" />
    <None Include="GTWSettings-UBx64_Sanitize.vgdbsettings" />
    <None Include="GTWSettings.rc2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\TMW61850\Common\SQLLite\SQLLite.vcxproj">
      <Project>{e6fc9765-08bf-479f-a859-3802e2b59e7d}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\TMW61850\Common\TMWBaseCPP\TMWBaseCPP.vcxproj">
      <Project>{16db0ccf-7073-4d70-806a-c6091de882cb}</Project>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="..\..\tmwscl\utils\utils.vcxproj">
      <Project>{e3af65ec-ac33-4170-bad2-d832eb1e2d37}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWLib\GTWLib.vcxproj">
      <Project>{46d59327-cd7d-48ab-a1f2-29a9606ef5a0}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWOsUtils\GTWOsUtils.vcxproj">
      <Project>{3ee97845-f534-49de-bc8a-abca84af34a2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWSettingsLib\GTWSettingsLib.vcxproj">
      <Project>{9b021662-9fe7-4026-a44c-b41b4c7b4ab0}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWSNLicUtils\GTWSNLicUtils.vcxproj">
      <Project>{64e790dd-f6c5-47d8-832a-80e4d5523f33}</Project>
    </ProjectReference>
    <ProjectReference Include="..\GTWWebLib\GTWWebLib.vcxproj">
      <Project>{5d8b920e-3309-4c77-b395-27baf4a79309}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Image Include="GTWSettings.ico" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="GTWSettings.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>