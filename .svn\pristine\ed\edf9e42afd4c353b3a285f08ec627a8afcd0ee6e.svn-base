build on pi 4 argon

set env vars ArmBuildDir=/home/<USER>/SDG   RemoteBuildDir=/media/sf_F_DRIVE/work/SDG_5.2.3

make sure sdg dir is mounted sudo mount -t cifs //JUPITER/SDG /home/<USER>/SDG -o username=<PERSON><PERSON><PERSON><PERSON>,password=???pw???,uid=pi,gid=pi,dir_mode=0777,file_mode=0777

run build arm 64 with visualgdb

for packager run local-build.sh in ~/SDG/gateway/InstallArmRaspberryPI
look in /home/<USER>/SDG_Linux_build for output deb file it might complain deb failure but it works anyhow

test on the advantech pi

sudo dpkg -i tmwsdg-5.2.2-22234.arm64-buster.deb

sudo systemctl status tmwsdg-monitor.service
sudo systemctl status tmwsdg-engine.service

sudo systemctl restart tmwsdg-monitor.service