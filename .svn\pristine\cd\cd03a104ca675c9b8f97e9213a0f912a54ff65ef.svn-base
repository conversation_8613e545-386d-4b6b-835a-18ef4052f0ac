#pragma once

#include "GTWOsUtilsDll.h"

#include <math.h>
#include "tmwscl/utils/tmwdtime.h"
#include "cctz/time_zone.h"
#include "cctz/include/cctz/time_zone.h"
#include "cctz/src/time_zone_info.h"

//class cctz::TimeZoneInfo;

class GTWOSUTILS_API GtwTimeZone
{
  friend class GtwSimulatedTime;
  friend class TimeTest_ConvertSpecificToUtcTime_Test;
  friend class TimeTest_ConvertUtcToSpecificTime_New_York_Test;
  friend class TimeTest_ConvertUtcToSpecificTime_London_Test;
  friend class TimeTest_ConvertUtcToSpecificTimePerf_Test;

private:
  static GtwTimeZone *g_pCurrentTimeZone;
//static std::string g_tz_db_path;

  static TMWTYPES_UCHAR asDayOfWeek(cctz::weekday wkd);
  cctz::time_zone *tzPtr()
  {
	return &_currentTimeZone;
  }

public:
  GtwTimeZone()
  {
	m_pCurrentTimeZoneInfo = nullptr;
	stdDstDiff = -1;
  }

  ~GtwTimeZone()
  {
	if (m_pCurrentTimeZoneInfo != nullptr)
	{
	  delete m_pCurrentTimeZoneInfo;
	  m_pCurrentTimeZoneInfo = nullptr;
	}
  }

  static void LoadTimeZones();
  static bool IsValidTimeZone(const std::string& sTz)
  {
	cctz::time_zone tzz;
	return cctz::load_time_zone(sTz.c_str(), &tzz);
  }

  static void SetUseSimulatedTime(bool bValue)
  {
    m_bUseSimulatedTime = bValue;
  }

	std::string Name()
	{
		return tzPtr()->name();
	}

  static CStdString getTimeAsString(const TMWDTIME& time)
  {
    CStdString sTime;
    sTime.Format("%02u:%02u:%02u.%03u", time.hour, time.minutes, time.mSecsAndSecs / 1000, time.mSecsAndSecs % 1000);

    return sTime;
  }

  static void SetCurrentUtcTime(TMWDTIME& tm);
  static void GetCurrentTzTime(TMWDTIME& tm, GtwTimeZone &timeZone);

  static std::string GetCurrentTimeString();
  static std::string GetCurrentTimeStringMs();
  static int64_t GetCurrentMilliseconds();

  static GtwTimeZone *GetCurrentTimeZone();
  static void destroy();
  static bool SetCurrentTzTime(TMWDTIME& tm);
  static bool ConvertSpecificToUtcTime(const TMWDTIME& specificTime, TMWDTIME& utcTime, GtwTimeZone& local_tz);
  static bool ConvertUtcToSpecificTime(const TMWDTIME& utcTime, TMWDTIME& specificTime, GtwTimeZone& tz);
  static std::string GetLocalTimeTimeZoneName();
  static void SetCurrentLocalTime(TMWDTIME& tm);
  static bool SetCurrentTimeZone(const std::string& tzName, bool ignoreDST);
  static bool GetTimeZoneFromRegion(const std::string &region, bool ignoreDST, GtwTimeZone *tz);
  //static std::unordered_map<std::string, const cctz::time_zone::Impl*> *GetTimeZoneDb();
  static bool GetTimeZoneIsDst(const GtwTimeZone& tz, const TMWDTIME& specificTime);
  static std::int_fast32_t GetTimeZoneOffset(GtwTimeZone& tz, const TMWDTIME& specificTime);

  static void ConvertZoneName(const std::string& winName, std::string &boostName);
  static void ConvertWinToInan(const std::string& winName, std::string &inanName);
  static void GetWinZoneNameFromIndex(TMWTYPES_USHORT index, std::string &boostName);
  static void DumpTimeZones(void);
  static void GetJsonTimeZones(CStdString &jsonStr);

private:

  static void setCurrentUtcSystemTime(TMWDTIME& tm);

	cctz::time_zone    _currentTimeZone;
	cctz::TimeZoneInfo *m_pCurrentTimeZoneInfo;
  std::int_fast32_t stdDstDiff;
  std::int_fast32_t tzOffset;
	bool _ignoreDst;

  static bool m_bUseSimulatedTime;
};

class GTWOSUTILS_API GtwTime
{
private:

  static bool strp_atoi(const char * & s, int & result, int low, int high, int offset);

public:
  static char* strptime(const char *s, const char *format, struct tm *tm, int *millisecs);
  static void InitTMWDTIME(TMWDTIME *dtime);

  static const char* GetTMWDTIMEAsString(const TMWDTIME *pTime, CStdString &sTime)
  {
    // MM-DD-YYYY HH:MM:SS:MS
    sTime.Format("%02d-%02d-%04d %02d:%02d:%02d.%03d", pTime->month, pTime->dayOfMonth, pTime->year, pTime->hour, pTime->minutes, pTime->mSecsAndSecs / 1000, pTime->mSecsAndSecs % 1000);
    return sTime.c_str();
  }

  static CStdString toString(const std::chrono::system_clock::time_point& tp)
  {
	std::time_t time_c = std::chrono::system_clock::to_time_t(tp);
	std::tm time_tm = *std::localtime(&time_c);

	char buffer[80];
	strftime(buffer, sizeof(buffer), "%m/%d/%Y %H:%M:%S", &time_tm);
	return CStdString(buffer);
  }
};

class GTWOSUTILS_API GtwSimulatedTime
{
public:
  static void InitInternalClock();
  static void GetCurrentClockTime(TMWDTIME &time);
  static void SetCurrentClockTime(TMWDTIME &time);

private:
  static bool     m_bInitialized;
  static TMWDTIME m_InternalClock;
  static uint32_t m_LastInternalClockUpdate; // milliseconds

  // All of these methods are taken out of WinIOTarg code - tried, tested, and proven
  static void _adjustToCurrentTime();
  static void _incrementMinute();
  static void _incrementHour();
  static void _incrementDay();
};



