// GTWODBCQueryEditor.h: interface for the GTWODBCQueryEditor class.
//
//////////////////////////////////////////////////////////////////////

#pragma once

#include "gateway/GTWLib/GTWBaseEditor.h"
#include "gateway/GTWLib/GTWODBCClient.h"
#include "gateway/GTWLib/GTWODBCQuery.h"

class GTWLIB_API GTWODBCQueryEditor : public GTWBaseEditor
{
public:
  GTWODBCQueryEditor(const EditorCommandDTO &dto, GTWCollectionMember *pEditableObject, GTWODBCClient *pODBCClient,TMWTYPES_USHORT clientIndex, bool bAddMode);
  virtual ~GTWODBCQueryEditor();

  TMWTYPES_BOOL BuildSelPopupMenu(int *id,CMenuEntryArray *pMenuEntries) override;
  TMWTYPES_BOOL EditObject() override;
  TMWTYPES_BOOL SaveObject() override;
  TMWTYPES_BOOL LoadObject() override;
  TMWTYPES_BOOL AddObject(MENUENTRY_EDIT_CMD cmd) override;
  TMWTYPES_BOOL DeleteObject(GTWCollectionMember **pRefreshMember, bool bAskIfOk = true) override;
  TMWTYPES_BOOL WebReadObject(nlohmann::json &pt, bool bEditAtRuntime) override;
  TMWTYPES_BOOL ValidateObject() override;
  TMWTYPES_BOOL UpdateObject() override;
  TMWTYPES_BOOL MiscCommand(MENUENTRY_EDIT_CMD cmd) override;

  TMWTYPES_BOOL AddQuery(GTWODBCClient *pODBCClient, BOOL bCreateTags);
  nlohmann::json ShowTable(const std::string &stable);
  nlohmann::json ExecuteSql(const std::string& sQuery, const std::string& queryAlias, bool& bQueryOk);
  CStdString GetDescription(void);

  CStdString GetQueryAliasName()
  {
    return m_sQueryAliasName;
  }

  static void PostRedrawMsg(void *pCallbackParam);
  TMWTYPES_BOOL ModifyObject();


protected:
  void GetDefaults();
  void DeleteINIparms() override;

  TMWTYPES_USHORT getQueryIndex()
  {
    if (GetEditableObject() != NULL)
    {
      m_iQueryIndex = ((GTWODBCQuery*)GetEditableObject())->GetQueryIndex();
      return m_iQueryIndex;
    }
    else
    {
      m_iQueryIndex = GetNextQueryIndex();
    }
    return m_iQueryIndex;
  }

  TMWTYPES_USHORT GetNextQueryIndex()
  {
    for (TMWTYPES_USHORT i=0;i< getGTKTPARM_MAX_QUERIES_PER_ODBC_CLIENT();i++)
    {
      if (GTWConfig::ODBCQueryAliasName.TMWParam_array_was_specifieds(GetClientIndex(),i) == TMWDEFS_FALSE 
        || CStdString(GTWConfig::ODBCQueryAliasName(GetClientIndex(), i)) == "")
      {
        return i;
      }
    }
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_ODBC, GetDTOToken(), "TR_ODBC_NO_QUERIES", "No more queries available");
    return 0;
  }

  GTWODBCClient *m_pODBCClient;
  CStdString m_sQueryAliasName;
  CStdString m_sQuery;
  bool m_bODBCQueryAlwaysRefreshRS;
  bool m_bQueryChanged;

private:
  nlohmann::json TryListTables(bool bViews, bool bSystemTables);
  nlohmann::json ListTables(bool bViews, bool bSystemTables);

};

