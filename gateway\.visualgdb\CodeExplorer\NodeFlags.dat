XXNODEFLAGS        L   3F:\work\SDG-trunk\gateway\GTWLib\GTW61850Client.cpp         GTW61850Client.7/DoServerUp       GTW61850Client.10/SetupSCL       GTW61850Client.11/SetupSCL    ;F:\work\SDG-trunk\gateway\GTWWebLib\GtwBroadcastMessage.cpp         GtwBroadcastMessage/SendMessage    3F:\work\SDG-trunk\gateway\GTWLib\GTW61850Client.cpp          GTW61850Client.10/SetupSCL       GTW61850Client.11/SetupSCL    BF:\work\SDG-trunk\gateway\GTWSNLicUtils\ConfigIntegrityChecker.cpp      *   ConfigIntegrityChecker/calculate_file_hash    @F:\work\SDG-trunk\gateway\GTWSNLicUtils\ConfigIntegrityChecker.h      *   ConfigIntegrityChecker/calculate_file_hash    @F:\work\SDG-trunk\gateway\GTWSNLicUtils\ConfigIntegrityChecker.h       *   ConfigIntegrityChecker/calculate_file_hash    BF:\work\SDG-trunk\gateway\GTWSNLicUtils\ConfigIntegrityChecker.cpp       *   ConfigIntegrityChecker/calculate_file_hash    3F:\work\SDG-trunk\gateway\GTWOsUtils\GtwOsUtils.cpp         GtwOsDebugBreak    3F:\work\SDG-trunk\gateway\GTWOsUtils\GtwOsUtils.cpp          GtwOsDebugBreak    6F:\work\SDG-trunk\gateway\GTWWebLib\HttpClientBase.cpp         HttpClientBase/MakeRequest    6F:\work\SDG-trunk\gateway\GTWWebLib\HttpClientBase.cpp          HttpClientBase/MakeRequest    2F:\work\SDG-trunk\gateway\GTWRedunMnger\SSITimer.h         SSITimer/GetSystemHealth    2F:\work\SDG-trunk\gateway\GTWRedunMnger\SSITimer.h          SSITimer/GetSharedStatusInfo    4F:\work\SDG-trunk\gateway\GTWWebLib\HttpClientBase.h      )   HttpClientBase/GetSharedStatusInformation    4F:\work\SDG-trunk\gateway\GTWWebLib\HttpClientBase.h       )   HttpClientBase/GetSharedStatusInformation    4F:\work\SDG-trunk\gateway\GTWRedunMnger\SSITimer.cpp         SSITimer/checkHeartbeat    4F:\work\SDG-trunk\gateway\GTWRedunMnger\SSITimer.cpp          SSITimer/checkHeartbeat    9F:\work\SDG-trunk\gateway\GTWRedunMnger\GTWRedunMnger.cpp         main    9F:\work\SDG-trunk\gateway\GTWRedunMnger\GTWRedunMnger.cpp       
   signalHandler    3F:\work\SDG-trunk\gateway\GTWLib\GTWTASE2Client.cpp      *   GTWTASE2Client.4/handleReconnectTimerFired    3F:\work\SDG-trunk\gateway\GTWLib\GTWTASE2Client.cpp       *   GTWTASE2Client.4/handleReconnectTimerFired    5F:\work\SDG-trunk\gateway\GTWOsUtils\GtwSysConfig.cpp         GtwSysConfig/ValidateGtwConfig       GtwSysConfig.2/SaveGtwConfig    4F:\work\SDG-trunk\gateway\GTWWebLib\HttpServerBase.h         HttpServerBase/getConfig    <F:\work\SDG-trunk\gateway\GTWSettingsLib\GTWSettingsFuns.cpp         ShouldAutoGenCert    6F:\work\SDG-trunk\gateway\GTWOsUtils\TmwAsymCrypto.cpp         TmwAsymCrypto/decryptRSA    6F:\work\SDG-trunk\gateway\GTWOsUtils\TmwAsymCrypto.cpp          TmwAsymCrypto/decryptRSA    ?F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigIntegrityChecker.cpp      *   ConfigIntegrityChecker/calculate_file_hash    ?F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigIntegrityChecker.cpp       *   ConfigIntegrityChecker/calculate_file_hash    4F:\work\SDG-trunk\gateway\GTWOsUtils\cpuMemStats.cpp         PerfStats/getMem    4F:\work\SDG-trunk\gateway\GTWOsUtils\cpuMemStats.cpp          PerfStats/getMem    =F:\work\SDG-trunk\gateway\GTWOsUtils\WorkspaceMD5Verifier.cpp      +   WorkspaceMD5Verifier/calculateDirectoryHash    =F:\work\SDG-trunk\gateway\GTWOsUtils\WorkspaceMD5Verifier.cpp       +   WorkspaceMD5Verifier/calculateDirectoryHash    :F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerImpl.cpp         DestroyKeyManager    :F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerImpl.cpp          DestroyKeyManager    5F:\work\SDG-trunk\gateway\GTWOsUtils\GtwSysConfig.cpp          GtwSysConfig/SaveGtwConfig       GtwSysConfig.2/SaveGtwConfig    *F:\work\SDG-trunk\gateway\GTWLib\GTWMain.h         GTWmain/BackupWorkspace    *F:\work\SDG-trunk\gateway\GTWLib\GTWMain.h          GTWmain/BackupWorkspace    FF:\work\SDG-trunk\gateway\GTWWebMonitor\MonHttpServerImplStartStop.cpp         RestoreWorkspaceBackup    FF:\work\SDG-trunk\gateway\GTWWebMonitor\MonHttpServerImplStartStop.cpp          RestoreWorkspaceBackup    9F:\work\SDG-trunk\gateway\GTWWebMonitor\GTWWebMonitor.cpp         main    9F:\work\SDG-trunk\gateway\GTWWebMonitor\GTWWebMonitor.cpp          main    2F:\work\SDG-trunk\gateway\GTWWebLib\http_user_db.h         http_user_db/load    4F:\work\SDG-trunk\gateway\GTWWebLib\http_user_db.cpp         http_user_db/CreateUserDb    4F:\work\SDG-trunk\gateway\GTWWebLib\http_user_db.cpp          http_user_db/CreateUserDb    4F:\work\SDG-trunk\gateway\GTWOsUtils\TmwAsymCrypto.h         TmwAsymCrypto/GetDbPw    5F:\work\SDG-trunk\gateway\GTWWebLib\http_audit_db.cpp         http_audit_db/load    5F:\work\SDG-trunk\gateway\GTWWebLib\http_audit_db.cpp          http_audit_db/load    4F:\work\SDG-trunk\gateway\GTWOsUtils\TmwAsymCrypto.h          TmwAsymCrypto/GetDbPw    7F:\work\SDG-trunk\gateway\GTWSNLicUtils\LibraryLoader.h         LibraryLoader/loadLibrary    7F:\work\SDG-trunk\gateway\GTWSNLicUtils\LibraryLoader.h          LibraryLoader/loadLibrary    :F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerLoader.h      !   KeyManagerLoader/unloadKeyManager    :F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerLoader.h       !   KeyManagerLoader/unloadKeyManager    7F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerPtr.h         KeyManagerPtr/~KeyManagerPtr    7F:\work\SDG-trunk\gateway\GTWSNLicUtils\KeyManagerPtr.h          KeyManagerPtr/~KeyManagerPtr    <F:\work\SDG-trunk\gateway\GTWWebMonitor\GTWWebMonitorAPI.cpp         GTWWebMonitorAPI/MonitorStartup    <F:\work\SDG-trunk\gateway\GTWWebMonitor\GTWWebMonitorAPI.cpp          GTWWebMonitorAPI/MonitorStartup    <F:\work\SDG-trunk\gateway\GTWSettingsLib\GTWSettingsFuns.cpp          ShouldAutoGenCert    4F:\work\SDG-trunk\gateway\GTWWebLib\HttpServerBase.h       #   HttpServerBase/DoSetLogFilterConfig    4F:\work\SDG-trunk\gateway\GTWOsUtils\icmp_pinger.cpp         ping    4F:\work\SDG-trunk\gateway\GTWOsUtils\icmp_pinger.cpp          ping    2F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigValue.h         ConfigValue/fromJson    2F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigValue.h          ConfigValue/fromJson    4F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigValue.cpp         isPortInUse    4F:\work\SDG-trunk\gateway\GTWOsUtils\ConfigValue.cpp          isPortInUse    .F:\work\SDG-trunk\gateway\GTWOsUtils\GtwTime.h         GtwTimeZone/asDayOfWeek    .F:\work\SDG-trunk\gateway\GTWOsUtils\GtwTime.h          GtwTimeZone/asDayOfWeek    :F:\work\SDG-trunk\gateway\GTWLib\GTWHttpServerImplEditor.h      1   GTWHttpServerImplEditor/DoGetDefineLocalParameter    :F:\work\SDG-trunk\gateway\GTWLib\GTWHttpServerImplEditor.h       1   GTWHttpServerImplEditor/DoGetDefineLocalParameter    9F:\work\SDG-trunk\gateway\GTWRedunMnger\RedunHTTPServer.h      #   RedunHTTPServer/UpdateAndSaveConfig    9F:\work\SDG-trunk\gateway\GTWRedunMnger\RedunHTTPServer.h       #   RedunHTTPServer/UpdateAndSaveConfig    ,F:\work\SDG-trunk\gateway\GTWLib\GTWMain.cpp      	   OnTimer.4    7F:\work\SDG-trunk\gateway\GTWWebMonitor\MonHttpServer.h      !   MonHttpServer/UpdateAndSaveConfig    8F:\work\SDG-trunk\gateway\GTWWebLib\HttpServerCommon.cpp         HttpServerCommon/genConfig    <F:\work\SDG-trunk\gateway\GTWRedunMnger\GTWRedunMngerAPI.cpp      "   GTWRedunMngerAPI/SetMeAsActivePeer    1F:\work\SDG-trunk\gateway\GTWEngine\GTWEngine.cpp         main    1F:\work\SDG-trunk\gateway\GTWEngine\GTWEngine.cpp          main    