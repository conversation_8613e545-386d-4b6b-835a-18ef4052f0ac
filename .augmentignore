# SVN directories and files
.svn/
**/.svn/
*.svn-base
*.svn-work

# Other version control
.git/
.hg/

# Build artifacts
**/VisualGDB/
**/Debug/
**/Release/
**/x64/
**/bin/
**/obj/

# IDE files
*.user
*.suo
*.sdf
*.opensdf
```
</augmentignore>

This will exclude all SVN-related files and directories from Augment's indexing, giving you cleaner search results that focus on your actual source code.

After creating this file, restart your editor or trigger a workspace re-index so Augment picks up the new ignore rules.