#pragma once
#include "RedunHTTPServer.h"
#include "gateway/GTWOsUtils/simple_timer.h"
#include "gateway/GTWOsUtils/cpuMemStats.h"
#include "GTWWebLib/HttpClientBase.h"
#include "GTWOsUtils/GtwLogger.h"

// Configuration parameters
const int HEARTBEAT_TIMEOUT = 5; // Seconds
const int GET_SSI_PERIOD = 1000; // Milli Seconds

class SSITimer
{
public:
  SSITimer(void *param);
  ~SSITimer();

  template <typename SOCK_TYP>
  void Start(unsigned long period)
  {
    _timer.start_periodic(period, this, &SSITimer::doWork<SOCK_TYP>);
  }
  void Stop();

private:
  PerfStats ps;
  bool bLastHealth;
  bool bFirstTime;
  template <typename SOCK_TYP>
  void doWork()
  {
    RedunHTTPServer<SOCK_TYP> *pServer = static_cast<RedunHTTPServer<SOCK_TYP> *>(_param);
    GetSystemHealth<SOCK_TYP>(pServer);

    //ScopedLock lock(RedunHTTPServerImplRestApis::RemoteSSIInfoLock);

    //GetSharedStatusInfo<SOCK_TYP>(pServer);

    //checkHeartbeat(RedunHTTPServerImplRestApis::g_RemoteSSIInfo);

    //CheckAuthTokens<SOCK_TYP>(pServer);
  }
  SimpleTimer _timer;
  void *_param;

  static bool isActive;
  static uint16_t expectedCounter;
  static bool hasWrappedAround;
  static REDUNDANCY_STATUS_ENUM lastStatus;
  static std::thread heartbeatThread;
  static int monitorReStartRetryCount;
  static int engineReStartRetryCount;

  // Function to process received heartbeat
  static void processHeartbeat(SharedStatusInfo& SsiInfo);

  // Function to check partner's heartbeat
  static void checkHeartbeat();

  template <typename SOCK_TYP>
  void CheckAuthTokens(RedunHTTPServer<SOCK_TYP> *pServer)
  {
    if (GtwSysConfig::gtwDoAuth() == false)
    {
      return;
    }
    pServer->user_db.checkTokens();

  }
  
  template <typename SOCK_TYP>
  void GetSharedStatusInfo(RedunHTTPServer<SOCK_TYP> *pServer)
  {
	  try
    {
      bool bSSI = HttpClientBase::GetSharedStatusInformation(GtwSysConfig::GetREDHostAddrOfPeer(), false, false, RedunHTTPServerImplRestApis::g_RemoteSSIInfo, RedunHTTPServerImplRestApis::RemoteSSIInfoLock);
      if (bSSI == true)
      {
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Redundancy, nullptr, "get shared status info: %s", "Success");
      }
      else
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "failed to get shared status info: %s", "RedunMnger not running? check logs");
      }

    }
    catch (const std::exception &e)
    {
      LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Redundancy, nullptr, "failed to get shared status info: %s", e.what());
    }
  
  }

  template <typename SOCK_TYP>
  void GetSystemHealth(RedunHTTPServer<SOCK_TYP>* pServer)
  {
      double monitorCpu = -1.0;
      double monitorMem = -1.0;
      MONITOR_STATE_ENUM monitorState = MONITOR_STATE_ENUM::MON_NOT_RUNNING;
      double redCpu = -1.0;
      double redMem = -1.0;
      double engineCpu = -1.0;
      double engineMem = -1.0;
      std::string iniPath = "";
      bool isIniCsvDirty = false;
      bool isRedSyncNeeded = false;

      bool redundantHealthy = false;
      bool configSyncRequired = false;
      int numConfiguredSlaveDevices = 0;
      int numHealthySlaveDevices = 0;
      int numConfiguredMasterDevices = 0;
      int numHealthyMasterDevices = 0;
      ENGINE_STATE_ENUM engineState = ENGINE_STATE_ENUM::ENG_NOT_RUNNING;

      try
      {

        bool bHealth = HttpClientBase::GetMonitorHealthRequest(monitorState, monitorCpu, monitorMem);
        if (!bHealth)
        {
          SSITimer::monitorReStartRetryCount++;
          if (SSITimer::monitorReStartRetryCount <= GtwSysConfig::redMonitorReStartRetryLimit())
          {
            // Consider health still OK, attempt to restart the monitor
            StartMonitor();
            bHealth = true;  // Assume health is still OK for now
          }
          else
          {
            // Monitor is truly down
            SSITimer::monitorReStartRetryCount = 0;  // Reset for next time
          }
        }
        else
        {
          SSITimer::monitorReStartRetryCount = 0;  // Reset on successful health check
        }

        if (bHealth != bLastHealth || bFirstTime == true)
        {
          GtwWebConfig::LoadGtwConfigUsingMonitor();

          if (bHealth == false)
          {
#ifdef _WIN32
            //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "TR_ENGINE_STOPPED_WINDOWS", "Gateway Engine is not running (check the SDG Engine logs or the Windows event log and services manager).");
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "%s", "Gateway Monitor is not running or re-starting (check the Monitor logs or the Windows event log and services manager).");
#else
            //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "TR_ENGINE_STOPPED_LINUX", "Gateway Engine is not running (check the SDG Engine logs or issue 'systemctl status tmwsdg-engine' in a Linux shell for possible errors).");
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "%s", "Gateway Monitor is not running or re-starting (check the Monitor logs or issue 'systemctl status tmwsdg-monitor' in a Linux shell for possible errors).");
#endif 
          }
        }
        bLastHealth = bHealth;
        bFirstTime = false;


        ScopedLock lock(RedunHTTPServerImplRestApis::LocalSSIInfoLock);
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.monitorState = monitorState;

      }
      catch (const std::exception& e)
      {
        LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_HTTP, nullptr, "failed to get Monitor health: %s", e.what());
        monitorState = MON_NOT_RUNNING;
        monitorCpu = -1.0;
        monitorMem = -1.0;
      }


      try
      {
        ps.getAll();
        redCpu = ps.cpuPctMe;
        if (std::isnan(redCpu))
        {
          redCpu = 0.0;
        }

        // Use physical memory (RSS) consistently, like top command
        redMem = ((double)ps.physMemUsedByMe / (double)ps.totalPhysMem) * 100.0;
        bool bAmIActive = false;

        {
          ScopedLock lock(RedunHTTPServerImplRestApis::LocalSSIInfoLock);
          bAmIActive = RedunHTTPServerImplRestApis::g_LocalSSIInfo.isRedActive;
        }

        bool bHealth = HttpClientBase::GetEngineHealthRequest(engineState, engineCpu, engineMem, iniPath, isIniCsvDirty, isRedSyncNeeded, numConfiguredSlaveDevices, numHealthySlaveDevices, numConfiguredMasterDevices, numHealthyMasterDevices);
        if (!bHealth)
        {
          SSITimer::engineReStartRetryCount++;
          if (SSITimer::engineReStartRetryCount <= GtwSysConfig::redEngineReStartRetryLimit())
          {
            // Consider health still OK, attempt to restart the engine
            StartEngine();
            bHealth = true;  // Assume health is still OK for now
          }
          else
          {
            // Engine is truly down
            SSITimer::engineReStartRetryCount = 0;  // Reset for next time
          }
        }
        else
        {
          SSITimer::engineReStartRetryCount = 0;  // Reset on successful health check
        }

        if (engineState != ENG_RUNNING || monitorState != MON_RUNNING)
        { // engine and monitor must be running to be active
          {
            ScopedLock lock(RedunHTTPServerImplRestApis::LocalSSIInfoLock);
            bAmIActive = RedunHTTPServerImplRestApis::g_LocalSSIInfo.isRedActive = false;
          }
        }
        
        if (bAmIActive == false)
        {
          // if we are not active we do not consider health
          numHealthyMasterDevices = numConfiguredMasterDevices;
          numHealthySlaveDevices = numConfiguredSlaveDevices;
        }

        if (bHealth != bLastHealth || bFirstTime == true)
        {
          GtwWebConfig::LoadGtwConfigUsingMonitor();

          if (bHealth == false && HttpClientBase::shutDownEngineRequestReceived == false)
          {
#ifdef _WIN32
            //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "TR_ENGINE_STOPPED_WINDOWS", "Gateway Engine is not running (check the SDG Engine logs or the Windows event log and services manager).");
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "%s", "Gateway Engine is not running or re-starting (check the SDG Engine logs or the Windows event log and services manager).");
#else
            //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "TR_ENGINE_STOPPED_LINUX", "Gateway Engine is not running (check the SDG Engine logs or issue 'systemctl status tmwsdg-engine' in a Linux shell for possible errors).");
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "%s", "Gateway Engine is not running or re-starting (check the SDG Engine logs or issue 'systemctl status tmwsdg-engine' in a Linux shell for possible errors).");
#endif 
          }
          else if (HttpClientBase::shutDownEngineRequestReceived == false)
          {
            nlohmann::json args = {};
            args["refreshWebBrowser"] = true;
            BroadcastEventDTOMsg dto(DestinationMaskEnum::Destination_Command, BroadcastTypeEnum::Broadcast_Refresh_UI, "", "", nullptr, args);
            HttpServerCommon::_SendWebTextMessage(dto);

            //GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Warning, GtwLogger::SDG_Category_Start_Stop, nullptr, "TR_ENGINE_STARTED", "Gateway Engine is running.");
          }
          else if (bHealth)
          {
            HttpClientBase::shutDownEngineRequestReceived = false;
          }
        }
        bLastHealth = bHealth;
        bFirstTime = false;

        double slaveTolerancePercentage = GtwSysConfig::redSlaveTolerancePercentage();
        bool slaveReconfigurationNeeded = isReconfigurationNeeded(numConfiguredSlaveDevices, numHealthySlaveDevices, slaveTolerancePercentage);

        double masterTolerancePercentage = GtwSysConfig::redMasterTolerancePercentage();
        bool masterReconfigurationNeeded = isReconfigurationNeeded(numConfiguredMasterDevices, numHealthyMasterDevices, masterTolerancePercentage);


        // Set the redundantHealthy bit based on the health status of master and slave devices and engine and monitor is running
        redundantHealthy = masterReconfigurationNeeded == false && slaveReconfigurationNeeded == false && engineState == ENG_RUNNING && monitorState == MON_RUNNING;

        configSyncRequired = isIniCsvDirty == false && isRedSyncNeeded == true;

        if (configSyncRequired)
        {
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "%s", "Configuration synchronization is needed due to changed peer sdg configuration. ");
        }

        // log why not healthy
        if (redundantHealthy == false)
        {
          std::string unHealthyReason = "";
          if (slaveReconfigurationNeeded)
          {
            unHealthyReason += "slave tolerance exceeded";
          }
          if (masterReconfigurationNeeded)
          {
            unHealthyReason += "master tolerance exceeded";
          }
          if (engineState != ENG_RUNNING)
          {
            unHealthyReason += "engine state: " + ENGINE_STATE_ENUM_to_hr(engineState) + " ";
          }
          if (monitorState != MON_RUNNING)
          {
            unHealthyReason += "monitor state: " + MONITOR_STATE_ENUM_to_hr(monitorState) + " ";
          }

          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "Potential unhealthy local sdg. reason: %s", unHealthyReason.c_str());
        }
        //else
        //{
        //  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_HTTP, nullptr, "No re-configuration needed. %s", "");
        //}

        pServer->SetHealth(engineState, monitorState, redCpu, redMem, monitorCpu, monitorMem, engineCpu, engineMem, redundantHealthy, configSyncRequired);

        ScopedLock lock(RedunHTTPServerImplRestApis::LocalSSIInfoLock);
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.numSlaveDevices = numConfiguredSlaveDevices;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.numSlaveDevicesHealthy = numHealthySlaveDevices;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.numMasterDevices = numConfiguredMasterDevices;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.numMasterDevicesHealthy = numHealthyMasterDevices;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.redundantHealthy = redundantHealthy;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.configSyncRequired = configSyncRequired;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.engineState = engineState;
        RedunHTTPServerImplRestApis::g_LocalSSIInfo.monitorState = monitorState;

        std::string hash = "";
        std::string err;
        std::string dir = GtwSysConfig::getCurrentWorkSpacePath();

        std::vector<std::string> excludeExtensions = { ".txt", ".der", ".key", ".pem", ".csv_hash", ".ini_hash" };
        std::vector<std::string> excludeDirs = { "opcua_certs", "SDGLogs", "SOELogs" };

        bool hashOk = calculateMD5(
          dir, 
          hash, 
          err,
          excludeExtensions,
          excludeDirs);
        if (hashOk)
        {
          RedunHTTPServerImplRestApis::g_LocalSSIInfo.workSpaceHash = hash;
        }
        else
        {
          RedunHTTPServerImplRestApis::g_LocalSSIInfo.workSpaceHash = GtwSysConfig::redPrimary() ? "Error on Primary" : "Error on Backup";
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Redundancy, nullptr, "Failed to get work space hash for: %s, err: %s", GtwSysConfig::getCurrentWorkSpacePath().c_str(), err.c_str());

        }

        return;
      }
      catch (const std::exception& e)
      {
        LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_HTTP, nullptr, "failed to get Engine health: %s", e.what());
        engineState = ENG_NOT_RUNNING;
        engineCpu = -1.0;
        engineMem = -1.0;
      }
      pServer->SetHealth(engineState, monitorState, redCpu, redMem, monitorCpu, monitorMem, engineCpu, engineMem, redundantHealthy, configSyncRequired);

  }



  static bool runHeartBeat;
  bool isReconfigurationNeeded(int totalDevices, int healthyDevices, double tolerancePercentage);
};

