// GTWEngine.cpp : Defines the entry point for the gateway engine.
//
#include "stdafx.h"
#include "gateway/GTWOsUtils/ServiceController.h"
#include "GtwEngineService.h"
#include "gateway/GTWOsUtils/ParseCmdLine.h"
#include "gateway/GTWLib/GTWLibApi.h"
#include "../GTWWebLib/svn_rev.h"
#include "../GTWWebLib/GtwWebConfig.h"
#include "../GTWWebLib/status_code.hpp"
#include "../GTWSNLicUtils/Crypto.h"
#include "Common/SQLLite/SQLLiteDB.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

// define this to run and exit so we can see some leaks
//#define MEM_LEAK 1

// define this to enable heap profiling
//#define MEM_PROFILE 1

// Add these includes for heap monitoring
#include <ctime>
#include <cstdio>
#ifndef _WIN32
#include <unistd.h>  // for getpid()
#endif
#ifndef _WIN32
#include <malloc.h>
#else
#include <windows.h>
#include <psapi.h>
#endif

// supported command line args/options
// -$               : prompt for INI file (will start monitor if not running)
// -m               : set monitor location i.e. m*************:58090
// -stopSDG        : tell other/running SDG to stop
// -embedding      : started by opc client and starts monitor
// -install        : setup to run as a service and reg opc only for windows
// -remove         : remove setup to run as service and unreg opc only for windows
// -service        : running as service
// -regopcserver   : register as opc server and not a service
// -unregopcserver : unregister as opc server and not a service
// -memleak[=time] : enable memory leak detection and early exit (default 1 minute, specify time in minutes)
// -memprofile[=time] : enable heap/memory profiling to file (default 15 minutes, specify time in minutes)
// -h or -help     : show command line options
// 
// these args/options can not be used in combination

void ShowHelp()
{
  printf("GTWEngine - SCADA Data Gateway Protocol Mapping Engine\n\n");
  printf("Usage: GTWEngine [options]\n\n");
  printf("Options:\n");
  printf("  -$                 Prompt for INI file (will start monitor if not running)\n");
  printf("  -ini=<filename>    Specify INI file to use\n");
  printf("  -m=<addr:port>     Set monitor location (e.g. -m=*************:58090)\n");
  printf("  -stopSDG           Tell other/running SDG to stop\n");
  printf("  -crashSDG          Tell other/running SDG to crash\n");
#ifdef _WIN32
  printf("  -embedding         Started by OPC client and starts monitor\n");
  printf("  -install           Setup to run as a service and register OPC\n");
  printf("  -remove            Remove service setup and unregister OPC\n");
  printf("  -service           Running as service\n");
  printf("  -regopcserver      Register as OPC server (not as service)\n");
  printf("  -unregopcserver    Unregister as OPC server (not as service)\n");
#endif
  printf("  -memleak[=minutes] Enable memory leak detection and early exit\n");
  printf("                     (default: 1 minute, specify time in minutes)\n");
  printf("  -memprofile[=minutes] Enable heap/memory profiling to file\n");
  printf("                     (default: 15 minutes, specify interval in minutes)\n");
  printf("  -h, -help          Show this help message\n");
  printf("\nMemory Debugging:\n");
  printf("  -memleak           Run for 1 minute then exit (for leak detection)\n");
  printf("  -memleak=5         Run for 5 minutes then exit\n");
  printf("  -memprofile        Log memory stats to heap_stats.xml every 15 minutes\n");
  printf("  -memprofile=5      Log memory stats every 5 minutes\n");
  printf("  -memprofile=60     Log memory stats every hour\n");
  printf("\nExamples:\n");
  printf("  GTWEngine                    Normal operation\n");
  printf("  GTWEngine -memleak=10        Run for 10 minutes for leak testing\n");
  printf("  GTWEngine -memprofile        Run with memory logging every 15 minutes\n");
  printf("  GTWEngine -memprofile=5      Run with memory logging every 5 minutes\n");
  printf("  GTWEngine -m=192.168.1.100:58090  Connect to specific monitor\n");
  printf("\nNote: Some options cannot be used in combination.\n");
}

int main(int argc, const char *argv[])
{  
  GtwSysConfig::AppType() = APP_TYPE::APP_ENGINE;

  TRACE("%s\n", "Sarting Engine");

  //GtwOsSleep(45000); //use to atattch debugger

  CParseCmdLine cmdLine(argc, argv);
   
#ifdef _WIN32
  _CrtSetDbgFlag((_CRTDBG_LEAK_CHECK_DF) | (_CRTDBG_ALLOC_MEM_DF) | _CrtSetDbgFlag(_CRTDBG_REPORT_FLAG));
  //_CrtSetBreakAlloc(25821027);
  //DebugBreak();
#endif
  // test leak report
  //void* ppp = malloc(1000);

  bool stopSDG = false;
  bool crashSDG = false;
  bool runAsService = false;
  bool promptForINI = false;
  bool startMonitor = false;
  bool startMonitorLocation = false;
  bool enableMemLeak = false;
  bool enableMemProfile = false;
  int memLeakDurationMinutes = 1; // Default 1 minute
  int memProfileIntervalMinutes = 15; // Default 15 minutes
  CStdString monAddress;
  CStdString cmdINIFileName;
  int monPort = -1;
  tmw::String option;
  tmw::String value;
  for (int i = 0; i < cmdLine.GetNumOpts(); i++)
  {
    cmdLine.FetchOpt(i, option, value);
    if (option.equalsNoCase("$"))
    {
      promptForINI = true;
      startMonitor = true;
    }
    //else if (option.equalsNoCase("@"))
    //{
    //  startMonitor = true;
    //}
    else if (option.equalsNoCase("ini"))
    {
      cmdINIFileName = (const char*)value;
    }
    else if (option.equalsNoCase("m"))
    {
      startMonitorLocation = true;
      // Expects value in the format *************:58090
      tmw::Array<tmw::String> strings;
      tmw::util::singleStringToMultipleStrings((const char*)value, ":", strings, true);
      if (strings.size() != 2)
        return FAIL_BAD_MONITOR_ADDR;
      monAddress = (const char*)strings[0];
      monPort = atoi(strings[1]);
      if (monPort <= 0 || monPort > 65535)
        return FAIL_BAD_MONITOR_ADDR;
    }
    else if (option.equalsNoCase("stopsdg"))
    {
      stopSDG = true;
    }
    else if (option.equalsNoCase("crashsdg"))
    {
      crashSDG = true;
    }
    else if (option.equalsNoCase("memleak"))
    {
      enableMemLeak = true;
      if (!value.isEmpty())
      {
        memLeakDurationMinutes = atoi(value);
        if (memLeakDurationMinutes <= 0)
          memLeakDurationMinutes = 1; // Fallback to 1 minute if invalid
      }
    }
    else if (option.equalsNoCase("memprofile"))
    {
      enableMemProfile = true;
      if (!value.isEmpty())
      {
        memProfileIntervalMinutes = atoi(value);
        if (memProfileIntervalMinutes <= 0)
          memProfileIntervalMinutes = 15; // Fallback to 15 minutes if invalid
      }
    }
    else if (option.equalsNoCase("h") || option.equalsNoCase("help"))
    {
      ShowHelp();
      return EXIT_SUCCESS;
    }
#ifdef _WIN32
    else if (option.equalsNoCase("regopcserver") || option.equalsNoCase("unregopcserver"))
    {
      ServiceController::UninstallService("GTWService");
      CStdString cmdLine = CStdString(argv[0]) + " " + CStdString(argv[1]);
      return GTWLibApi::OpcServerRegistration(cmdLine, "", "SCADA Data Gateway Protocol Mapping Engine Service");
    }
    else if (option.equalsNoCase("embedding"))
    {
      startMonitor = true;
    }
    else if (option.equalsNoCase("install"))
    {
      CStdString cmdLine = CStdString(argv[0]) + " -regopcserver";
      GTWLibApi::OpcServerRegistration(cmdLine, "GTWService", "SCADA Data Gateway Protocol Mapping Engine Service");
      //ServiceController::InstallService("GTWService", "GTWService", "SCADA Data Gateway Protocol Mapping Engine Service", SERVICE_DEMAND_START, nullptr, nullptr, nullptr);
      return _SUCCESS;
    }
    else if (option.equalsNoCase("remove"))
    {
      CStdString cmdLine = CStdString(argv[0]) + " -unregopcserver";
      GTWLibApi::OpcServerRegistration(cmdLine, "GTWService", "SCADA Data Gateway Protocol Mapping Engine Service");
      //ServiceController::UninstallService("GTWService");
      return _SUCCESS;
    }
    else if (option.equalsNoCase("service"))
    {
      runAsService = true;
    }
#endif
  }

#ifdef _WIN32
#ifndef _DEBUG
  runAsService = true;
#endif
  if (runAsService)
  {
    GtwEngineService service("GTWService");
    if (!CServiceBase::Run(service))
    {
      service.WriteEventLogEntry("Failed To start, not a service", EVENTLOG_ERROR_TYPE);
      return FAIL_ERROR;
    }

    service.WriteEventLogEntry("GTWService Exit", EVENTLOG_INFORMATION_TYPE);
  }
  else
#endif
  {
    // log engine start to audit db
    void *ar_startup = CommonApi::CreateSysAuditRecord("Engine startup");

    if (ENGINE_EXIT_FAIL_STATE_ENUM retVal = GTWLibApi::EngineStartup(APP_TYPE::APP_ENGINE, runAsService, stopSDG, crashSDG, promptForINI, startMonitor, startMonitorLocation, monAddress, monPort, cmdINIFileName); retVal != _SUCCESS)
    {
      CStdString msg;
      msg.Format(" failed exit code: %d (%s)", retVal, getExitCodeAsString(retVal));
      CommonApi::AppendAuditRecord(ar_startup, msg.c_str());
      CommonApi::AddAuditRecord(ar_startup);
      CommonApi::DeleteSysAuditRecord(ar_startup);

      GTWLibApi::setEngineExitFailState(retVal);
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", msg.c_str());
      GTWLibApi::EngineShutdown(true);
      GTWLibApi::SetShuttingDown(TmwCrypto::UP_NONE_AUTH_TOKEN());


#ifndef _WIN32
      // Special handling for license failure
      if (retVal == FAIL_NO_LICENSE)
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Start_Stop, nullptr, "%s", "License Error - Service will attempt restart in 60 seconds");
        GtwOsSleep(45000); // Wait 45 + 15 from the .service file seconds to avoid thrashing
        _exit(_SUCCESS); // Exit as "success" to trigger systemd restart
      }
#endif

#ifdef _WIN32
      TerminateProcess(GetCurrentProcess(), retVal);
#else
      _exit(retVal);
#endif // _WIN32

    }
    else
    {
      CommonApi::AppendAuditRecord(ar_startup, " success");
      CommonApi::AddAuditRecord(ar_startup);
      CommonApi::DeleteSysAuditRecord(ar_startup);
    }

    // Initialize heap monitoring variables
    static time_t last_dump = 0;

    int loopCnt = 0;
    while (true)
    {
      if (GTWLibApi::IsShuttingDown() == true && GTWLibApi::CanShutdown() == true)
      {
        break;
      }
      
      // Heap monitoring - dump memory info at specified interval
      if (enableMemProfile)
      {
        time_t now = time(NULL);
        int profileIntervalSeconds = memProfileIntervalMinutes * 60;
        if (now - last_dump >= profileIntervalSeconds) {
          // Create unique filename with timestamp and process info
          char filename[256];
          struct tm* tm_info = localtime(&now);
#ifndef _WIN32
          snprintf(filename, sizeof(filename), "heap_stats_GTWEngine_%04d%02d%02d_%02d%02d%02d_%d.xml", 
                   tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                   tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, getpid());
#else
          snprintf(filename, sizeof(filename), "heap_stats_GTWEngine_%04d%02d%02d_%02d%02d%02d_%lu.xml", 
                   tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                   tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, GetCurrentProcessId());
#endif
          
          FILE* heap_file = fopen(filename, "w"); // Use "w" since filename is unique
          if (heap_file != nullptr) {
            // Add timestamp header
            struct tm* tm_info = localtime(&now);
            char time_buffer[64];
            strftime(time_buffer, sizeof(time_buffer), "%Y-%m-%d %H:%M:%S", tm_info);
            fprintf(heap_file, "\n<!-- Heap dump at %s -->\n", time_buffer);
            
            // Write memory info output
#ifndef _WIN32
            malloc_info(0, heap_file);
            
            // When using jemalloc, malloc_info returns zeros
            // So also log system memory stats from /proc/self/status
            FILE* status_file = fopen("/proc/self/status", "r");
            if (status_file) {
              fprintf(heap_file, "<!-- System Memory Stats from /proc/self/status -->\n");
              char line[256];
              while (fgets(line, sizeof(line), status_file)) {
                if (strncmp(line, "VmRSS:", 6) == 0 || 
                    strncmp(line, "VmSize:", 7) == 0 ||
                    strncmp(line, "VmData:", 7) == 0 ||
                    strncmp(line, "VmStk:", 6) == 0 ||
                    strncmp(line, "VmExe:", 6) == 0 ||
                    strncmp(line, "VmLib:", 6) == 0 ||
                    strncmp(line, "VmHWM:", 6) == 0 ||
                    strncmp(line, "VmPeak:", 7) == 0) {
                  // Remove newline and add as XML comment
                  line[strcspn(line, "\n")] = 0;
                  fprintf(heap_file, "<!-- %s -->\n", line);
                }
              }
              fclose(status_file);
            }
#else
            // Windows memory statistics
            PROCESS_MEMORY_COUNTERS_EX pmc;
            if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
              fprintf(heap_file, "<!-- Windows Memory Stats -->\n");
              fprintf(heap_file, "<memory>\n");
              fprintf(heap_file, "  <working_set>%zu</working_set>\n", pmc.WorkingSetSize);
              fprintf(heap_file, "  <private_usage>%zu</private_usage>\n", pmc.PrivateUsage);
              fprintf(heap_file, "  <peak_working_set>%zu</peak_working_set>\n", pmc.PeakWorkingSetSize);
              fprintf(heap_file, "  <peak_private_usage>%zu</peak_private_usage>\n", pmc.PeakPagefileUsage);
              fprintf(heap_file, "  <pagefile_usage>%zu</pagefile_usage>\n", pmc.PagefileUsage);
              fprintf(heap_file, "</memory>\n");
            } else {
              fprintf(heap_file, "<!-- Failed to get Windows memory stats -->\n");
            }
#endif
            fprintf(heap_file, "\n");
            
            fclose(heap_file);
          }
          last_dump = now;
        }
      }
      
      GtwOsSleep(100);
      //for (int i=0;i<10000;i++)
      //{
      //  GTWLibApi::sendLogInfo("this is a test");
      //}

      if (enableMemLeak)
      {
        // Convert minutes to loop iterations (each loop is 100ms, so 600 iterations = 1 minute)
        int maxLoopCount = memLeakDurationMinutes * 600;
        if (loopCnt > maxLoopCount)
        {
          break;
        }
        ++loopCnt;
      }
    }

    // Don't UN-comment this unless you are looking for memory leaks
    // and you disable the while(true) loop above and 
    // comment out the web server startup
    //GTWLibApi::SetShuttingDown();
    //GtwOsSleep(5000);
    void *ar_shutdown = CommonApi::CreateSysAuditRecord("Engine shutdown");
    CommonApi::AddAuditRecord(ar_shutdown);
    CommonApi::DeleteSysAuditRecord(ar_shutdown);

    if (GtwSysConfig::fullLogOnRestart())
    {
      GtwSysConfig::fullLogOnRestart() = false;
      GtwWebConfig::SaveGtwConfig(false);
    }

    if (GtwSysConfig::gtwDoFastShutdown() == true)
    {
#ifdef _WIN32
      TerminateProcess(GetCurrentProcess(), _SUCCESS);
#else
      _exit(_SUCCESS);
#endif // _WIN32
    }

    if (GtwSysConfig::gtwDoFastShutdown() == false)
    {
      int r = GTWLibApi::EngineShutdown(false);
      tmwSQLLite::SQLLiteDB::ShutDown();
      return r;
    }
  }


  return _SUCCESS;
}