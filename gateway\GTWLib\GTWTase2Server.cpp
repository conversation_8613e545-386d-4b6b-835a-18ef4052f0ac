/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2008 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWTase2Server.cpp                                          */
/* DESCRIPTION:  Implementation of the Tase2 Server                          */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif
#include "GTWTASE2Client.h"
#include "GTWTase2SlaveDataObject.h"
#include "GTWTASE2Server.h"
#include "GTWTase2ServerEditor.h"
#include <memory>
#include "../GTWOsUtils/TMWStringUtils.h"
#include "GTWTase2VCCFolder.h"

#ifdef DEBUG
static bool g_bTurnOnDataSimulation = false;
static unsigned int g_iSimulateThrottleTimeMS = 5000;
#endif

//#define DEBUG_TASE2_SERVER

ImplementClassBaseInfo (GTWTase2Server,GTWCollectionListParent,pClassInfo,NULL);

const TMWTYPES_UINT NEXT_COUNT = 100; // number of elements to request when calling Next

#define FULL_TEST 1 // define this to test more interfaces

ITASE2ServerList GTWTase2Server::m_ServerList; // the one and only Tase2 server list

bool GTWTase2Server::m_bIsLicensed = false;

class t2GTWNumClients : public GTWInternalCounter
{
private:
  virtual CStdString GetBaseName()
  {
    return "NumberOfClientsConnected";
  }

public:
  t2GTWNumClients() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  bool isWriteable() override { return false; }
  bool IsCommandDataType() override { return false; }
};

class t2GTWBlockAllControlsMDO : public GTWInternalUserDataObjectBool
{
private:
  virtual CStdString GetBaseName()
  {
    return "BlockAllControls";
  }

public:
  t2GTWBlockAllControlsMDO() :
    GTWInternalUserDataObjectBool(false)
  {
  }

  virtual bool SetCommandValue(GtwVariant& value) override
  {
    SetValue(value.GetBooleanValue());
    return true;
  }

  virtual bool isWriteable() override
  {
    return true;
  }

  virtual bool IsDeletable() override
  {
    return false;
  }
};

struct ICCPDataChangeRecord
{
  GTWTase2SlaveDataObject* m_pSDO;
  tmwTase2::Tase2DataAttribute* m_pDA;
  GTWDEFS_STD_QLTY m_stdQuality;
  bool m_bIsTimeDA; // if m_pDA points to a time data attribute
  bool m_bIsTimeExtDA; // if m_pDA point to an extended time data attribute
};

OnTase2DataChangeStruct::OnTase2DataChangeStruct() :
  m_pDa(NULL)
{
}

void OnTase2DataChangeStruct::onActivate()
{
  m_pDa = NULL;
  //  reason = tmw::Server::Reason_Unknown; 
}

void show_Tase2_error(const char *format,...)
{
  char Buffer[2048];
  va_list argpointer;
  int Result;

  va_start(argpointer, format);
  Result = vsprintf(Buffer, format, argpointer);

  LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Tase2, nullptr, "%s",Buffer);
  va_end(argpointer);
}

void show_Tase2_msg(const char *format,...)
{
  char Buffer[2048];
  va_list argpointer;
  int Result;

  va_start(argpointer, format);
  Result = vsprintf(Buffer, format, argpointer);

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%s",Buffer);
  
  va_end(argpointer);
}

GTWDEFS_CTRL_STAT t2GTWServerChannelActive::SetValue(bool newValue)
{
  if (GetValue() != newValue)
  {
    if (newValue)
    {
      if (!m_pServer->GetServerConnection()->IsConnectionAliveAndReady())
        newValue = m_pServer->Start(false);
    }
    else if (m_pServer->GetServerConnection()->IsConnectionAliveAndReady())
    {
      m_pServer->Disconnect();
    }
    GTWInternalDataObjectBool::SetValue(newValue);
    if (m_pServer->IsBidirectional() && newValue)
    {
      if (m_pServer->GetBidirClient()->IsInitiator() && m_pServer->GetBidirClient()->IsChannelActive())
      {
        m_pServer->GetBidirClient()->Connect();
      }
      else
      {
        if (m_pServer->GetBidirClient()->IsInitiator() == false)
        {
          LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_Tase2, nullptr, "Note : Bidirectional Client '%s' will not automatically connect because it is not the initiator.", m_pServer->GetBidirClient()->GetFullName().c_str());
        }
        else
        {
          LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_Tase2, nullptr, "Note : Bidirectional Client '%s' will not automatically connect because its ChannelActiveControl is false.", m_pServer->GetBidirClient()->GetFullName().c_str());
        }
      }
    }
  }
  return GTWDEFS_CTRL_STAT_SUCCESS;
}

bool GTWTase2Server::IsActive()
{
  return m_pChannelActiveMdo->GetValue();
}

void GTWTase2Server::AddTimeChange(GTWTase2SlaveDataObject* pSDO, tmwTase2::Tase2DataAttribute* pTase2Time, bool bIsExt)
{
  tmw::CriticalSectionLock queueLock(m_DataChangeQueueCS);

  ICCPDataChangeRecord dcr;
  dcr.m_pSDO = pSDO;
  dcr.m_pDA = pTase2Time;
  dcr.m_stdQuality = 0;
  dcr.m_bIsTimeDA = !bIsExt;
  dcr.m_bIsTimeExtDA = bIsExt;

  m_DataChangeQueue.push_back(dcr);
}

void GTWTase2Server::AddDataChange(GTWTase2SlaveDataObject* pSDO, tmwTase2::Tase2DataAttribute* pDA, GTWDEFS_STD_QLTY stdQuality)
{
  tmw::CriticalSectionLock queueLock(m_DataChangeQueueCS);

  ICCPDataChangeRecord dcr;
  dcr.m_pSDO = pSDO;
  dcr.m_pDA = pDA;
  dcr.m_stdQuality = stdQuality;
  dcr.m_bIsTimeDA = false;
  dcr.m_bIsTimeExtDA = false;

  m_DataChangeQueue.push_back(dcr);
}

void GTWTase2Server::GetDataChangeRecords(std::list<ICCPDataChangeRecord>& records)
{
  tmw::CriticalSectionLock queueLock(m_DataChangeQueueCS);
  for (auto it : m_DataChangeQueue)
  {
    records.push_back(it);
  }
  m_DataChangeQueue.clear();
}

GTWTASE2Client* GTWTase2Server::GetBidirClient()
{
  return IsBidirectional() ? GTWTASE2Client::getTASE2Client(getObIndex()) : nullptr;
}

int GTWTase2Server::GetNumServers()
{
  return (int)m_ServerList.size();
}

void GTWTase2Server::GetServers(std::list<GTWTase2Server*>& serverList)
{
  for (auto iter : m_ServerList)
  {
    serverList.push_back(iter);
  }
}

GTWCollectionList* GTWTase2Server::GetVCCMemberCollection()
{
  //if (!m_pVCCFolder)
  //{
   // m_pVCCFolder = new GTWTase2VCCFolder();
  //}
  return m_pVCCFolder->GetMemberCollection();
}

void GTWTase2Server::GetDescriptionColText(CStdString& itemStr)
{
  TMWTYPES_USHORT index = getObIndex();
  if (IsServerUp())
  {
    itemStr.Format("ICCP Server (%s,%d) : Running (%d client(s) connected)", (const char*)GTWConfig::TASE2ServerRFCIPAddr(index),
      GTWConfig::TASE2ServerListeningPort(index), GetClientConnectedCount());
  }
  else
  {
    itemStr.Format("ICCP Server (%s,%d) : NOT Running", (const char*)GTWConfig::TASE2ServerRFCIPAddr(index),
      GTWConfig::TASE2ServerListeningPort(index));
  }
}

//**************************************************************************
// Called by the server when data changes

class OnTase2DataChangeItem : public WorkItemBase
{
public:
  virtual void   DoWork(void* param);

  virtual void Abort()
  {
  }

  OnTase2DataChangeStruct *dc;
  void *pUserData;
};


void GTWTase2Server::OnDataRead(tmwTase2::Tase2Server *cc,  tmwTase2::Tase2DataAttribute *da)
{
  CStdString msg;
  String sName, sValue;
  da->GetFullName(sName);
  da->GetValueAsString(sValue);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "Remote ICCP client finished read %s, value returned is '%s'", (const char*)sName, (const char*)sValue);

  /*
  void *pUserData = da->GetUserData();
  if (pUserData != NULL)
  {
    GTWTase2SlaveDataObject *pSdo = (GTWTase2SlaveDataObject *)da->GetUserData();
    if (pSdo->m_Tase2Type == GTWTASE2_TYPE_REALSETPOINT)
    {
      pSdo->retrieve();
    }
  }
  */
}

void GTWTase2Server::OnAnyDataChange(tmwTase2::Tase2Base *cc, tmwTase2::Tase2PointChangeInfo *pci)
{
  /* 2/13/2018: DM: For now we do nothing in this method it is only needed if simulating data changes internally

  GTWTase2SlaveDataObject *pSdo = (GTWTase2SlaveDataObject *)pci->GetPointMetaData()->GetUserData();
  if (!pSdo && pci->GetPointMetaData()->GetParent())
  {
  pSdo = (GTWTase2SlaveDataObject *)pci->GetPointMetaData()->GetParent()->GetUserData();
  }

  if (pSdo)
  {
  pci->GetPointMetaData()->GetValueAsVariant(pSdo->GetVariant());
  }
  */

  /* old code - not sure if this has ever been used??
  void *pUserData = pci->GetPointMetaData()->GetUserData();
  if (pUserData != NULL)
  {
  // PostOnAnyDataChange(dynamic_cast<tmwTase2::Tase2Server*>(cc), pci->GetPointMetaData() /*, pci->m_reportReason/);
  }
  */
}


void GTWTase2Server::PostOnAnyDataChange(tmwTase2::Tase2Server *cc, tmwTase2::Tase2DataAttribute *da)
{
  if (da->isCommandType()) // we do not process a data change event for controls on the server (let OnOperateControl handle it)
  {
    return;
  }

  GTWTase2Server *pServer = (GTWTase2Server *)cc->GetUserData();

  if (pServer->GetWorkQueue()->IsActive())
  {
    OnTase2DataChangeStruct *pS = pServer->m_dispenser.GetObjectFromPool();

    if (pS == NULL)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Tase2, nullptr, "%s", "IECTase2: Out of pooled data change objects");
      return;
    }

    OnTase2DataChangeItem* pDC = new OnTase2DataChangeItem();
    pS->m_pDa = da;
    pS->m_pServer = cc;

    pDC->dc = pS;
    pServer->GetWorkQueue()->AddWorkItemToQueue(pDC);
  }

}

void GTWTase2Server::OnDataChangeFromClient(tmwTase2::Tase2Server *cc,  tmwTase2::Tase2PointChangeInfo *pci)
{
  void *pUserData = pci->GetPointMetaData()->GetUserData();
  if (pUserData != NULL)
  {
    PostOnAnyDataChange(cc, pci->GetPointMetaData() /*, pci->m_reportReason*/);
  }
}

void GTWTase2Server::PostOnDataChangeFromClient(tmwTase2::Tase2Server *cc, tmwTase2::Tase2DataAttribute *da)
{
  GTWTase2Server *pServer = (GTWTase2Server *)cc->GetUserData();

  if (pServer->GetWorkQueue()->IsActive())
  {
    OnTase2DataChangeStruct *pS = pServer->m_dispenser.GetObjectFromPool();
    if (pS == NULL)
    {
      LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%s", "IECTase2: Out of pooled data change objects");
      return;
    }

    pS->m_pDa = da;
    pS->m_pServer = cc;
    OnTase2DataChangeItem* pDC = new OnTase2DataChangeItem();
    pDC->dc = pS;
    pServer->GetWorkQueue()->AddWorkItemToQueue(pDC);
  }
}
//

//
void OnTase2DataChangeItem::DoWork(void *param)
{
  bool bKeepDAActive = false;
  OnTase2DataChangeStruct *pS = dc;
  tmwTase2::Tase2DataAttribute *da = pS->m_pDa;

  GTWTase2Server *pServer = (GTWTase2Server *)pS->m_pServer->GetUserData();
  pServer->GetDataChangeDispenser()->ReturnObjectToPool(pS);

  GTWTase2SlaveDataObject *pSdo = (GTWTase2SlaveDataObject *)da->GetUserData();

  if (pSdo)
  {
    pSdo->GTWTase2_write(da);
  }
}

/**********************************************************************************\
Function :			GTWTase2Server::GTWTase2Server
Description : [none]
Return :			constructor	-
Parameters :
const char *alias_name	-
TMWTYPES_UINT reconnect_time	-
TMWTYPES_USHORT index	-
Note : [none]
\**********************************************************************************/
GTWTase2Server::GTWTase2Server(TMWTYPES_USHORT index)
:
GTWCollectionListParent(TMWDEFS_FALSE),
m_workQueueThread(this, "IgnoredAnyway"),
m_pChannelActiveMdo(NULL),
m_pNumClientMdo(NULL),
m_pBlockAllControlsMdo(nullptr),
m_pDataChangeThread(nullptr),
m_bRunDCThread(true)
{
  m_obIndex = index;
  m_pVCCFolder = new GTWTase2VCCFolder(this);
  m_workQueueThread.Start();
  //m_ServerIpAddress = GTWConfig::TASE2ServerIPAddress(index);
  //m_aliasName = GTWConfig::TASE2ServerName(index);

  TMWTYPES_UINT serviceRole = GTWConfig::TASE2ServiceRole(index);
  m_bInPeerMode = (serviceRole == CLIENT_SERVER_ROLE);

  m_pTase2ServerConnection = new tmwTase2::Tase2Server(m_bInPeerMode);
  m_pTase2ServerConnection->SetUserData(this);
  RegisterCallbacks(false);

  //for enabling TP logging
#if 0
  {
    std::string sName = (const char*)this->GetMemberName();
    int idx = 0;
    if ((idx = sName.find(':')) != std::string::npos)
    {
      sName = sName.substr(idx + 1);
    }

    m_pTase2ServerConnection->EnableTPLogging(true, sName.c_str());
  }
#endif

#ifdef _DEBUG
  /*
  if (g_bTurnOnDataSimulation)
  {
    //init each thread context
    GtwTase2ThreadContext* pContext = new GtwTase2ThreadContext;
    pContext->m_pServer = this;

    //create thread
    DWORD threadId;
    HANDLE hThread = CreateThread(NULL,
      0,
      SimulateThreadFunc,
      pContext,
      0,
      &threadId);

    pContext->m_hThread = hThread;
  }
  */
#endif

  if (UseDataChangeCache())
  {
    m_pDataChangeThread = new std::thread(std::bind(GTWTase2Server::DataChangeThreadFunc, this));
  }
}

GTWTase2Server::~GTWTase2Server()
{
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "GTWTase2Server::~GTWTase2Server: alias name: %s", (const char *)GetAliasName());
  m_bRunDCThread = false;
  if (m_pDataChangeThread)
  {
    m_pDataChangeThread->join();
    delete m_pDataChangeThread;
  }

  m_workQueueThread.Stop(false, false);
  if (m_pTase2ServerConnection)
  {
    delete m_pTase2ServerConnection;
    m_pTase2ServerConnection = NULL;
  }

  GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

GTWBaseEditor *GTWTase2Server::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTWTase2ServerEditor(dto, this, false);
  else
    m_pEditor->SetDTO(dto); 
  
  return m_pEditor;
}


void GTWTase2Server::RegisterCallbacks(bool bRemove)
{
  m_pTase2ServerConnection->RegisterServerReadPointFunc(bRemove ? NULL : OnDataRead);
  m_pTase2ServerConnection->RegisterClientChangedServerPointFunc(bRemove ? NULL : OnDataChangeFromClient);
  m_pTase2ServerConnection->RegisterServerOperationalEventFunc(bRemove ? NULL : OnOperationalMsg);
  m_pTase2ServerConnection->RegisterOnClientConnectedFunc(bRemove ? NULL : OnClientConnected);
  m_pTase2ServerConnection->RegisterOnClientConnectingFunc(bRemove ? NULL : OnClientConnecting);
  m_pTase2ServerConnection->RegisterOnClientDisconnectedFunc(bRemove ? NULL : OnClientDisconnected);
  m_pTase2ServerConnection->RegisterControlCallBacks(bRemove ? NULL : OnSelectControl, bRemove ? NULL : OnOperateControl, bRemove ? NULL : OnDeSelectControl);

  // 2/13/2018: DM: For now we do not register this method it is only needed if simulating data changes internally
  //m_pTase2ServerConnection->RegisterPointChangeFunc(OnAnyDataChange);
}

unsigned long __stdcall GTWTase2Server::DataChangeThreadFunc(void* pParam)
{
  GTWTase2Server* pT2Server = (GTWTase2Server*)pParam;
  assert(pT2Server);
  while (pT2Server->m_bRunDCThread && pT2Server->UseDataChangeCache())
  {
    try
    {
      if (pT2Server->IsServerUp())
      {
        std::list<ICCPDataChangeRecord> records;
        pT2Server->GetDataChangeRecords(records);

        //TRACE("Got %d datachangerecords\n", records.size());
        if (records.size() > 0)
        {
          if (pT2Server->GetServerConnection()->StartTransaction())
          {
            for (auto it : records)
            {
              ICCPDataChangeRecord dcr = it;
              if (dcr.m_bIsTimeDA)
              {
                dcr.m_pSDO->SetTase2Time((tmwTase2::Tase2TimeStamp*)dcr.m_pDA);
              }
              else if (dcr.m_bIsTimeExtDA)
              {
                dcr.m_pSDO->SetTase2Time((tmwTase2::Tase2TimeStampExtended*)dcr.m_pDA);
              }
              else
              {
                dcr.m_pSDO->SetValue(dcr.m_pDA, dcr.m_stdQuality);
              }
            }
            pT2Server->GetServerConnection()->EndTransaction();
          }
          else
          {
            show_Tase2_error("Failed to start transaction in GTWTase2Server::DataChangeThreadFunc");
          }
        }
      }

      tmw::Thread::SleepMS(250);
    }
    catch (const std::exception& ex)
    {
      assert(false);
      show_Tase2_error("Exception occurred in GTWTase2Server::DataChangeThreadFunc : %s", ex.what());
    }
    catch (...)
    {
      assert(false);
      show_Tase2_error("Unknown Exception occurred in GTWTase2Server::DataChangeThreadFunc");
    }
  }
  return 0;
}

void GTWTase2Server::OnDeSelectControl(tmwTase2::Tase2Server *server, tmwTase2::Tase2SBO *pSbo)
{
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer && pServer->m_pBlockAllControlsMdo && pServer->m_pBlockAllControlsMdo->GetValue())
  {
    show_Tase2_error("%s : Cannot deselect ICCP server control when BlockAllControls MDO is true.", pServer->GetFullName().c_str());
  }
}

bool GTWTase2Server::OnSelectControl(tmwTase2::Tase2Server *server, tmwTase2::Tase2SBO *pSbo)
{
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer && pServer->m_pBlockAllControlsMdo && pServer->m_pBlockAllControlsMdo->GetValue())
  {
    show_Tase2_error("%s : Cannot select ICCP server control when BlockAllControls MDO is true.", pServer->GetFullName().c_str());
    return false;
  }

  return true;
}

GTWTase2SlaveDataObject* GTWTase2Server::GetSdo(tmwTase2::Tase2DataAttribute *pDa)
{
  GTWCollectionList *pCollection = this->GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  GTWTase2SlaveDataObject *pSdo = nullptr;
  for (auto sdoPos = pCollection->GetMap()->begin(); sdoPos != pCollection->GetMap()->end(); ++sdoPos)
  {
    GTWCollectionMember *pMember = sdoPos->second;
    if (!pMember)
    {
      continue;
    }
    
    GTWTase2ServerLogicalDevice *pLD = dynamic_cast<GTWTase2ServerLogicalDevice*>(pMember);
    if (pLD)
    {
      pSdo = pLD->GetSdo(pDa);
      if (pSdo)
      {
        return pSdo;
      }
      continue;
    }

    GTWTase2VCCFolder* pVCC = dynamic_cast<GTWTase2VCCFolder*>(pMember);
    if (pVCC)
    {
      pSdo = pVCC->GetSdo(pDa);
      if (pSdo)
      {
        return pSdo;
      }
      continue;
    }

    pSdo = dynamic_cast<GTWTase2SlaveDataObject*>(pMember);
    if (pSdo)
    {
      if (pSdo->GetTase2DA() == pDa)
      {
        return pSdo;
      }
    }
  }

  return nullptr;
}

class ICCPServerOperateWorkItem : public WorkItemBase
{
public:
  virtual void DoWork(void* param)
  {
    std::unique_ptr<ICCPServerOperateWorkItem> deleteThis(this);
    bool bResult = m_pSdo->GTWTase2_write(m_pControl);
  }

  ~ICCPServerOperateWorkItem()
  {
    delete m_pControl;
  }

  virtual void Abort() {}

  tmwTase2::Tase2DataAttribute *m_pControl;
  GTWTase2SlaveDataObject *m_pSdo;
};


bool GTWTase2Server::OnOperateControl(tmwTase2::Tase2Server *server, tmwTase2::Tase2Command *pOriginalCommand, tmwTase2::Tase2Command *pNewCommand, tmwTase2::ServerMessage::ServerMMSErrorCode &mmsErrorCode)
{
  //tmw::String s1, s2;
  //TRACE("GTW61850Server::OnOperateControl called for : %s\n", da->GetFullName(s1));
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer && pServer->m_pBlockAllControlsMdo && pServer->m_pBlockAllControlsMdo->GetValue())
  {
    mmsErrorCode = tmwTase2::ServerMessage::MMSErrorCode_IO_Object_Access_Denied;
    show_Tase2_error("%s : Cannot operate ICCP server control when BlockAllControls MDO is true.", pServer->GetFullName().c_str());
    return false;
  }

  if (!pNewCommand)
  {
    show_Tase2_error("Operating invalid control - command is null. Missing control value. Make sure model file is correct");
    return false;
  }

  GTWTase2SlaveDataObject *pSdo = GTWTase2Server::GetServer(server)->GetSdo(pOriginalCommand);
  if (pSdo)
  {
    //return pSdo->GTWTase2_write(pNewCommand);

    ICCPServerOperateWorkItem *pWI = new ICCPServerOperateWorkItem;
    pWI->m_pControl = dynamic_cast<tmwTase2::Tase2DataAttribute*>(pNewCommand->Copy());
    pWI->m_pSdo = pSdo;

    GTWTase2Server::GetServer(server)->GetWorkQueue()->AddWorkItemToQueue(pWI);
    return true;
  }
  else
  {
    mmsErrorCode = tmwTase2::ServerMessage::MMSErrorCode_IO_Object_Access_Denied;
    show_Tase2_error("Operating invalid control - Could not find  Make sure model file is correct");
    return false;
  }
}

void GTWTase2Server::InsertMDOs()
{
  if (NULL != m_pChannelActiveMdo)
    return;
  
  GetMemberCollection()->InsertCollectionMember(m_pChannelActiveMdo = new t2GTWServerChannelActive(this));
  GetMemberCollection()->InsertCollectionMember(m_pNumClientMdo = new t2GTWNumClients);
  GetMemberCollection()->InsertCollectionMember(m_pBlockAllControlsMdo = new t2GTWBlockAllControlsMDO);
  GetMemberCollection()->InsertCollectionMember(m_pVCCFolder);// = new GTWTase2VCCFolder(this));
}

class OnTase2OperationalMsgStruct : public WorkItemBase
{
public:
  virtual void   DoWork(void* param);

  virtual void Abort()
  {
  }

  tmwTase2::ServerMessage::ServerOperationalEventType m_level;
  CStdString m_msg;
};

void GTWTase2Server::OnOperationalMsg(tmwTase2::ConnectionBase *firedFrom, tmwTase2::Node *node, tmwTase2::ServerMessage::ServerOperationalEventType level, const char *msg)
{
  PostOnOperationalMsg(level, msg);
}

void GTWTase2Server::PostOnOperationalMsg(tmwTase2::ServerMessage::ServerOperationalEventType level, const char *msg)
{
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP Client operation performed: %s", msg);
  // Do not add to work queue because DoWork (below) does nothing
}

void OnTase2OperationalMsgStruct::DoWork(void *param)
{
  //unique_ptr<OnTase2OperationalMsgStruct> deleteOnReturn(this);
  //free(msg);
}

bool GTWTase2Server::IsServerUp()
{
  return m_pTase2ServerConnection->IsConnectionAliveAndReady();
}

bool GTWTase2Server::Save(CStdString fileName)
{
  m_pTase2ServerConnection->SaveToXML(fileName, false);
  fileName.Replace("xml", "csv");
  m_pTase2ServerConnection->SaveToCSV(fileName);
  
  fileName.Replace(".csv", "_withDS.xml");
  m_pTase2ServerConnection->SaveToXML(fileName, true);

  return true;
}

bool GTWTase2Server::ReStart()
{
  if (GetGTWApp()->IsShuttingDown())
  {
    return false;
  }
  if (!IsServerUp())
  {
    Start();
  }
  else if (Disconnect())
  {
    Start();
  }
  return true;
}

/**********************************************************************************\
Function :			GTWTase2Server::Connect
Description : [none]
Return :			void	-
Parameters :
Note : [none]
\**********************************************************************************/
bool GTWTase2Server::Start(bool bSetChannelActiveMdo)
{
  // start up a Tase2 server
  if (m_pTase2ServerConnection != NULL && m_pTase2ServerConnection->IsConnectionAliveAndReady() == false)
  {
    bool isBidir = IsPeerMode();

    if (isBidir)
    {
      GTWTASE2Client *pClient = GTWTASE2Client::getTASE2Client(getObIndex());
      m_iCurProfileIndex = pClient ? pClient->GetCurProfileIndex() : 0;
    }

    TMWTYPES_USHORT index = this->getObIndex();

    m_pTase2ServerConnection->SetIsMultiPlex(GTWConfig::TASE2ServerEnableIECFullStackAddressing(index));

    Initialize7Layer(isBidir);
    InitializeSecurity(isBidir);
    m_pTase2ServerConnection->SetMaxConnectionsAllowed(GTWConfig::TASE2ServerMaxConnectionsAllowed[index]);
    UpdateModelVersion();

    TMWTYPES_UINT keepAliveStartTime = GTWConfig::TASE2KeepAliveStartTime(index); // we assume only one of these, so dont need to call GetProfileAttr
    TMWTYPES_UINT keepAliveInterval = GTWConfig::TASE2KeepAliveInterval(index); // we assume only one of these, so dont need to call GetProfileAttr
    m_pTase2ServerConnection->SetKeepAlive(keepAliveStartTime, keepAliveInterval);

    if (m_pTase2ServerConnection->StartServer() == false)
    {
      if (bSetChannelActiveMdo)
      {
        if (m_pChannelActiveMdo)
        {
          m_pChannelActiveMdo->SetValue(TMWDEFS_FALSE);
        }
      }
      return TMWDEFS_FALSE;
    }
    if (bSetChannelActiveMdo)
    {
      if (m_pChannelActiveMdo)
      {
	      m_pChannelActiveMdo->SetValue(TMWDEFS_TRUE);
      }
    }
    UpdateAllSDOs();
  }
  return TMWDEFS_TRUE;
}

void GTWTase2Server::Tokenize(const CStdString &s, std::vector<CStdString> &sArray)
{
  int i = 0;
  for(CStdString sItem = s.Tokenize(";",i); i >= 0; sItem = s.Tokenize(",",i))
  {
    sArray.push_back( sItem.Trim() );
  }
}

CStdString GTWTase2Server::GetProfileAttr(const CStdString &sProfileAttr)
{
  // We dont support tokenizing on the server, but we still do this for backwards compatibility in case someone has semicolon separated fields in previous releases
  // m_iCurProfileIndex is always zero

  std::vector<CStdString> saItems;
  Tokenize(sProfileAttr, saItems);
  unsigned int index = (m_iCurProfileIndex < saItems.size()) ? m_iCurProfileIndex : 0;
  return (index < saItems.size()) ? saItems[index] : CStdString("");
}

void GTWTase2Server::UpdateModelVersion()
{
  CStdString t2Version = GTWConfig::TASE2Version(m_obIndex);
  tmwTase2::Tase2Server* pT2S = GetServerConnection();
  if (pT2S == nullptr)
  {
    return;
  }

  tmwTase2::Tase2Version* pTase2Version = dynamic_cast<tmwTase2::Tase2Version*>(tmwTase2::Tase2RootNode::FindNode(tmwTase2::Tase2Base::TASE2_VERSION, pT2S->Model()));
  if (!pTase2Version)
  {
    pTase2Version = new tmwTase2::Tase2Version();
    pT2S->Model()->AddChild(pTase2Version);
  }

  pTase2Version->SetMajorVersion(t2Version == "2000.8" ? 2000 : 1996);
  pTase2Version->SetMinorVersion(8);
}

const char* getStackAddressString(const unsigned char* sval)
{
  return (const char*)sval;
}

#if 0
void GTWTase2Server::dump7LayerStack()
{
  CStdString s = this->GetFullName();

  const char* sn = s.length() > 0 ? s.c_str() : "empty name";

  tmwTase2::ServerStackAddress* pStackAddr = m_pTase2ServerConnection->GetServerStackAddress();
  unsigned int uInclusion = pStackAddr->GetInclusion();
  TRACE("%s : inclusion = %d\n", sn, uInclusion);

  TRACE("\tAE Invoke ID = %d\n", pStackAddr->GetServerAEInvokeID());
  TRACE("\tAE Qualifier = %d\n", pStackAddr->GetServerAEQual());
  TRACE("\tAP Invoke ID = %d\n", pStackAddr->GetServerAPInvokeID());

  TRACE("\tAP Title = %s\n", getStackAddressString(pStackAddr->GetServerAPTitle()));
  TRACE("\tPSEL = %s\n", getStackAddressString(pStackAddr->GetServerPSel()));
  TRACE("\tSSEL = %s\n", getStackAddressString(pStackAddr->GetServerSSel()));
  TRACE("\tTSEL = %s\n", getStackAddressString(pStackAddr->GetServerTSel()));
}
#endif

void GTWTase2Server::Initialize7Layer(bool isBidir)
{
  int index = this->getObIndex();

  // We are removing this param in 5.2 patch and 5.2.1 because it should be taken care of in the migration 
  // Now we always assume new way of doing this
  if (GTWConfig::TASE2UseNew7LayerConfiguration) // if new, we use the client fields to fill in the MMSd_Stack_Addresses data structure
  {
    CStdString client_ae_qual = GetProfileAttr(GTWConfig::TASE2ClientAEQualifier(index));
    CStdString client_ap_invoke_id = GetProfileAttr(GTWConfig::TASE2ClientAPInvokeID(index));
    CStdString client_ae_invoke_id = GetProfileAttr(GTWConfig::TASE2ClientAEInvokeID(index));
    CStdString client_app_id = GetProfileAttr(GTWConfig::TASE2ClientAppID(index));
    CStdString client_pres_addr = GetProfileAttr(GTWConfig::TASE2ClientPresentationAddress(index));
    CStdString client_session_addr = GetProfileAttr(GTWConfig::TASE2ClientSessionAddress(index));
    CStdString client_transport_addr = GetProfileAttr(GTWConfig::TASE2ClientTransportAddress(index));

    CStdString server_rfc_addr = GetProfileAttr(GTWConfig::TASE2ServerRFCIPAddr(index));
    TMWTYPES_UINT server_listening_port = GTWConfig::TASE2ServerListeningPort(index);

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' 7 layer stack settings: APTitle=%s, AEQual=%s, AEInvokeId=%s, APInvokeId=%s, PSEL=%s, SSEL=%s, TSEL=%s",
      GetFullName().c_str(), client_app_id.c_str(), client_ae_qual.c_str(), client_ae_invoke_id.c_str(), client_ap_invoke_id.c_str(),
      client_pres_addr.c_str(), client_session_addr.c_str(), client_transport_addr.c_str());

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' listening on : (IP=%s, Port=%d)", GetFullName().c_str(), server_rfc_addr.c_str(), server_listening_port);

    bool bInclusion = client_ae_invoke_id.GetLength() > 0;
    TMWTYPES_ULONG uVal = bInclusion ? atoi(client_ae_invoke_id) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAEInvokeID(uVal, bInclusion);

    bInclusion = client_ae_qual.GetLength() > 0;
    uVal = bInclusion ? atoi(client_ae_qual) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAEQual(uVal, bInclusion);

    bInclusion = client_ap_invoke_id.GetLength() > 0;
    uVal = bInclusion ? atoi(client_ap_invoke_id) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAPInvokeID(uVal, bInclusion);

    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAPTitle(client_app_id.GetLength() > 0 ? (const unsigned char*)(const char*)client_app_id : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerPSel(client_pres_addr.GetLength() > 0 ? (const unsigned char*)(const char*)client_pres_addr : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerSSel(client_session_addr.GetLength() ? (const unsigned char*)(const char*)client_session_addr : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerTSel(client_transport_addr.GetLength() > 0 ? (const unsigned char*)(const char*)client_transport_addr : NULL);

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' 7 layer stack settings: APTitle=%s, AEQual=%s, AEInvokeId=%s, APInvokeId=%s, PSEL=%s, SSEL=%s, TSEL=%s",
        GetFullName().c_str(), client_app_id.c_str(), client_ae_qual.c_str(), client_ae_invoke_id.c_str(), client_ap_invoke_id.c_str(),
        client_pres_addr.c_str(), client_session_addr.c_str(), client_transport_addr.c_str());
  }
  else
  {
    CStdString server_app_id = GetProfileAttr(GTWConfig::TASE2ServerAppID(index));
    CStdString server_ae_qual = GetProfileAttr(GTWConfig::TASE2ServerAEQualifier(index));
    CStdString server_ae_invoke_id = GetProfileAttr(GTWConfig::TASE2ServerAEInvokeID(index));
    CStdString server_ap_invoke_id = GetProfileAttr(GTWConfig::TASE2ServerAPInvokeID(index));
    CStdString server_pres_addr = GetProfileAttr(GTWConfig::TASE2ServerPresentationAddress(index));
    CStdString server_session_addr = GetProfileAttr(GTWConfig::TASE2ServerSessionAddress(index));
    CStdString server_transport_addr = GetProfileAttr(GTWConfig::TASE2ServerTransportAddress(index));
    CStdString server_rfc_addr = GetProfileAttr(GTWConfig::TASE2ServerRFCIPAddr(index));
    TMWTYPES_UINT server_listening_port = GTWConfig::TASE2ServerListeningPort(index);

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' 7 layer stack settings: APTitle=%s, AEQual=%s, AEInvokeId=%s, APInvokeId=%s, PSEL=%s, SSEL=%s, TSEL=%s",
      GetFullName().c_str(), server_app_id.c_str(), server_ae_qual.c_str(), server_ae_invoke_id.c_str(), server_ap_invoke_id.c_str(),
      server_pres_addr.c_str(), server_session_addr.c_str(), server_transport_addr.c_str());

    bool bInclusion = server_ae_invoke_id.GetLength() > 0;
    TMWTYPES_ULONG uVal = bInclusion ? atoi(server_ae_invoke_id) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAEInvokeID(uVal, bInclusion);

    bInclusion = server_ae_qual.GetLength() > 0;
    uVal = bInclusion ? atoi(server_ae_qual) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAEQual(uVal, bInclusion);

    bInclusion = server_ap_invoke_id.GetLength() > 0;
    uVal = bInclusion ? atoi(server_ap_invoke_id) : 0;
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAPInvokeID(uVal, bInclusion);

    m_pTase2ServerConnection->GetServerStackAddress()->SetServerAPTitle(server_app_id.GetLength() > 0 ? (const unsigned char*)(const char*)server_app_id : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerPSel(server_pres_addr.GetLength() > 0 ? (const unsigned char*)(const char*)server_pres_addr : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerSSel(server_session_addr.GetLength() ? (const unsigned char*)(const char*)server_session_addr : NULL);
    m_pTase2ServerConnection->GetServerStackAddress()->SetServerTSel(server_transport_addr.GetLength() > 0 ? (const unsigned char*)(const char*)server_transport_addr : NULL);

    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' 7 layer stack settings: APTitle=%s, AEQual=%s, AEInvokeId=%s, APInvokeId=%s, PSEL=%s, SSEL=%s, TSEL=%s",
      GetFullName().c_str(), server_app_id.c_str(), server_ae_qual.c_str(), server_ae_invoke_id.c_str(), server_ap_invoke_id.c_str(),
      server_pres_addr.c_str(), server_session_addr.c_str(), server_transport_addr.c_str());
  }

  CStdString server_rfc_addr = GetProfileAttr(GTWConfig::TASE2ServerRFCIPAddr(index));
  TMWTYPES_UINT server_listening_port = GTWConfig::TASE2ServerListeningPort(index);

  m_pTase2ServerConnection->GetRFCAddress()->SetLocalIPAddress(server_rfc_addr);
  m_pTase2ServerConnection->GetRFCAddress()->SetListenPort(server_listening_port);
  m_ServerIpPort = server_listening_port;

  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_Tase2, nullptr, "ICCP server '%s' listening on : (IP=%s, Port=%d)", GetFullName().c_str(), server_rfc_addr.c_str(), server_listening_port);
  
  TMWTYPES_UINT iSupportedFeatures = GTWConfig::TASE2ServerSupportedFeatures(index);
  tmwTase2::Tase2Features *pSupportedFeatures = dynamic_cast<tmwTase2::Tase2Features*>(tmwTase2::Tase2RootNode::FindNode(tmwTase2::Tase2Base::TASE2_SUPPORTED_FEATURES, m_pTase2ServerConnection->Model()));
  if (!pSupportedFeatures)
  {
    pSupportedFeatures = new tmwTase2::Tase2Features();
    m_pTase2ServerConnection->Model()->AddChild(pSupportedFeatures);
  }
  assert(pSupportedFeatures);
  if (pSupportedFeatures)
  {
    pSupportedFeatures->Reset(); // Reset internal bits to all zero
    
    int value = 0;
    for (int block = 1; block <= 12; block++)
    {
      unsigned int bit_i = (unsigned int)(pow((double)2, (double)(16-block)));
      if (bit_i & iSupportedFeatures)
      {
        switch (block)
        {
        case 1: pSupportedFeatures->SetBlock1(true); break;
        case 2: pSupportedFeatures->SetBlock2(true); break;
        case 3: pSupportedFeatures->SetBlock3(true); break;
        case 4: pSupportedFeatures->SetBlock4(true); break;
        case 5: pSupportedFeatures->SetBlock5(true); break;
        case 6: pSupportedFeatures->SetBlock6(true); break;
        case 7: pSupportedFeatures->SetBlock7(true); break;
        case 8: pSupportedFeatures->SetBlock8(true); break;
        case 9: pSupportedFeatures->SetBlock9(true); break;
        case 10: pSupportedFeatures->SetBlock10(true); break;
        case 11: pSupportedFeatures->SetBlock11(true); break;
        case 12: pSupportedFeatures->SetBlock12(true); break;
        }
      }
    }
#ifdef DEBUG_TASE2_SERVER
    for (int block = 1; block <= 12; block++)
    {
        switch (block)
        {
        case 1:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block1 supported=%d\n", pSupportedFeatures->GetBlock1()); break;
        case 2:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block2 supported=%d\n", pSupportedFeatures->GetBlock2()); break;
        case 3:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block3 supported=%d\n", pSupportedFeatures->GetBlock3()); break;
        case 4:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block4 supported=%d\n", pSupportedFeatures->GetBlock4()); break;
        case 5:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block5 supported=%d\n", pSupportedFeatures->GetBlock5()); break;
        case 6:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block6 supported=%d\n", pSupportedFeatures->GetBlock6()); break;
        case 7:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block7 supported=%d\n", pSupportedFeatures->GetBlock7()); break;
        case 8:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block8 supported=%d\n", pSupportedFeatures->GetBlock8()); break;
        case 9:  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block9 supported=%d\n", pSupportedFeatures->GetBlock9()); break;
        case 10: LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block10 supported=%d\n", pSupportedFeatures->GetBlock10()); break;
        case 11: LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block11 supported=%d\n", pSupportedFeatures->GetBlock11()); break;
        case 12: LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr,"block12 supported=%d\n", pSupportedFeatures->GetBlock12()); break;
        }
    }
#endif
  }

#ifdef DEBUG_TASE2_SERVER
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_ae_qual=%s\n",m_iCurProfileIndex,server_ae_qual);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_ap_invoke_id=%s\n",m_iCurProfileIndex,server_ap_invoke_id);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_ae_invoke_id=%s\n",m_iCurProfileIndex,server_ae_invoke_id);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_app_id=%s\n",m_iCurProfileIndex,server_app_id);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_pres_addr=%s\n",m_iCurProfileIndex,server_pres_addr);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_session_addr=%s\n",m_iCurProfileIndex,server_session_addr);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "%d:server_transport_addr=%s\n",m_iCurProfileIndex,server_transport_addr);
#endif
}

void GTWTase2Server::InitializeSecurity(bool isBidir)
{
  int index = m_obIndex;
  tmwTase2::ServerStackAddress *pServerAddr = m_pTase2ServerConnection->GetServerStackAddress();
  if (!GTWConfig::TASE2SecurityOn(index))
  {
    pServerAddr->SetAuthMechanism(tmwTase2::EnumDefs::Authentication_None);
  }
  else
  {
#if defined(TMW_USE_MMS_AUTH) && defined(TMW_USE_TLS)
    pServerAddr->SetAuthMechanism(tmwTase2::EnumDefs::Authentication_Strong); // Certificate
    tmwTase2::StrongSecurityConfig *secConfig = pServerAddr->GetStrongSecurityConfig();

    // Central Authority
    secConfig->SetCaVerifyDepth(atoi(GetProfileAttr(GTWConfig::TASE2CertAuthChainingVerDepth(index))));
    secConfig->SetCAFileName(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2CertificateAuthorityFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str()));
    secConfig->SetCARevokeListFileName(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2CertificateAuthorityRevokeListFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str()));
    
    //secConfig->SetCAPathName(GTWConfig::TASE2DirectoryToCertificateAuthority(index));
    CStdString sdir = GTWConfig::TASE2DirectoryToCertificateAuthority(index);
    if (sdir.length() > 0)
    {
      CStdString fullPath = GtwSysConfig::getCurrentWorkSpacePath();
      secConfig->SetCAPathName(fullPath + "/" + sdir);
    }

    // MMS
    secConfig->SetMmsCommonName(GetProfileAttr(GTWConfig::TASE2MMSCommonName(index)));
    secConfig->SetMmsPrivateKeyFile(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2MMSPrivateKeyFile(index), GtwSysConfig::getPrivateKeyPath()).c_str()));
    secConfig->SetMmsPrivateKeyPassPhrase(GetProfileAttr(GTWConfig::GetTASE2MMSPrivateKeyPassPhrase(index)));
    secConfig->SetMmsPublicCertificate(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2MMSPublicCertificateFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str()));

    // TLS
    secConfig->SetTlsCommonName(GetProfileAttr(GTWConfig::TASE2TLSCommonName(index)));
    secConfig->SetTlsRenegotiationCount(atoi(GetProfileAttr(GTWConfig::TASE2TLSMaxPDUs(index))));
    secConfig->SetTlsRenegotiationMsTimeout(atoi(GetProfileAttr(GTWConfig::TASE2TLSMaxRenegotiationWaitTime(index))));
    secConfig->SetTlsRenegotiationSeconds(atoi(GetProfileAttr(GTWConfig::TASE2TLSRenegotiation(index))));

    // TLS DSA - not set by default

    // TLS RSA
    secConfig->SetTlsRsaPrivateKeyFile(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2TLSRSAPrivateKeyFile(index), GtwSysConfig::getPrivateKeyPath()).c_str()));
    secConfig->SetTlsRsaPrivateKeyPassPhrase(GetProfileAttr(GTWConfig::GetTASE2TLSRSAPrivateKeyPassPhrase(index)));
    secConfig->SetTlsRsaPublicCertificate(GetProfileAttr(GTWmain::GetIniRelativeFullFilePath(GTWConfig::TASE2TLSRSAPublicCertFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str()));


    bool bUseMMSOnly = GTWConfig::TASE2UseMMSOnly(index);
    bool bUseTLSOnly = GTWConfig::TASE2UseTLSOnly(index);

    if (bUseMMSOnly && bUseTLSOnly)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Tase2, nullptr,
        "Invalid security configuration. The ini parameters TASE2UseMMSOnly and TASE2UseTLSOnly cannot both be true (at index=%d). "
        "SDG will default both as false. Please edit and fix the ini file as needed. ", index);

      bUseMMSOnly = false;
      bUseTLSOnly = false;
    }

    if (bUseMMSOnly)
    {
      secConfig->SetDisableMMSSecurity(false);
      secConfig->SetDisableTLSSecurity(true);
    }
    else if (bUseTLSOnly)
    {
      secConfig->SetDisableMMSSecurity(true);
      secConfig->SetDisableTLSSecurity(false);
    }
    else
    {
      secConfig->SetDisableMMSSecurity(false);
      secConfig->SetDisableTLSSecurity(false);
    }

    secConfig->SetUseSiscoCompatibility(GTWConfig::TASE2UseSiscoCompatability(index));
#endif

#ifdef DEBUG_TASE2_SERVER
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "GTWTASE2Server::m_iCurProfileIndex=%d\n", m_iCurProfileIndex);
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2CertAuthChainingVerDepth=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2CertAuthChainingVerDepth(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2CertificateAuthorityFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2CertificateAuthorityFile(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2CertificateAuthorityRevokeListFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2CertificateAuthorityRevokeListFile(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2MMSCommonName=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2MMSCommonName(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2MMSPrivateKeyFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2MMSPrivateKeyFile(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2MMSPrivateKeyPassPhrase=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2MMSPrivateKeyPassPhrase(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2MMSPublicCertificateFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2MMSPublicCertificateFile(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSCommonName=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSCommonName(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSMaxPDUs=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSMaxPDUs(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSMaxRenegotiationWaitTime=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSMaxRenegotiationWaitTime(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSRenegotiation=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSRenegotiation(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSRSAPrivateKeyFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSRSAPrivateKeyFile(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSRSAPrivateKeyPassPhrase=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSRSAPrivateKeyPassPhrase(index), isBidir));
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_Tase2, nullptr, "server %d:TASE2TLSRSAPublicCertFile=%s\n", m_iCurProfileIndex,GetProfileAttr(GTWConfig::TASE2TLSRSAPublicCertFile(index), isBidir));
#endif
  }
}

/*
bool GTWTase2Server::OnClientConnecting(tmwTase2::Tase2Server *server, tmwTase2::ClientConnectInfo *clientConnectionInfo)
{
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer->IsPeerMode() && server->GetConnectedClientCount() > 1)
  {
    if (GTWConfig::TASE2RejectSecondaryConnections(pServer->getObIndex()))
    {
      LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_Tase2, nullptr, "Warning: Tase2 Server ('%s') rejecting secondary connection from %s because the ini parameter TASE2RejectSecondaryConnections is set to true", pServer->GetAliasName().c_str(), (const char*)clientConnectionInfo->GetClientIPAddress());
      return false;
    }
  }
  pServer->m_pNumClientMdo->Increment();
  return true;
}
*/

bool GTWTase2Server::OnClientConnecting(tmwTase2::Tase2Server* server, tmwTase2::ClientConnectInfo* clientConnectionInfo)
{
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer->IsPeerMode())
  {
    GTWTASE2Client* pClient = GTWTASE2Client::getTASE2Client(pServer->getObIndex());
    bool bConnectingOrConnected = pClient->GetClientConnection()->IsConnectionAliveAndReady() || pClient->GetClientConnection()->IsConnecting() || server->GetConnectedClientCount() > 1;
    if (bConnectingOrConnected)
    {
      if (GTWConfig::TASE2RejectSecondaryConnections(pServer->getObIndex()))
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Tase2, nullptr, "The ini parameter TASE2RejectSecondaryConnections is obsolete and no longer used. Please use TASE2ServerMaxConnectionsAllowed instead.", pServer->GetAliasName(), clientConnectionInfo->GetClientIPAddress());
      }
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_Tase2, nullptr, "Remote client tried to connect to ICCP server '%s' (from IP:%s) when it is already connected or trying to connect. "
        "Multiple client connections are not currently supported for bidirectional ICCP devices.", pServer->GetAliasName().c_str(), clientConnectionInfo->GetClientIPAddress());
      return false;
    }
  }
  pServer->m_pNumClientMdo->Increment();
  return true;
}

void GTWTase2Server::OnClientConnected(tmwTase2::Tase2Server *server, tmwTase2::Tase2Client *client)
{
  GTWTase2Server *pServer = GTWTase2Server::getTase2Server(server);
  if (pServer->IsPeerMode())
  {
    //pServer->dump7LayerStack();
    GTWTASE2Client *pClient = GTWTASE2Client::getTASE2Client(pServer->getObIndex());
    if (pClient)
    {
      pClient->PeerClientConnected(client);
    }
  }

#ifdef TEST_CONNECT_DISCONNECT
  Disconnect();
#endif
}

void GTWTase2Server::OnClientDisconnected(tmwTase2::Tase2Server *server, tmwTase2::ClientConnectInfo *clientConnectionInfo)
{
  GTWTase2Server* pServer = GTWTase2Server::GetServer(server);
  if (pServer)
  {
    pServer->m_pNumClientMdo->SetValue(pServer->GetServerConnection()->GetConnectedClientCount());
    //if (pServer->m_pNumClientMdo->GetValue() > 0)
    //{
    //  pServer->m_pNumClientMdo->SetValue(nCount);
    //}

    if (GTWConfig::TASE2ServerRestartOnDisconnect(pServer->m_obIndex))
    {
      if (pServer->m_pNumClientMdo->GetValue() == 0)
      {
        show_Tase2_msg("Client disconnected. ServerRestartOnDisconnect is true. Will restart server.");
        pServer->ReStart();
      }
      else
      {
        show_Tase2_msg("Client disconnected. ServerRestartOnDisconnect is true. However, there are still clients connected, so server will not restart until all clients are disconnected.");
      }
    }
    /*
    if (pServer->IsPeerMode()) // in bidirectional mode, the server handles the disconnect from the client
    {
      GTWTASE2Client* pClient = GTWTASE2Client::getTASE2Client(pServer->getObIndex());
      assert(pClient);
      if (pClient)
      {
        pClient->DoServerDown();
      }
    }
    */
  }
}

int GTWTase2Server::GetClientConnectedCount()
{
  return m_pNumClientMdo->GetValue();
}

bool GTWTase2Server::Disconnect()
{
  if (m_pTase2ServerConnection)
  {
    // If in peer mode we must disconnect the client before disconnecting the server because
    // they share a stack context, which the server disconnect deletes and the client can thus no longer
    // access
    //if (m_bInPeerMode)
    //{
	  //  GTWTASE2Client *pClient = GTWTASE2Client::getTASE2Client(getObIndex());
	  //  if (pClient)
    //    pClient->Disconnect();
    //}
    // Should only be called from this method
    m_pNumClientMdo->SetValue(0);
    return m_pTase2ServerConnection->Disconnect();
  }
  return false;
}

/**********************************************************************************\
Function :			GTWTase2Server::Disconnect
Description : [none]
Return :			void	-
Parameters :
bool bCleanItemList	-
Note : [none]
\**********************************************************************************/
bool GTWTase2Server::Stop()
{
  if (m_pTase2ServerConnection)
  {
    Disconnect();
    if (GetGTWApp()->IsShuttingDown() == false)
    {
      m_pChannelActiveMdo->SetValue(TMWDEFS_FALSE);
    }
  }
  return true;
}

GTWTase2Server* GTWTase2Server::GetServer(tmwTase2::Tase2Server *server)
{
  auto pos = m_ServerList.begin(); 
  while (pos != m_ServerList.end())
  {
    GTWTase2Server *pTase2Server = *pos;
    ++pos;
    if (pTase2Server->GetServerConnection() == server)
      return pTase2Server;
  }  
  return nullptr;
}

void GTWTase2Server::StopAllServersOnExit()
{ 
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* p61850Server = *serverListPOS;
    ++serverListPOS;
    p61850Server->RegisterCallbacks(true);
    p61850Server->Stop();
  }
}

void GTWTase2Server::RemoveAllServers()
{
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pServer = *serverListPOS;
    ++serverListPOS;

    RemoveServer(pServer, false);
  }
  m_ServerList.clear();
}

void GTWTase2Server::RemoveServer(GTWTase2Server *pServer, bool bRemoveFromCollection)
{
  if (!pServer)
  {
    return;
  }

  if (pServer->IsServerUp())
  {
    pServer->Stop();
  }

  if (bRemoveFromCollection)
  {
    auto pos = m_ServerList.begin();
    while (pos != m_ServerList.end())
    {
      GTWTase2Server* pT2Server = *pos;
      if (pT2Server == pServer)
      {
        m_ServerList.erase(pos);
        break;
      }
      ++pos;
    }
  }
}

void GTWTase2Server::RemoveTase2ServerNodes(GTWCollectionBase *pClctn)
{
  GTWCollectionMember *pTase2ServerNode = pClctn->GetOwnerMember();

  if (pTase2ServerNode->GetMemberCollection()->getNumMembers() == 0)
  {
    GTWCollectionBase *pColl = pTase2ServerNode->GetParentCollection();
    pColl->RemoveCollectionMember(pTase2ServerNode);
    RemoveTase2ServerNodes(pColl);
  }
}

/**********************************************************************************\
Function :			GTWTase2Server::getTase2connection
Description : [none]
Return :			GTWTase2Server *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTWTase2Server *GTWTase2Server::getTase2connection(
  const char *name,
  const char *ip_addr)
{
  // Look for a matching server
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pTase2Server = *serverListPOS;
    ++serverListPOS;

    if (ip_addr == NULL || ip_addr == pTase2Server->m_ServerIpAddress)
    {
      // Look for a matching server name
      if (name == pTase2Server->GetAliasName())
      {
        return pTase2Server;
      }
    }
  }
  return NULL;
}

GTWTase2Server *GTWTase2Server::getTase2Server(const char *name)
{
  // Look for a matching server
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pTase2Server = *serverListPOS;
    ++serverListPOS;

    // Look for a matching server name
    if (name == pTase2Server->GetAliasName())
    {
      return pTase2Server;
    }
  }
  return NULL;
}

GTWTase2Server *GTWTase2Server::getTase2Server(int index)
{
  // Look for a matching server
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pTase2Server = *serverListPOS;
    ++serverListPOS;

    if (pTase2Server->getObIndex() == index)
    {
      return pTase2Server;
    }
  }
  return NULL;
}

GTWTase2Server *GTWTase2Server::getTase2Server(tmwTase2::Tase2Server *pServer)
{
  // Look for a matching server
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pTase2Server = *serverListPOS;
    ++serverListPOS;

    if (pTase2Server->GetServerConnection() == pServer)
    {
      return pTase2Server;
    }
  }
  return NULL;
}

void GTWTase2Server::UpdateAllSDOs()
{
  std::list<GTWTase2SlaveDataObject*> sdoList;
  GetAllSDOs(sdoList);

  for (auto it = sdoList.begin(); it != sdoList.end(); ++it)
  {
    GTWTase2SlaveDataObject *pT2SDO = *it;
    pT2SDO->doUpdate();
  }
}

void GTWTase2Server::GetAllSDOs(std::list<GTWTase2SlaveDataObject*> &list)
{
  GTWCollectionList *pCollection = this->GetMemberCollection();
  if (!pCollection || pCollection->getNumMembers() == 0)
  {
    return;
  }
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  GetAllSDOs(pCollection, list); // Get SDOs in VCC

  // Now get from all domains
  for (auto sdoPos = pCollection->GetMap()->begin(); sdoPos != pCollection->GetMap()->end(); ++sdoPos)
  {
    GTWCollectionMember *pMember = sdoPos->second;
    if (GTWTase2ServerLogicalDevice *pLD = dynamic_cast<GTWTase2ServerLogicalDevice*>(pMember))
    {
      GetAllSDOs(pLD->GetMemberCollection(), list);
    }
    else if (GTWTase2VCCFolder *pVCC = dynamic_cast<GTWTase2VCCFolder*>(pMember))
    {
      GetAllSDOs(pVCC->GetMemberCollection(), list);
    }
  }
}

void GTWTase2Server::GetAllSDOs(GTWCollectionList *pCollection, std::list<GTWTase2SlaveDataObject*> &list)
{
  if (!pCollection)
  {
    return;
  }

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto sdoPos = pCollection->GetMap()->begin(); sdoPos != pCollection->GetMap()->end(); ++sdoPos)
  {
    GTWCollectionMember *pMember = sdoPos->second;
    if (pMember)
    {
      GTWTase2SlaveDataObject *pT2Sdo = dynamic_cast<GTWTase2SlaveDataObject*>(pMember);
      if (pT2Sdo)
      {
        list.push_back(pT2Sdo);
      }
    }
  }
}

/**********************************************************************************\
Function :			GTWTase2Server::UponInsert
Description : [none]
Return :			void	-
Parameters :
GTWCollectionBase *pParent	-
Note : [none]
\**********************************************************************************/
void GTWTase2Server::UponInsert(GTWCollectionBase *pParent)
{
  GTWCollectionListParent::UponInsert(pParent);
  m_pMemberCollection = new GTWCollectionList(this);
}

/**********************************************************************************\
Function :			GTWTase2Server::CompareTagName
Description : [none]
Return :			GTWDEFS_STAT	-
Parameters :
const char  **ppTagName	-
Note : [none]
\**********************************************************************************/
GTWDEFS_STAT GTWTase2Server::CompareTagName(  CStdString &tagName)
{
  CStdString alias = GetAliasName();
  GTWDEFS_STAT stat = GTWBaseDataObject::CompareTagField(tagName, alias);
  return(stat);
}

tmwTase2::Tase2Client* GTWTase2Server::CreatePeerClient()
{
  return m_pTase2ServerConnection->CreatePeerClient();
}

bool GTWTase2Server::CanEdit(const char *connectionToken)
{
  if (GetClientConnectedCount() > 0)
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, connectionToken, "TR_TASE2_NO_EDIT_WITH_CLIENTS_CON", "Cannot edit server while clients are connected");
    return false;
  }
  if (GetServerConnection()->IsConnectionAliveAndReady())
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Tase2, connectionToken, "TR_TASE2_NO_EDIT_CONN", "Cannot edit server while connection is up");
    return false;
  }
  return true;
}

void GTWTase2Server::AddServer(GTWTase2Server *pServer)
{
  if (pServer != NULL && (m_ServerList.find(pServer) == m_ServerList.end()))
  {
    m_ServerList.insert(pServer);
  }
}

void GTWTase2Server::StartAll()
{
  auto serverListPOS = m_ServerList.begin();
  while (serverListPOS != m_ServerList.end())
  {
    GTWTase2Server* pTASE2Server = *serverListPOS;
    ++serverListPOS;

    if (pTASE2Server != NULL)
    {
      pTASE2Server->Start();
    }
  }
}

void GTWTase2Server::RestartAll()
{
  auto serverListPOS = m_ServerList.begin();
  for (; serverListPOS != m_ServerList.end(); ++serverListPOS)
  {
    GTWTase2Server* pTASE2Server = *serverListPOS;
    if (pTASE2Server != NULL)
    {
      if (pTASE2Server->m_pChannelActiveMdo->GetValue() == false) // Do not restart if channel active control is off
      {
        continue;
      }
      pTASE2Server->ReStart();
    }
  }
}

/*
void GTWTase2Server::StopAll()
{  
  ... cant do this - need to add to deleteq in release mode


  POSITION pos2 = NULL; 
  for (POSITION pos1 = m_ServerList.GetHeadPosition(); (pos2 = pos1) != NULL; )
  {
    GTWTase2Server *pTASE2Server = m_ServerList.GetNext(pos1);
    m_ServerList.RemoveAt(pos2);

    pTASE2Server->GetServerConnection()->RegisterServerReadPointFunc(NULL);
    pTASE2Server->GetServerConnection()->RegisterClientChangedServerPointFunc(NULL);
    //    m_pTase2ServerConnection->RegisterPointChangeFunc(NULL);
    pTASE2Server->GetServerConnection()->RegisterServerOperationalEventFunc(NULL);
    pTASE2Server->GetServerConnection()->RegisterOnClientConnectedFunc(NULL);
    pTASE2Server->GetServerConnection()->RegisterOnClientConnectingFunc(NULL);
    pTASE2Server->GetServerConnection()->RegisterOnClientDisconnectedFunc(NULL);

    pTASE2Server->Stop();

    GTWCollectionBase *pClctn = pTASE2Server->GetParentCollection();
    if (pClctn != NULL)
    {
      pTASE2Server->TraverseMembers(GTWCollectionMember::AddMembertoDeleteQ,GTWCollectionMember::AddMembertoDeleteQ,NULL,NULL);

      //pClctn->RemoveCollectionMember(p61850Server);
      GTWCollectionMember::AddMembertoDeleteQ(pTASE2Server,NULL,false);
    }

    //delete pTASE2Server;
  }
}
  */

#ifdef DEBUG
void ChangeData(tmwTase2::Node* node)
{
  if (node && tmwTase2::Node::IsKindOf<tmwTase2::Tase2DataAttribute>(node))
  {

    tmw::String s;
    node->GetFullName(s);
    if (s.find("TASE2_Version") >= 0)
    {
      return;
    }

    tmwTase2::Tase2DataAttribute* pDA = dynamic_cast<tmwTase2::Tase2DataAttribute*>(node);
    if (pDA->GetType() == tmwTase2::Value::INTEGER || pDA->GetType() == tmwTase2::Value::UNSIGNED)
    {
      long value = pDA->GetLongValue();
      if (pDA->GetSize() == 8 && value > 122)
        pDA->SetLongValue(1);
      else
      {
        pDA->SetLongValue(value + 1);
      }

      //String sv;
      //((tmw61850::DataAttribute*)node)->GetValueAsString(sv);

      //String sn;
      //node->GetFullName(sn);
      //TRACE("SimulateDataChange for da '%s', value=%s\n", (const char*)sn, (const char*)sv);

    }
    else if (pDA->GetType() == tmwTase2::Value::FLOAT)
    {
      double value = pDA->GetDoubleValue();
      pDA->SetDoubleValue(value + 0.5);
    }
    else if (pDA->GetType() == tmwTase2::Value::BOOLEAN)
    {
      bool value = pDA->GetBooleanValue();
      pDA->SetBooleanValue(!value);
    }
    else if (pDA->GetType() == tmwTase2::Value::BITSTRING) // a State type, can be 0,1,2,3
    {
      tmw::BitString* value = (tmw::BitString*)pDA->GetBitStringValue();
      int bc = value->NumBits();
      for (int i = 0; i < bc; i++)
      {
        if (value->IsBitOn(i))
          value->SetBit(i, 0);
        else
          value->SetBit(i, 1);
      }
      pDA->SetBitStringValue(*value);
    }
    else // not supported yet
    {
      tmwTase2::Value::Type type = pDA->GetType();

      //int lll = 3;
    }
  }
}

void GTWTase2Server::SimulateChangeData(tmwTase2::Node* pNode)
{
  tmw::Array<tmwTase2::Node*>* pChildren = pNode->Children();

  size_t size = pChildren->size();
  for (unsigned int i = 0; i < size; i++)
  {
    tmwTase2::Node* pNode = pChildren->getAt(i);
    if (pNode && pNode->Children() && pNode->Children()->size() > 0)
    {
      SimulateChangeData(pNode);
    }
    else
    {
      ChangeData(pNode);
    }
  }

}

unsigned int __stdcall GTWTase2Server::SimulateThreadFunc(void* pParam)
{
  GtwTase2ThreadContext* pContext = (GtwTase2ThreadContext*)pParam;

  GTWTase2Server* pServer = pContext->m_pServer;
  while (1)
  {
    if (pServer->GetServerConnection()->IsConnectionAliveAndReady())
    {
      pServer->GetServerConnection()->StartTransaction();

      tmwTase2::Node* pModel = pServer->GetServerConnection()->Model();
      pServer->SimulateChangeData(pModel);

      pServer->GetServerConnection()->EndTransaction();
    }
    tmw::Thread::SleepMS(g_iSimulateThrottleTimeMS);
  }

  return 0;
}
#endif

std::string GTWTase2Server::GetNameFromConnection(void *pOb)
{
  auto pos = m_ServerList.begin();
  while (pos != m_ServerList.end())
  {
    GTWTase2Server* pTase2Server = *pos;
    ++pos;
    if (pTase2Server->GetServerConnection() == pOb)
      return pTase2Server->GetFullName();
  }
  return "";
}

void GTWTase2Server::getDomainConfigString(tmwTase2::Tase2Base* pConnection, const CStdString& sDomain, std::list<CStdString>& sConfigStringList)
{
  tmwTase2::Node* pDomainNode = nullptr;
  if (sDomain == "VCC" || sDomain.length() == 0)
  {
    pDomainNode = pConnection->Model();
  }
  else
  {
    std::list<tmwTase2::Tase2LogicalDevice*> domainList;
    pConnection->GetDomainList(domainList);
    for (auto iter : domainList)
    {
      tmwTase2::Tase2LogicalDevice* pLD = iter;
      if (pLD->GetName() == sDomain)
      {
        pDomainNode = pLD;
        break;
      }
    }
  }

  if (!pDomainNode)
  {
    return;
  }

  std::stringstream ss;

  CStdString sSpace = "    ";

  // Add points first
  int index = 0;

  sConfigStringList.push_back("Domain, " + sDomain);

  for (unsigned int i = 0; i < pDomainNode->Children()->size(); i++)
  {
    tmwTase2::Node* child = pDomainNode->Children()->getAt(i);

    tmwTase2::Tase2DataAttribute* pDA = dynamic_cast<tmwTase2::Tase2DataAttribute*>(child);
    if (!pDA)
    {
      continue;
    }

    const char* sDaName = pDA->GetName();
    if (tmwTase2::Tase2Server::IsSpecialProperty(sDaName))
    {
      continue;
    }

    CStdString sStringValue;
    sStringValue += sDaName;

    if (strcmp(sDaName, "Bilateral_Table_ID") == 0 || strcmp(sDaName, "TASE2_Version") == 0 || strcmp(sDaName, "Supported_Features") == 0)
    {
      sStringValue += ", ";
      //tmw::String sValue;
      //pDA->GetValueAsString(sValue);
      //sStringValue += (const char*)sValue;
    }
    else
    {
      sStringValue += " (";
      tmw::String s = pDA->GetTase2TypeString();
      sStringValue += (const char*)s;
      sStringValue += "), ";
    }

    tmw::String sValue;
    pDA->GetValueAsString(sValue);
    sStringValue += CStdString((const char*)sValue);

    sConfigStringList.push_back(sStringValue);
  }

  sConfigStringList.push_back("");
  sConfigStringList.push_back("DataSetTransfetSets");

  int nCount = 0;
  // Add DSTransfetSets
  for (unsigned int i = 0; i < pDomainNode->Children()->size(); i++)
  {
    tmwTase2::Node* child = pDomainNode->Children()->getAt(i);
    tmwTase2::Tase2DSTransferSet* pDSTS = dynamic_cast<tmwTase2::Tase2DSTransferSet*>(child);
    if (!pDSTS)
    {
      continue;
    }

    nCount++;
    CStdString sStringValue = sSpace;
    sStringValue += pDSTS->GetName();
    sStringValue += " (";
    if (pDSTS->GetStatus())
    {
      sStringValue += "Enabled, DataSet=";
      sStringValue += pDSTS->GetDataSetName();
    }
    else
    {
      sStringValue += "NOT Enabled";
    }
    sStringValue += ")";

    sConfigStringList.push_back(sStringValue);
  }

  if (nCount == 0)
  {
    CStdString sStringValue = sSpace;
    sStringValue += "N/A";
    sConfigStringList.push_back(sStringValue);
  }

  nCount = 0;
  sConfigStringList.push_back("");
  sConfigStringList.push_back("DataSets:");

  // Add DataSets
  for (unsigned int i = 0; i < pDomainNode->Children()->size(); i++)
  {
    tmwTase2::Node* child = pDomainNode->Children()->getAt(i);
    tmwTase2::Tase2DataSet* pDS = dynamic_cast<tmwTase2::Tase2DataSet*>(child);
    if (!pDS)
    {
      continue;
    }

    CStdString sStringValue;
    sStringValue += pDS->GetName();
    if (sStringValue.Find("Marked For Deletion") != std::string::npos) // do now show datasets marked for deletion
    {
      continue;
    }

    nCount++;

    tmw::Array<tmwTase2::Tase2DataAttribute*>* points = pDS->GetDSPoints();
    int size = points->size();

    sStringValue += " (# Members=";
    char buf[32];
    sStringValue += tmw_itoa(size, buf, 10);
    sStringValue += ")";
    sConfigStringList.push_back(sStringValue.c_str());

    if (size > 0)
    {
      tmw::String s;
      for (int i = 0; i < size; i++)
      {
        tmwTase2::Tase2DataAttribute* pDA = points->getAt(i);
        CStdString sName = sSpace + sSpace;
        sName += pDA->GetFullName(s);
        sName += " (";
        sName += pDA->GetTase2TypeString();
        sName += ")";
        sConfigStringList.push_back(sName);
      }
    }
  }

  if (nCount == 0)
  {
    CStdString sStringValue = sSpace;
    sStringValue += "N/A";
    sConfigStringList.push_back(sStringValue.c_str());
  }
}

void GTWTase2Server::getDomainList(std::list<CStdString>& domainList)
{
  std::list<tmwTase2::Tase2LogicalDevice*> ldList;
  GetServerConnection()->GetDomainList(ldList);

  domainList.push_back("VCC");
  for (auto it : ldList)
  {
    domainList.push_back(it->GetName());
  }
}

ServiceRole ServiceRole_to_enum(const char *enumStr)
{
  if (tmw::util::compare(enumStr, "CLIENT_ROLE"))
    return CLIENT_ROLE;
  if (tmw::util::compare(enumStr, "SERVER_ROLE"))
    return SERVER_ROLE;
  if (tmw::util::compare(enumStr, "CLIENT_SERVER_ROLE"))
    return CLIENT_SERVER_ROLE;

  throw tmw::Exception("ServiceRole out of range %s", enumStr);
}

const char *ServiceRole_to_string(ServiceRole enumID)
{
  switch (enumID)
  {
    case CLIENT_ROLE:
      return "CLIENT_ROLE";
    case SERVER_ROLE:
      return "SERVER_ROLE";
    case CLIENT_SERVER_ROLE:
      return "CLIENT_SERVER_ROLE";
  }

  throw tmw::Exception("ServiceRole out of range %d", enumID);
}

ServiceRole ServiceRole_to_enum_from_hr(const char *enumStr)
{
  if (tmw::util::compare(enumStr, "Client"))
    return CLIENT_ROLE;
  if (tmw::util::compare(enumStr, "Server"))
    return SERVER_ROLE;
  if (tmw::util::compare(enumStr, "Client / Server"))
    return CLIENT_SERVER_ROLE;

  throw tmw::Exception("ServiceRole out of range %s", enumStr);
}

const char *ServiceRole_to_hr(ServiceRole enumID)
{
  switch (enumID)
  {
    case CLIENT_ROLE:
      return "Client";
    case SERVER_ROLE:
      return "Server";
    case CLIENT_SERVER_ROLE:
      return "Client / Server";
  }

  throw tmw::Exception("ServiceRole out of range %d", enumID);
}

std::string ServiceRole_to_hr_json()
{
  nlohmann::json json_ary;
  nlohmann::json json;
  json["CLIENT_ROLE"] = "Client";
  json_ary.push_back(json);

  nlohmann::json json1;
  json1["SERVER_ROLE"] = "Server";
  json_ary.push_back(json1);

  nlohmann::json json2;
  json2["CLIENT_SERVER_ROLE"] = "Client / Server";
  json_ary.push_back(json2);
  return json_ary.dump();
}
