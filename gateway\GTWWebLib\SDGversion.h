/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2021 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/
#ifndef SDG_TMWVERSION
#define SDG_TMWVERSION

#define SDG_TMWVERSION_MAJOR 5
#define SDG_TMWVERSION_MINOR 3
#define SDG_TMWVERSION_PATCH 0
#define SDG_TMWVERSION_BUILD 1029
#define SDG_TMWVERSION_STRING "5.03.0000"
#define SDG_MANAGED_SCL_VERSION_STRING "5.03.0000.0000"

#define SDG_TMWVERSION_DATE "Wed Sep 24 09:09:05 2025"

#define SDG_TMWVERSION_DATE_SEC 5
#define SDG_TMWVERSION_DATE_MIN 9
#define SDG_TMWVERSION_DATE_HOUR 9
#define SDG_TMWVERSION_DATE_DAY 24
#define SDG_TMWVERSION_DATE_MONTH 9
#define SDG_TMWVERSION_DATE_YEAR 2025

#ifdef __cplusplus
extern "C" {
#endif

extern void SDG_getVersionInfo(
  unsigned int *pMajor,
  unsigned int *pMinor,
  unsigned int *pPatch,
  const char **ppDate);

extern void SDG_getVersionTime(
  int *pSec,
  int *pMin,
  int *pHour,
  int *pDay,
  int *pMonth,
  int *pYear);

#ifdef __cplusplus
}
#endif
#endif
