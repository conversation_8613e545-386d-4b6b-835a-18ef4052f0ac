/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWequat.cpp                                                */
/* DESCRIPTION:  Class definition for equation MD<PERSON>'s                         */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 11 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

#include "gateway/GTWLib/strmio.h"
#include "GTWEquationReadConverterTemplate.h"
#include "GTWEquationCommon.h"
#include "GTWEquationConstant.h"
#include "GTWEquationDataObject.h"
#include "GTWmdoEquationEditor.h"



static const char* ClassName = "GTWEquationDataObject";
static tmw::LeakDetector gcounter(ClassName);

#ifdef DEBUG
void TraceDT(const std::string &sContext, TMWDTIME &dt)
{
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_DNP, nullptr, "%s : %d/%d/%d %d:%d:%d:%d\n", sContext.c_str(), dt.dayOfMonth, dt.month, dt.year, dt.hour, dt.minutes, dt.mSecsAndSecs / 1000, dt.mSecsAndSecs % 1000);
}
#endif

#ifdef _WIN32
#define LL "%I64d%c"
#else
#define LL "%ld%c"
#endif


TMWTYPES_UINT GTWEquationDataObject::g_iEquationNumber = 0;

/*****************************************************************************/
/*                             TYPE DEFINITIONS                              */
/*****************************************************************************/

GTWEQUAT_FUNC_DESCRIPTOR baseDescriptor = {"Base",TMWDEFS_NULL};


ImplementClassBaseInfo (GTWEquationDataObject,GTWInternalBaseDataObject,pClassInfo1,new GTWEquationDataObject("",""));

/*****************************************************************************/
/*                              LOCAL CONSTANTS                              */
/*****************************************************************************/

static const GTWEQUAT_ACTION action[GS_NUM_STATES][TMWTOKEN_NUM_TYPES] =
{
  /* TMWTOKEN_TYPE_=>        NONE         CTRL        TAB        NEWLINE     WHITESPACE    OPERATOR     CONSTANT    IDENTIFIER     STRING   */
  /* GS_...              ------------ ------------ ---------- ------------ ------------ ------------ ------------ ------------ ------------ */
  /* START           */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPERATOR ,GA_SYMBOL   ,GA_SYMBOL   ,GA_STRING   },
  /* IN_FUNCTION     */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPERATOR ,GA_SYMBOL   ,GA_SYMBOL   ,GA_STRING   },
  /* AFTER_COMMA     */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPERATOR ,GA_SYMBOL   ,GA_SYMBOL   ,GA_STRING   },
  /* IN_PAR_PHRASE   */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPERATOR ,GA_SYMBOL   ,GA_SYMBOL   ,GA_STRING   },
  /* IN_OPER_EXPR    */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPERATOR ,GA_SYMBOL   ,GA_SYMBOL   ,GA_STRING   },
  /* IN_COMMENT      */ {GA_UNEX_CMNT,GA_SAME       ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_CMNT_END ,GA_SAME     ,GA_SAME     ,GA_SAME     },
  /* WTNG_FOR_LPAR   */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPER_TEST,GA_BAD_TOKEN,GA_BAD_TOKEN,GA_BAD_TOKEN},
  /* WTNG_FOR_END    */ {GA_END,      GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPER_TEST,GA_BAD_TOKEN,GA_BAD_TOKEN,GA_BAD_TOKEN},
  /* WTNG_FOR_COMMA  */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPER_TEST,GA_BAD_TOKEN,GA_BAD_TOKEN,GA_BAD_TOKEN},
  /* WTNG_FOR_OPER   */ {GA_END,      GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPER_TEST,GA_BAD_TOKEN,GA_BAD_TOKEN,GA_BAD_TOKEN},
  /* WTNG_FOR_RPAR   */ {GA_UNEX_END, GA_WARN_CTRL  ,GA_SAME   ,GA_SAME     ,GA_SAME     ,GA_OPER_TEST,GA_BAD_TOKEN,GA_BAD_TOKEN,GA_BAD_TOKEN},
};

/*****************************************************************************/

/* This array specifies multi-char operators; */
/* no need to specify single-char operators   */

static const char *operatorStrings[] = 
{
  "/*",  /* comment begin */
  "*/",  /* comment end */
  "&&",  /* logical and */
  "||",  /* logical or */
  "==",  /* equals */
  "!=",  /* not equal */
  ">=",  /* greater than or equal */
  "<=",  /* less than or equal */
};

static GTWEQUAT_FUNC_DESCRIPTOR *funcDescriptors[] = 
{
  &gtweqcnv_convertToDouble, 
  &gtweqcnv_convertToFloat,  
  &gtweqcnv_convertToLong,   
  &gtweqcnv_convertToULong,  
  &gtweqcnv_convertToShort,  
  &gtweqcnv_convertToUShort, 
  &gtweqcnv_convertToChar,  
  &gtweqcnv_convertToUChar, 
  &gtweqcnv_convertToBool,
  &gtweqcnv_convertToInt64,
  &gtweqcnv_convertToUInt64,
  &gtweqath_setVQTFunction,
  &gtweqath_setVQTFunctionSync,
  &gtweqath_setVQFunctionPeriodic,
  &gtweqath_ifFunction,
  &gtweqath_ifDelayFunction,
  &gtweqath_iftFunction,
  &gtweqath_sumFunction,
  &gtweqath_subtractFunction,
  &gtweqath_multiplyFunction,
  &gtweqath_divideFunction,
  &gtweqath_MakeDoublePointFunction,
  &gtweqath_MakeFloatFunction,
  &gtweqath_UShortToInt64,
  &gtweqath_UShortToUInt64,
  &gtweqath_UShortToFloat64,
  &gtweqath_concatFunction,
  &gtweqath_DoCROBFunction,
  &gtweqath_DoCROBwCountFunction,
  &gtweqath_IPAddrToNumFunction,
  &gtweqlgc_BitWiseAndFunction,
  &gtweqlgc_BitWiseOrFunction,
  &gtweqlgc_LogicalAndFunction,
  &gtweqlgc_LogicalOrFunction,
  &gtweqlgc_LogicalXorFunction,
  &gtweqlgc_TestBitFunction,
  &gtweqlgc_BitWiseGetFlagsFunction,
  &gtweqlgc_BitWiseGetQualityFunction,
  &gtweqlgc_GetGatewayQualityFunction,
  &gtweqlgc_notFunction,
  &gtweqlgc_BitWiseNotFunction,
  &gtweqlgc_mkDblFunction,
  &gtweqlgc_BitStringFunction,
  &gtweqlgc_WBitStringFunction,
  &gtweqlgc_RandomFunction,
  &gtweqlgc_RampFunction,
  &gtweqlgc_SineFunction,
  &gtweqlgc_SawFunction,
  &gtweqlgc_SquareFunction,
  &gtweqlgc_SquareDTFunction,
  &gtweqlgc_PulseFunction,
  &gtweqlgc_PulseFunction2,
  &gtweqlgc_PulseOnEventFunction,
  &gtweqlgc_CountChangeFunction,
  &gtweqlgc_OnChangeFunction,
  &gtweqlgc_OnStrChangeFunction,
  &gtweqlgc_OnNumChangeFunction,
  &gtweqlgc_AnyChangedFunction,
  &gtweqcmp_RoundFunction,
  &gtweqcmp_EqualsFunction,
  &gtweqcmp_NotEqualsFunction,
  &gtweqcmp_GreaterThanOrEqualsFunction,
  &gtweqcmp_LessThanOrEqualsFunction,
  &gtweqcmp_GreaterThanFunction,
  &gtweqcmp_LessThanFunction,
  &gtweqlgc_SteadyFunction,
  &gtweqlgc_gettimeFunction,
  &gtweqlgc_TimeAsStringFunction,
  &gtweqlgc_time_to_msFunction,
  &gtweqlgc_time_from_msFunction,
#if USE_OPC_44
  &gtweqath_OPCCommandUserPwdFunction,
#endif
  &gtweqcmp_StringCompareFunction,

  & gtweqmath_degToRadFunction,
  & gtweqmath_radToDegFunction,
  & gtweqmath_sineFunction,
  & gtweqmath_cosineFunction,
  & gtweqmath_tanFunction,
  & gtweqmath_absFunction,
  & gtweqmath_sqrtFunction,
  & gtweqmath_powFunction,
  & gtweqmath_expFunction,
  & gtweqmath_logFunction,
  & gtweqmath_maxFunction,
  & gtweqmath_minFunction
};

int GTWEquationDataObject::getNumFunctions(void)
{
  return TMWDEFS_ARRAY_LENGTH(funcDescriptors);
}

GTWEQUAT_FUNC_DESCRIPTOR *GTWEquationDataObject::getFunction(int i)
{
  return funcDescriptors[i];
}
/*****************************************************************************/
/* The following specifies the supported operators and their corresponding   */
/* functional equivalents.  The an operator has a higher precedence order,   */
/* it will be evaluated before an operator with a lower precedence order     */
/* even if the lower order operator occurs first in an expression phrase.    */
/* Parenthesis, of course, demark expression phrases.  If operators have the */
/* same precedence order the will be evaluated left-to-right.                */
/*****************************************************************************/

static GTWEQUAT_OPER_DESCRIPTOR operatorFunctions[] =
{
    "*",   "expr * expr", "Arithmetic result of multiplying the exprs on either side of the operator",   4,      &gtweqath_multiplyFunction,
    "/",   "expr / expr", "Arithmetic result of dividing the exprs on either side of the operator",   4,      &gtweqath_divideFunction,
    "+",   "expr + expr", "Arithmetic result of adding the exprs on either side of the operator",   3,      &gtweqath_sumFunction,
    "-",   "expr - expr", "Arithmetic result of subtracting the exprs on either side of the operator",   3,      &gtweqath_subtractFunction,
    "&",   "expr & expr", "Bit Wise 'and' of the exprs on either side of the operator",   3,      &gtweqlgc_BitWiseAndFunction,
    "|",   "expr | expr", "Bit Wise 'or' of the exprs on either side of the operator",   3,      &gtweqlgc_BitWiseOrFunction,
    "&&",  "expr && expr", "Logical 'and' of the exprs on either side of the operator",   2,      &gtweqlgc_LogicalAndFunction,
    "||",  "expr || expr", "Logical 'or' of the exprs on either side of the operator",   1,      &gtweqlgc_LogicalOrFunction,
    "==",  "expr == expr", "Compares the left and right side of the '==' and returns true if they are the same value",   4,      &gtweqcmp_EqualsFunction,
    "!=",  "expr != expr", "Compares the left and right side of the '!=' and returns true if they are not the same value",   4,      &gtweqcmp_NotEqualsFunction, 
    ">=",  "expr >= expr", "Compares the left and right side of the '>=' and returns true if the left side is greater than or equal to the right side",   4,      &gtweqcmp_GreaterThanOrEqualsFunction, 
    "<=",  "expr <= expr", "Compares the left and right side of the '<=' and returns true if the left side is greater than or equal to the right side",   4,      &gtweqcmp_LessThanOrEqualsFunction, 
    ">",   "expr > expr", "Compares the left and right side of the '>' and returns true if the left side is greater than the right side",   4,      &gtweqcmp_GreaterThanFunction, 
    "<",   "expr < expr", "Compares the left and right side of the '<' and returns true if the right side is greater than the left side",   4,      &gtweqcmp_LessThanFunction, 
};


GTWEquationDataObject::GTWEquationDataObject(const char* pName, const char* pEqString) :
  GTWEquationFunctionConverter(&baseDescriptor),
  m_iRecursionDepth(0),
  m_bDoNotCallUpdateMDO(false),
  m_sEquationString(pEqString),
  m_name(pName),
  m_equationStdQuality(GTWDEFS_STD_QLTY_UNINITIALIZED),
  m_bUpdateIfNoChange(TMWDEFS_FALSE),
  m_timeSource(GTWDEFS_UPDATE_TIME),
  m_timeSourceSpecified(TMWDEFS_FALSE),
  m_bSuspendUpdate(false),
  m_bCallingArgumentUpdate(false)
{
  gcounter.Add(this);
  m_pLastUpdateMdo = nullptr;
  tempValueSaved = 0.0;
  tempSavedStdQuality = GTWDEFS_STD_QLTY_NOT_TOPICAL;

  GTWBaseDataObject* pBdo = static_cast<GTWBaseDataObject*>(this);
  GTWMasterDataObject::setBdo(pBdo);
  GTWSlaveDataObject::setBdo(pBdo);
  //pBdo->m_eReportedTimeQuality = GTWDEFS_TIME_QLTY_ASUM;
  m_equationTimeCnvtrFunc = nullptr;
  m_equationStringCnvtrFunc = nullptr;

  m_equationBoolCnvtrFunc = nullptr;

  m_equationULongCnvtrFunc = nullptr;
  m_equationLongCnvtrFunc = nullptr;

  m_equationUShortCnvtrFunc = nullptr;
  m_equationShortCnvtrFunc = nullptr;

  m_equationUCharCnvtrFunc = nullptr;
  m_equationCharCnvtrFunc = nullptr;

  m_equationSFloatCnvtrFunc = nullptr;
  m_equationDoubleCnvtrFunc = nullptr;
  m_equationInt64CnvtrFunc = nullptr;
  m_equationUInt64CnvtrFunc = nullptr;

  m_iEquationNumber = g_iEquationNumber;
  ++g_iEquationNumber;

  ReportedTimeQuality(0);
}

GTWEquationDataObject::~GTWEquationDataObject()
{
  gcounter.Remove(this);
  DeleteConverters();

  GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

void GTWEquationDataObject::Rename(const CStdString& sName)
{
  GTWCollectionBase* pCollection = GetGTWApp()->getMdoCollection();
  if (pCollection->RenameCollectionMember(m_name, sName, this))
  {
    m_name = sName;
  }
}

int GTWEquationDataObject::getNumOperators()
{
  return TMWDEFS_ARRAY_LENGTH(operatorFunctions);
}

GTWEQUAT_OPER_DESCRIPTOR *GTWEquationDataObject::getOperator(int i)
{
  return &operatorFunctions[i];
}

/*****************************************************************************/
/*****************************************************************************/
/*                                                                           */
/*                           FUNCTION DEFINITIONS                            */
/*                                                                           */
/*****************************************************************************/
/*****************************************************************************/

GTWBaseEditor *GTWEquationDataObject::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTWmdoEquationEditor(dto, GetParentMember(), this, false);
  else 
    m_pEditor->SetDTO(dto);
  
  return m_pEditor;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTWEquationDataObject::getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg)
{
  switch(m_equationType)
  {
    case(GTWCNVTR_TYPE_TMWDTIME):
      char timeBuf[64];
      tmwdiag_time2string(&m_EquationValue.timeValue, TMWDEFS_TIME_FORMAT_56, timeBuf, sizeof(timeBuf), TMWDEFS_FALSE);
      msg.Format("%s", timeBuf);
      break;
    case(GTWCNVTR_TYPE_STRING):
      msg = m_equationStringValue;
      break;
    case(GTWCNVTR_TYPE_BOOL):
      msg.Format(pFormat.stringFormat, m_EquationValue.boolValue?"On":"Off");
      break;
    case(GTWCNVTR_TYPE_CHAR):
    case(GTWCNVTR_TYPE_SHORT):
    case(GTWCNVTR_TYPE_LONG):
      msg.Format(pFormat.signedFormat, 
                 TMWTYPES_INT(m_EquationValue.signedValue));
      break;
    case(GTWCNVTR_TYPE_UCHAR):
    case(GTWCNVTR_TYPE_USHORT):
    case(GTWCNVTR_TYPE_ULONG):
      msg.Format(pFormat.unsignedFormat, 
                 TMWTYPES_UINT(m_EquationValue.unsignedValue));
      break;
    case(GTWCNVTR_TYPE_SFLOAT):
      msg.Format(pFormat.floatFormat, m_EquationValue.floatValue);
      break;
    case(GTWCNVTR_TYPE_INT64):
      msg.Format(pFormat.signed64bitIntFormat,
        int64_t(m_EquationValue.signed64bitIntValue));
      break;
    case(GTWCNVTR_TYPE_UINT64):
      msg.Format(pFormat.unsigned64bitIntFormat,
        uint64_t(m_EquationValue.unsigned64bitIntValue));
      break;
    case(GTWCNVTR_TYPE_DOUBLE):
      msg.Format(pFormat.floatFormat, m_EquationValue.doubleValue);
      break;
    default:
      msg.Format(pFormat.floatFormat, m_EquationValue.doubleValue);
      break;
  }
}

void GTWEquationDataObject::getMdoValueAsVariant(GtwVariant &variant)
{
  switch(m_equationType)
  {
    case(GTWCNVTR_TYPE_BOOL):
      variant = m_EquationValue.boolValue;
      break;
    case(GTWCNVTR_TYPE_CHAR):
    case(GTWCNVTR_TYPE_SHORT):
    case(GTWCNVTR_TYPE_LONG):
      variant = m_EquationValue.signedValue;
      break;
    case(GTWCNVTR_TYPE_UCHAR):
    case(GTWCNVTR_TYPE_USHORT):
    case(GTWCNVTR_TYPE_ULONG):
      variant = (double)m_EquationValue.unsignedValue;
      break;
    case(GTWCNVTR_TYPE_SFLOAT):
      variant = m_EquationValue.floatValue;
      break;
    case(GTWCNVTR_TYPE_STRING):
      variant = (CStdString)this->m_equationStringValue;
      break;
    case(GTWCNVTR_TYPE_TMWDTIME):
      {
        //DATE date=0;

        //FILETIME fileTime;
        //if (ConvertTMWDTIMEToFileTime(&m_EquationValue.timeValue, &fileTime))
        //{
        //  SYSTEMTIME systime;
        //  if( FileTimeToSystemTime(&fileTime, &systime) )
        //  {
        //    INT bOK = SystemTimeToVariantTime( &systime, &date);
        //  }
        //}

        GtwOsDateTime dateTime;
        dateTime.SetFromTMWDTime(&m_EquationValue.timeValue);

        variant = dateTime;

        //variant.AssignFromTMWDTIME(m_EquationValue.timeValue);

        //variant.vt = VT_DATE;
        //variant.date = date;
      }
      break;
    case(GTWCNVTR_TYPE_INT64):
      variant = m_EquationValue.signed64bitIntValue;
      break;
    case(GTWCNVTR_TYPE_UINT64):
      variant = m_EquationValue.unsigned64bitIntValue;
      break;
    case(GTWCNVTR_TYPE_DOUBLE):
      variant = m_EquationValue.doubleValue;
      break;
    default:
      variant = m_EquationValue.doubleValue;
      break;
  }
}

/*****************************************************************************/

void *GTWEquationDataObject::bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType)
{
  void *gtwcnvtr = TMWDEFS_NULL;

  switch(cnvtrType)
  {
    case(GTWCNVTR_TYPE_FLAGS):   
      gtwcnvtr = new GTWEquationReadConverterTemplate<bool>(this);   
      break;

    case(GTWCNVTR_TYPE_BOOL):   
      gtwcnvtr = new GTWEquationReadConverterTemplate<bool>(this);   
      break;

    case(GTWCNVTR_TYPE_SHORT):  
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_SHORT>(this);  
      break;

    case(GTWCNVTR_TYPE_LONG):   
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_INT>(this);   
      break;

    case(GTWCNVTR_TYPE_USHORT): 
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_USHORT>(this); 
      break;

    case(GTWCNVTR_TYPE_ULONG):  
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_UINT>(this);  
      break;

    case(GTWCNVTR_TYPE_SFLOAT): 
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_SFLOAT>(this); 
      break;

    case(GTWCNVTR_TYPE_DOUBLE): 
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_DOUBLE>(this); 
      break;

    case(GTWCNVTR_TYPE_INT64):
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_INT64>(this);
      break;

    case(GTWCNVTR_TYPE_UINT64):
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWTYPES_UINT64>(this);
      break;

    case(GTWCNVTR_TYPE_TMWDTIME):
      gtwcnvtr = new GTWEquationReadConverterTemplate<TMWDTIME>(this); 
      break;
  }

  if(gtwcnvtr != TMWDEFS_NULL) 
    addSdoToReadBoundList(pUpdateSdo); 

  return(gtwcnvtr);
}

void GTWEquationDataObject::storeUpdatedTime(TMWDTIME *pTimeStamp)
{
  if (pTimeStamp)
  {
    GTWBaseDataObject::storeUpdatedTime(pTimeStamp);
  }
  else if (canUseUpdatedTime()) // Only if the underlying equation supports updated time being set to NOW (NULL). E.g. "if" does not allow this
  {
    GTWBaseDataObject::storeUpdatedTime(NULL);
  }
}

void GTWEquationDataObject::getDisplayedReportedTime(TMWDTIME *pDateTime, GTWDEFS_TIME_QLTY *pTimeQuality)
{
  bool bUseReported = GTWConfig::EquationTimeSource == GTWDEFS_REPORTED_TIME;

  *pTimeQuality &= ~(GTWDEFS_TIME_QLTY_REMOTE | GTWDEFS_TIME_QLTY_ASUM); // turn off both bits
  *pTimeQuality |= bUseReported ? GTWDEFS_TIME_QLTY_REMOTE : GTWDEFS_TIME_QLTY_ASUM;

  if (bUseReported)
  {
    TMWDTIME rTime = ReportedTime();
    GtwTimeZone::ConvertUtcToSpecificTime(rTime, *pDateTime, *(GtwTimeZone::GetCurrentTimeZone()));
  }
  else
  {
    GTWBaseDataObject::getDisplayedReportedTime(pDateTime, pTimeQuality);
  }
}


bool GTWEquationDataObject::canUseUpdatedTime()
{
  bool bResult = true;

  switch(m_equationType)
  {
    case(GTWCNVTR_TYPE_TMWDTIME):
      bResult = m_equationTimeCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_STRING):
      bResult = m_equationStringCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_BOOL):
      bResult = m_equationBoolCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_CHAR):
      bResult = m_equationCharCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_SHORT):
      bResult = m_equationShortCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_LONG):
      bResult = m_equationLongCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_UCHAR):
      bResult = m_equationUCharCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_USHORT):
      bResult = m_equationUShortCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_ULONG):
      bResult = m_equationULongCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_SFLOAT):
      bResult = m_equationSFloatCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_DOUBLE):
      bResult = m_equationDoubleCnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_INT64):
      bResult = m_equationInt64CnvtrFunc->canUseUpdatedTime();
      break;
    case(GTWCNVTR_TYPE_UINT64):
      bResult = m_equationUInt64CnvtrFunc->canUseUpdatedTime();
      break;
  }

  return bResult;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

bool GTWEquationDataObject::getConverterReportedTime(TMWDTIME *dt)
{
  bool bResult = false;
  GTWDEFS_STD_QLTY stdQuality;

#define GET_TIME(eqn) \
  bResult = eqn->getTime(this, dt, &stdQuality); \
  if (!bResult) \
  {\
    bResult = eqn->getTime(dt);  \
  }\

  switch(m_equationType)
  {
    case(GTWCNVTR_TYPE_TMWDTIME):
      GET_TIME(m_equationTimeCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_STRING):
      GET_TIME(m_equationStringCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_BOOL):
      GET_TIME(m_equationBoolCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_CHAR):
      GET_TIME(m_equationCharCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_SHORT):
      GET_TIME(m_equationShortCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_LONG):
      GET_TIME(m_equationLongCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_UCHAR):
      GET_TIME(m_equationUCharCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_USHORT):
      GET_TIME(m_equationUShortCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_ULONG):
      GET_TIME(m_equationULongCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_SFLOAT):
      GET_TIME(m_equationSFloatCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_DOUBLE):
      GET_TIME(m_equationDoubleCnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_INT64):
      GET_TIME(m_equationInt64CnvtrFunc)
      break;
    case(GTWCNVTR_TYPE_UINT64):
      GET_TIME(m_equationUInt64CnvtrFunc)
      break;
  }

  return bResult;
}

void GTWEquationDataObject::updateValues()
{
  m_bDoNotCallUpdateMDO = true;
  updateSDO(GTWDEFS_UPDTRSN_REQUESTED, this);
  m_bDoNotCallUpdateMDO = false;
}

GTWReadConverterBase* GTWEquationDataObject::getReadConverter()
{
  switch (m_equationType)
  {
  case GTWCNVTR_TYPE_TMWDTIME: return m_equationTimeCnvtrFunc;
  case GTWCNVTR_TYPE_STRING: return m_equationStringCnvtrFunc;
  case GTWCNVTR_TYPE_BOOL: return m_equationBoolCnvtrFunc;
  case GTWCNVTR_TYPE_CHAR: return m_equationCharCnvtrFunc;
  case(GTWCNVTR_TYPE_SHORT): return m_equationShortCnvtrFunc;
  case(GTWCNVTR_TYPE_LONG): return m_equationLongCnvtrFunc;
  case(GTWCNVTR_TYPE_UCHAR): return m_equationUCharCnvtrFunc;
  case(GTWCNVTR_TYPE_USHORT): return m_equationUShortCnvtrFunc;
  case(GTWCNVTR_TYPE_ULONG): return m_equationULongCnvtrFunc;
  case(GTWCNVTR_TYPE_SFLOAT): return m_equationSFloatCnvtrFunc;
  case(GTWCNVTR_TYPE_INT64): return m_equationInt64CnvtrFunc;
  case(GTWCNVTR_TYPE_UINT64): return m_equationUInt64CnvtrFunc;
  case(GTWCNVTR_TYPE_DOUBLE): return m_equationDoubleCnvtrFunc;
  }
  assert(false);
  return nullptr;
}

void GTWEquationDataObject::updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject* pMdo)
{
  assert(this->isWritable());
  if (isWritable())
  {
    GtwVariant value;
    pMdo->getMdoValueAsVariant(value);

    GTWReadConverterBase* pReadConverter = getReadConverter();
    pReadConverter->updateValue(value, this);
  }
}

void GTWEquationDataObject::updateMDO(GTWDEFS_UPDTRSN updateReason)
{
  if (m_bDoNotCallUpdateMDO)
  {
    return;
  }
  GTWMasterDataObject::updateMDO(updateReason);
}

void GTWEquationDataObject::updateSDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo)
{
  // call this for each equation to give notify the equation an update of one of its arguments is occurring
  if (!m_bCallingArgumentUpdate)
  {
    m_bCallingArgumentUpdate = true;
    getReadConverter()->OnArgumentUpdate(this, pMdo);
    m_bCallingArgumentUpdate = false;
  }

  if (m_bSuspendUpdate)
  {
    return;
  }

  m_iRecursionDepth++;

  // Get reported time before call get value on converter because calling getValue can change the time to the value from getTime above
  TMWDTIME preUpdateTime = this->ReportedTime();

  // If we are configured to use reported time for equations see if we
  //  can get the reported time from this MDO. If not we use the reported
  //  time from the last MDO to cause an update
  if (pMdo != TMWDEFS_NULL)
  {
    if ((m_timeSourceSpecified && (m_timeSource == GTWDEFS_REPORTED_TIME))
      || (!m_timeSourceSpecified && (GTWConfig::EquationTimeSource == GTWDEFS_REPORTED_TIME)))
    {
      m_timeSource = GTWDEFS_REPORTED_TIME;
      GTWDEFS_TIME_QLTY timeQuality = GTWDEFS_TIME_QLTY_REMOTE; // Initialize to remote since EquationTimeSource is GTWDEFS_REPORTED_TIME
      ReportedTimeQuality(timeQuality);
      TMWDTIME tempTime;
      GtwTime::InitTMWDTIME(&tempTime);

      if (!getConverterReportedTime(&tempTime))
      {
        GTWBaseDataObject *pBdo = NULL;
        if (pMdo != NULL)
        {
          pBdo = pMdo->getBdo();
        }
        if (pBdo != NULL)
        {
          pBdo->getMdoReportedTime(&tempTime, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
        }
        else
        {
          getMdoReportedTime(&tempTime, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
        }
      }

      ReportedTimeQuality(timeQuality);
      SetReportedTime(&tempTime);
      // set updated time as well
      TMWDTIME utcTime;
      GtwTimeZone::SetCurrentUtcTime(utcTime);
      SetUpdatedTime(&utcTime);
    }
    else
    {
      m_timeSource = GTWDEFS_UPDATE_TIME;
      ReportedTimeQuality(GTWDEFS_TIME_QLTY_ASUM);
    }
  }

  this->m_pLastUpdateMdo = pMdo;

#define CHECK_TIME_MODIFIED(eqn) \
  GTWDEFS_STD_QLTY stdQualityTemp; \
  TMWDTIME         tempTimeValue; \
  bool bTimeOk = eqn->getTime(this, &tempTimeValue, &stdQualityTemp); \
  bool bTimeChanged = false; \
  if (bTimeOk) \
  { \
    if (m_timeSource == GTWDEFS_REPORTED_TIME || GTWConfig::EquationTimeSource == GTWDEFS_REPORTED_TIME) \
    { \
      bTimeChanged = (tmwdtime_compareTime(&preUpdateTime, &tempTimeValue) != 0); \
    } \
    else \
    { \
      bTimeChanged = false; \
    } \
  }

  // Based on equation type, call the appropriate converter to calculate
  //  the current value of the equation
  switch (m_equationType)
  {
  case(GTWCNVTR_TYPE_TMWDTIME):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWDTIME     timeValue;

    m_equationTimeCnvtrFunc->getValue(this, &timeValue, &stdQuality);

    if ((tmwdtime_compareTime(&m_EquationValue.timeValue, &timeValue) != 0)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange)
    {
      m_EquationValue.timeValue = timeValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }

      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_STRING):
  {
    GTWDEFS_STD_QLTY stdQuality;
    CStdString     strValue;

    m_equationStringCnvtrFunc->getValue(this, &strValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationStringCnvtrFunc);

    if ((m_equationStringValue != strValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_equationStringValue = strValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_BOOL):
  {
    GTWDEFS_STD_QLTY stdQuality;
    bool     boolValue;

    m_equationBoolCnvtrFunc->getValue(this, &boolValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationBoolCnvtrFunc);

    if ((m_EquationValue.boolValue != boolValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.boolValue = boolValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_CHAR):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_CHAR    charValue;

    m_equationCharCnvtrFunc->getValue(this, &charValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationCharCnvtrFunc);

    if ((TMWTYPES_SHORT(m_EquationValue.signedValue) != charValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.signedValue = charValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_SHORT):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_SHORT    shortValue;

    m_equationShortCnvtrFunc->getValue(this, &shortValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationShortCnvtrFunc);

    if ((TMWTYPES_SHORT(m_EquationValue.signedValue) != shortValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.signedValue = shortValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_LONG):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_LONG     longValue;

    m_equationLongCnvtrFunc->getValue(this, &longValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationLongCnvtrFunc);

    if ((m_EquationValue.signedValue != longValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.signedValue = longValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_UCHAR):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_UCHAR   ucharValue;

    m_equationUCharCnvtrFunc->getValue(this, &ucharValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationUCharCnvtrFunc);

    if ((TMWTYPES_UCHAR(m_EquationValue.unsignedValue) != ucharValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.unsignedValue = ucharValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_USHORT):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_USHORT   ushortValue;

    m_equationUShortCnvtrFunc->getValue(this, &ushortValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationUShortCnvtrFunc);

    if ((TMWTYPES_USHORT(m_EquationValue.unsignedValue) != ushortValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.unsignedValue = ushortValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_ULONG):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_ULONG    ulongValue;

    m_equationULongCnvtrFunc->getValue(this, &ulongValue, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationULongCnvtrFunc);

    if ((m_EquationValue.unsignedValue != ulongValue)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.unsignedValue = ulongValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_SFLOAT):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_SFLOAT  floatValue;

    m_equationSFloatCnvtrFunc->getValue(this, &floatValue, &stdQuality);
    if (std::isnan(m_EquationValue.floatValue) && !std::isnan(floatValue))
    {
      m_EquationValue.floatValue = floatValue;
    }
    CHECK_TIME_MODIFIED(m_equationSFloatCnvtrFunc);

    if ((!std::isnan(m_EquationValue.floatValue) && (m_EquationValue.floatValue != floatValue))
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      if (std::isnan(floatValue))
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "MDO: %s has NAN value\n", (const char*)this->GetFullName());
      }
      m_EquationValue.floatValue = floatValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_INT64):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_INT64   int64Value;

    m_equationInt64CnvtrFunc->getValue(this, &int64Value, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationInt64CnvtrFunc);

    if ((m_EquationValue.signed64bitIntValue != int64Value)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.signed64bitIntValue = int64Value;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_UINT64):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_UINT64   uint64Value;

    m_equationUInt64CnvtrFunc->getValue(this, &uint64Value, &stdQuality);
    CHECK_TIME_MODIFIED(m_equationUInt64CnvtrFunc);

    if ((m_EquationValue.unsigned64bitIntValue != uint64Value)
      || (m_equationStdQuality != stdQuality)
      || m_bUpdateIfNoChange
      || bTimeChanged)
    {
      m_EquationValue.unsigned64bitIntValue = uint64Value;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  case(GTWCNVTR_TYPE_DOUBLE):
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWTYPES_DOUBLE  doubleValue;

    //bool bTimeOk = m_equationDoubleCnvtrFunc->getTime(this, &timeValue, &stdQuality2);
    //CStdString s1, s2;
    //TRACE("eqndataobject::updatesdo1: prevGetValueTime=%s, newtime=%s\n", GetTMWDTIMEAsString(&preUpdateTime, s1), GetTMWDTIMEAsString(&timeValue, s2));

    m_equationDoubleCnvtrFunc->getValue(this, &doubleValue, &stdQuality);
    if (std::isnan(m_EquationValue.doubleValue) && !std::isnan(doubleValue))
    {
      m_EquationValue.doubleValue = doubleValue;
    }
    CHECK_TIME_MODIFIED(m_equationDoubleCnvtrFunc);

    if ((!std::isnan(m_EquationValue.doubleValue) && (m_EquationValue.doubleValue != doubleValue))
      || (m_equationStdQuality != stdQuality)
      || bTimeChanged
      || m_bUpdateIfNoChange)
    {
      if (std::isnan(doubleValue))
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "MDO: %s has NAN value\n", (const char*)this->GetFullName());
      }
      m_EquationValue.doubleValue = doubleValue;
      m_equationStdQuality = stdQuality;

      if ((m_iRecursionDepth > 1) && m_bUpdateIfNoChange)
      {
        this->m_pLastUpdateMdo = NULL;
        m_iRecursionDepth--;
        return;
      }
      updateMDO(updateReason);
    }
    break;
  }
  }
  this->m_pLastUpdateMdo = NULL;
  m_iRecursionDepth--;

  //TRACE("recursion depth: %d\n", m_iRecursionDepth);
  if (m_iRecursionDepth > 150)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_General, nullptr, "Warning: equation '%s:%s' recursion depth has exceeded 150, value=%d", this->GetFullName().c_str(), static_cast<const char *>(this->getEquationString()), m_iRecursionDepth);
#ifdef _WIN32
    assert(false);
#endif
  }
}

void GTWEquationDataObject::updateValue()
{
  TMWDTIME utcTime;
  GtwTimeZone::SetCurrentUtcTime(utcTime);
  SetUpdatedTime(&utcTime);
  SetReportedTime(&utcTime);

  GTWDEFS_STD_QLTY stdQuality;

  // Based on equation type, call the appropriate converter to calculate
  //  the current value of the equation
  switch (m_equationType)
  {
  case(GTWCNVTR_TYPE_TMWDTIME):
  {
    TMWDTIME timeValue;
    m_equationTimeCnvtrFunc->getValue(this, &timeValue, &stdQuality);
    m_EquationValue.timeValue = timeValue;
  }
  break;
  case(GTWCNVTR_TYPE_STRING):
  {
    CStdString strValue;
    m_equationStringCnvtrFunc->getValue(this, &m_equationStringValue, &stdQuality);
  }
  break;
  case(GTWCNVTR_TYPE_BOOL):
  {
    bool boolValue;
    m_equationBoolCnvtrFunc->getValue(this, &boolValue, &stdQuality);
    m_EquationValue.boolValue = boolValue;
  }
  break;
  case(GTWCNVTR_TYPE_CHAR):
  {
    TMWTYPES_CHAR charValue;
    m_equationCharCnvtrFunc->getValue(this, &charValue, &stdQuality);
    m_EquationValue.signedValue = charValue;
  }
  break;
  case(GTWCNVTR_TYPE_SHORT):
  {
    TMWTYPES_SHORT shortValue;
    m_equationShortCnvtrFunc->getValue(this, &shortValue, &stdQuality);
    m_EquationValue.signedValue = shortValue;
  }
  break;
  case(GTWCNVTR_TYPE_LONG):
  {
    TMWTYPES_LONG longValue;
    m_equationLongCnvtrFunc->getValue(this, &longValue, &stdQuality);
    m_EquationValue.signedValue = longValue;
  }
  break;
  case(GTWCNVTR_TYPE_UCHAR):
  {
    TMWTYPES_UCHAR ucharValue;
    m_equationUCharCnvtrFunc->getValue(this, &ucharValue, &stdQuality);
    m_EquationValue.unsignedValue = ucharValue;
  }
  break;
  case(GTWCNVTR_TYPE_USHORT):
  {
    TMWTYPES_USHORT ushortValue;
    m_equationUShortCnvtrFunc->getValue(this, &ushortValue, &stdQuality);
    m_EquationValue.unsignedValue = ushortValue;
  }
  break;
  case(GTWCNVTR_TYPE_ULONG):
  {
    TMWTYPES_ULONG ulongValue;
    m_equationULongCnvtrFunc->getValue(this, &ulongValue, &stdQuality);
    m_EquationValue.unsignedValue = ulongValue;
  }
  break;
  case(GTWCNVTR_TYPE_SFLOAT):
  {
    TMWTYPES_SFLOAT floatValue;
    m_equationSFloatCnvtrFunc->getValue(this, &floatValue, &stdQuality);
    m_EquationValue.floatValue = floatValue;
  }
  break;
  case(GTWCNVTR_TYPE_INT64):
  {
    TMWTYPES_INT64 int64Value;
    m_equationInt64CnvtrFunc->getValue(this, &int64Value, &stdQuality);
    m_EquationValue.signed64bitIntValue = int64Value;
  }
  break;
  case(GTWCNVTR_TYPE_DOUBLE):
  {
    TMWTYPES_DOUBLE  doubleValue;
    m_equationDoubleCnvtrFunc->getValue(this, &doubleValue, &stdQuality);
    m_EquationValue.doubleValue = doubleValue;
  }
  break;
  }
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

bool GTWEquationDataObject::parseTokens( /* returns TRUE if successful */
  GTWEQUAT_STATE             state,
  GTWSlaveDataObject                    *pUpdateSdo,
  GTWEquationArgument              *pReturnArgument,
  int                        precedenceOrder,
  TMWTOKEN_CHANNEL          *pEquationChannel,
  const char* connectionToken,
  GTWEquationDataObject *pEQOB)
{
  while(TMWDEFS_TRUE)
  {
    TMWTOKEN token;

    token = pEquationChannel->getToken(connectionToken,
                           operatorStrings,
                           TMWDEFS_ARRAY_LENGTH(operatorStrings),
                           TMWDEFS_FALSE /* do not ignore strings -- allow them */);

    switch(action[state][token.getType()])
    {
      case(GA_SAME):      break; /* no change in state */
      case(GA_WARN_CTRL):
        /* This action is for when a control character has been */
        /* detected. This action does not change the state.     */

        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_ILLEGAL_CHAR_IGNORED", "Illegal character '{{arg1}}'; character is ignored",(const char*)token);
        break;
      case(GA_UNEX_END):
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_UNEXPECTED_EQUATION", "Unexpected end of equation");
        return(TMWDEFS_FALSE);
      case(GA_UNEX_CMNT):
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_END_OF_COMMENT", "End of comment not found");
        return(TMWDEFS_FALSE);
      BAD_TOKEN:
      case(GA_BAD_TOKEN):
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_UNEXPCTED_TOKEN", "Unexpected token '{{arg1}}'",(const char *) token);
        return(TMWDEFS_FALSE);
      case(GA_OPERATOR):
        /* This action is for when an operator is detected when a symbol */
        /* is expected. The legal operators are the comment operator,    */
        /* left parenthesis, and unary operators.  A right parenthesis   */
        /* is also allowed when parsing argument-less functions.         */

        if (strcmp(token,"/*") == 0)
        {
          if (!parseTokens(GS_IN_COMMENT,
                                    TMWDEFS_NULL, /* no destination of result */
                                    TMWDEFS_NULL, /* no return argument */
                                    0,
                                    pEquationChannel,
                                    connectionToken,
                                    pEQOB))
          {
            return(TMWDEFS_FALSE); /* error was detected */
          }

          /* after comment, no state change */
        }
        else if ((strcmp(token,")") == 0) && (state == GS_IN_FUNCTION))
        {
          return(TMWDEFS_TRUE);
        }
        else if (strcmp(token,"(") == 0)
        {
          if (!parseTokens(GS_IN_PAR_PHRASE,
                                    pUpdateSdo,
                                    pReturnArgument,
                                    0,
                                    pEquationChannel,
                                    connectionToken,
                                    pEQOB))
          {
            return(TMWDEFS_FALSE); /* error was detected */
          }
 
          if (state == GS_IN_PAR_PHRASE)
          {
            state = GS_WTNG_FOR_RPAR;
          }
          else if (state == GS_START)
          {
            state = GS_WTNG_FOR_END;
          }
          else
          {
            return(TMWDEFS_TRUE);
          }
        }
        else 
        {
          /* TODO:  allow unary operators here */           
          goto BAD_TOKEN; /* all other operators are illegal */
        }
        break;
      case(GA_SYMBOL):
      {
        /* This action is for when a symbol is detected */

        bool funcFound = TMWDEFS_FALSE;

        /* first test if the symbol is one of function names. */
        /* If not, then perhaps this symbol is the beginning  */
        /* of a MDO identifier, or a literal constant number. */

        for (int i = 0; i < TMWDEFS_ARRAY_LENGTH(funcDescriptors); i++)
        {
          if (_stricmp(token,funcDescriptors[i]->pIdentifier)==0)
          {
            GTWEquationFunctionConverter *pFuncCnvtr = funcDescriptors[i]->pAllocCnvtr(pEQOB);

            if (pFuncCnvtr == TMWDEFS_NULL)
            {
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_MEMORY_ALLOCATION", "Could not allocate memory for '{{arg1}}'",(const char *) token);
              return(TMWDEFS_FALSE);
            }

            pReturnArgument->set(pFuncCnvtr);

            GTWEQUAT_STATE funcState = GS_WTNG_FOR_LPAR;
            
            while(1)
            {
              GTWEquationArgument funcArgument;
            
              if (!parseTokens(funcState,
                                        pUpdateSdo,
                                        &funcArgument,
                                        0,
                                        pEquationChannel,
                                        connectionToken,pEQOB))
              {
                funcArgument.Release();
                return(TMWDEFS_FALSE); /* error was detected */
              }

              /* We get to here after one or no arguments      */
              /* have been parsed and returned in funcArgument */

              if (funcArgument.argGetType() == GTWEquationArgument_TYPE_NONE)
              {
                if (!pFuncCnvtr->funcCheckNumArguments(connectionToken))
                {
                  funcArgument.Release();
                  return(TMWDEFS_FALSE); /* error was detected */
                }
                break;
              }

              if (!pFuncCnvtr->funcAddArgument(
                     pUpdateSdo,       /* destination of result of equation */
                     &funcArgument,     /* the argument to add */
                     connectionToken))
              {
                pEquationChannel->clearAnyPush();
                funcArgument.Release();
                return(TMWDEFS_FALSE);
              }

              funcState = GS_WTNG_FOR_COMMA; /* next state to wait for */
            }

            funcFound = TMWDEFS_TRUE;
            break;
          }
        }

        if (!funcFound)
        {
          /* must not be a function name, perhaps it is an MDO name */

          GTWMasterDataObject *pMdo;

          CStdString tempToken(token);
          if (GetGTWApp()->findMdo(tempToken, &pMdo) == GTWDEFS_STAT_SUCCESS)
          {
            pReturnArgument->set(pMdo);
          }
          else
          { // must ba a constant
            TMWTYPES_INT64      signed64bitInt=0;
            double              doubleValue=0.0;
            char                stringValue[1024];
            TMWTYPES_UINT      unsignedValue=0;
            TMWTYPES_INT       signedValue=0;
            GTWEquationConstant *pConstant;
            char                tempChar; // if tempChar is scanned, then we  know we don't have a good syntax

            if (sscanf(token, "0x%x%c", &unsignedValue,&tempChar) == 1)
            {
              pConstant = new GTWEquationConstantDouble(double(unsignedValue));
            }
            else if (sscanf(token, "0X%x%c", &unsignedValue,&tempChar) == 1)
            {
              pConstant = new GTWEquationConstantDouble(double(unsignedValue));
            }
            else if (sscanf(token, LL, &signed64bitInt, &tempChar) == 1)
            {
              pConstant = new GTWEquationConstantInt64(signed64bitInt);
            }
            else if (sscanf(token, "%d%c", &signedValue,&tempChar) == 1)
            {
              pConstant = new GTWEquationConstantDouble(double(signedValue));
            }
            else if (sscanf(token, "%lf%c", &doubleValue,&tempChar) == 1)
            {
              pConstant = new GTWEquationConstantDouble(doubleValue);
            }
            else if (sscanf(token, "%s", (char*)stringValue) == 1)
            {
              pConstant = new GTWEquationConstantString(stringValue);
            }
            else
            {
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_UNKONWN_IDENTIFIER", "Unknown identifier '{{arg1}}'",(const char *) token);
              return(TMWDEFS_FALSE);
            }

            if (pConstant == TMWDEFS_NULL)
            {
              GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_ALLOC_MEMORY", "Could not allocate memory for '{{arg1}}'",(const char *) token);
              return(TMWDEFS_FALSE);
            }

            pReturnArgument->set(pConstant);
          }
        }

        if (!parseTokens(GS_WTNG_FOR_OPER,
                                  pUpdateSdo,
                                  pReturnArgument,
                                  precedenceOrder,
                                  pEquationChannel,
                                  connectionToken,pEQOB))
        {
          pReturnArgument->Release();
          return(TMWDEFS_FALSE); /* error was detected */
        }

        if (state == GS_IN_PAR_PHRASE)
        {
          state = GS_WTNG_FOR_RPAR;
        }
        else if (state == GS_START)
        {
          state = GS_WTNG_FOR_END;
        }
        else
        {
          return(TMWDEFS_TRUE);
        }

        break;
      }
      case(GA_STRING):
        /* TODO - support strings as stand alone values, arguments to functions, or arguments to operators */
        //pReportError(pReportErrorParam,"Strings are not supported (%s)",(const char *) token);
        return(TMWDEFS_TRUE);
      case(GA_CMNT_END):
        /* This action is for when inside a comment, operator tokens */
        /* must be compared with the comment ending operator.        */

        /* If end of comment is found, then we must have */
        /* been called recursively when the beginning of */
        /* the comment was found; therefore, we need to  */
        /* return. This will restore the GTWEQUAT_STATE. */

        if (strcmp(token,"*/") == 0)
        {
          return(TMWDEFS_TRUE); /* successful, no errors detected */
        }
        break;
      case(GA_OPER_TEST):
        /* This action is for when we detect an operator after a symbol */

        if (strcmp(token,"/*") == 0)
        {
          if (!parseTokens(GS_IN_COMMENT,
                                    TMWDEFS_NULL, /* no destination of result */
                                    TMWDEFS_NULL, /* no active function converter */
                                    0,
                                    pEquationChannel,
                                    connectionToken,pEQOB))
          {
            return(TMWDEFS_FALSE); /* error was detected */
          }
        }
        else if ((strcmp(token,"(") == 0) && (state == GS_WTNG_FOR_LPAR))
        {
          state = GS_IN_FUNCTION;
        }
        else if (strcmp(token,")") == 0) 
        {
          if (state == GS_WTNG_FOR_COMMA)
          {
            return(TMWDEFS_TRUE); /* this marks the end of a function argument list */
          }
          else if (state == GS_WTNG_FOR_RPAR)
          {
            state = GS_WTNG_FOR_OPER; /* finish expression after phrase */
          }
          else if (state == GS_WTNG_FOR_OPER)
          {
            /* this operator signifies the end of an argument or */
            /* of a parenthetical phrase; it must be processed   */
            /* by a recursive parent state, not this state       */
            pEquationChannel->push(token);

            return(TMWDEFS_TRUE);
          }
          else
          {
            goto BAD_TOKEN; /* right paren in all other states is illegal */
          }
        }
        else if (strcmp(token,",") == 0)
        {
          if (state == GS_WTNG_FOR_COMMA)
          {
            state = GS_AFTER_COMMA;
          }
          else if (state == GS_WTNG_FOR_OPER)
          {
            /* this operator signifies the end of an argument; it must  */
            /* be processed by a recursive parent state, not this state */
            pEquationChannel->push(token);

            return(TMWDEFS_TRUE);
          }
          else
          {
            goto BAD_TOKEN; /* comma in all other states is illegal */
          }
        }
        else if (state == GS_WTNG_FOR_OPER)
        {
          bool operatorFound = TMWDEFS_FALSE;

          for (int i = 0; i < TMWDEFS_ARRAY_LENGTH(operatorFunctions); i++)
          {
            if (_stricmp(token, operatorFunctions[i].pIdentifier)==0)
            {
              if (operatorFunctions[i].precedenceOrder < precedenceOrder)
              {
                /* If we get to here, a previous operator has a higher       */
                /* precedence order; therefore, push this operator and       */
                /* return so that the previous symbol can be operated upon   */
                /* by the previous operator.  Only after that will this      */
                /* operator be processed.                                    */
                pEquationChannel->push(token);

                return(TMWDEFS_TRUE);
              }

              /* If we get to here, then the current operator has a higher   */
              /* precedence order than any previous operator.  In this case  */
              /* we should fully evaluate this operator before returning an  */
              /* argument to the previous operator                           */

              GTWEquationFunctionConverter *pFuncCnvtr = operatorFunctions[i].pFuncDescriptpr->pAllocCnvtr(pEQOB);

              if (pFuncCnvtr == TMWDEFS_NULL)
              {
                GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_ALLOC_MEMORY", "Could not allocate memory for '{{arg1}}'",(const char *) token);
                return(TMWDEFS_FALSE);
              }

              if (!pFuncCnvtr->funcAddArgument(
                     pUpdateSdo,        /* destination of result of equation */
                     pReturnArgument,   /* the argument to add */
                     connectionToken))
              {
                pReturnArgument->Release();
                return(TMWDEFS_FALSE);
              }

              pReturnArgument->set(pFuncCnvtr);

              GTWEquationArgument funcArgument;
            
              if (!parseTokens(GS_IN_OPER_EXPR,
                                        pUpdateSdo,
                                        &funcArgument,
                                        operatorFunctions[i].precedenceOrder,
                                        pEquationChannel,
                                        connectionToken,pEQOB))
              {
                funcArgument.Release();
                return(TMWDEFS_FALSE); /* error was detected */
              }

              /* We get to here after an argument */
              /* to the operator has been parsed  */

              if (!pFuncCnvtr->funcAddArgument(
                     pUpdateSdo,        /* destination of result of equation */
                     &funcArgument,     /* the argument to add */
                     connectionToken))
              {
                pEquationChannel->clearAnyPush();
                funcArgument.Release();
                return(TMWDEFS_FALSE);
              }

              operatorFound = TMWDEFS_TRUE;
              break;
            }
          }
     
          if (!operatorFound)
          {
            goto BAD_TOKEN; /* all other operators are illegal */
          }
        }
        else
        {
          goto BAD_TOKEN; /* all other operators in all other states are illegal */
        }
        break;
      case(GA_END):
        return TMWDEFS_TRUE;
    }
  }
} /* end parseTokens() */

bool GTWEquationDataObject::HasArgument(GTWMasterDataObject * pMdoOb)
{
  bool bHasArg = false;
  if (m_equationTimeCnvtrFunc)
  {
    bHasArg |= m_equationTimeCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationStringCnvtrFunc)
  {
    bHasArg |=  m_equationStringCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationBoolCnvtrFunc)
  {
    bHasArg |=  m_equationBoolCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationCharCnvtrFunc)
  {
    bHasArg |=  m_equationCharCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationShortCnvtrFunc)
  {
    bHasArg |=  m_equationShortCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationLongCnvtrFunc)
  {
    bHasArg |=  m_equationLongCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationUCharCnvtrFunc)
  {
    bHasArg |=  m_equationUCharCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationUShortCnvtrFunc)
  {
    bHasArg |=  m_equationUShortCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationULongCnvtrFunc)
  {
    bHasArg |=  m_equationULongCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationSFloatCnvtrFunc)
  {
    bHasArg |=  m_equationSFloatCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationDoubleCnvtrFunc)
  {
	  bHasArg |=  m_equationDoubleCnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationInt64CnvtrFunc)
  {
    bHasArg |=  m_equationInt64CnvtrFunc->HasArgument(pMdoOb);
  }
  if (m_equationUInt64CnvtrFunc)
  {
    bHasArg |= m_equationUInt64CnvtrFunc->HasArgument(pMdoOb);
  }
  return bHasArg;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_STAT GTWEquationDataObject::createMdo(              /* creates calculation MDO     */
  const char              *pUserTagName,      /* name to assign to MDO (could be blank)      */
  const char                *pEquationString, /* calculation                 */
  GTWMasterDataObject                   **ppMdo,           /* place to store pointer to created MDO  */
  const char* connectionToken,
  bool bDoInitialUpdate,
  bool bInsertIntoCollection
  )
{
  GTWEquationDataObject *pEquateMdoSdo = new GTWEquationDataObject(pUserTagName,pEquationString);
  GTWEquationArgument equationArg;

  if (pEquateMdoSdo == TMWDEFS_NULL)
  {
    return(GTWDEFS_STAT_OUT_OF_MEMORY);
  }

  CStdString           equationStrobj(pEquationString);
  STRMIO_CHANNEL   equationStream(&equationStrobj);

  if (TMWTOKEN_CHANNEL equationChannel(&equationStream); !parseTokens(GS_START,
                            pEquateMdoSdo,   /* destination of result */
                            &equationArg,    /* base active function converter */
                            0,               /* precedence order */
                            &equationChannel,
                            connectionToken,pEquateMdoSdo))
  {
    equationArg.Release();
    delete pEquateMdoSdo; 
    return(GTWDEFS_STAT_NOT_VALID);
  }

  if (pEquateMdoSdo->CreateConverters(&equationArg, connectionToken))
  {
    // evaluate equation for the first time, and set update time
    if (bDoInitialUpdate == true)
    {
      pEquateMdoSdo->updateSDO(GTWDEFS_UPDTRSN_REQUESTED, pEquateMdoSdo);
    }

    if (bInsertIntoCollection == true)
      GetGTWApp()->getMdoCollection()->InsertCollectionMember(pEquateMdoSdo);
    *ppMdo = pEquateMdoSdo;
    return (GTWDEFS_STAT_SUCCESS);
  }

  equationArg.Release();
  delete pEquateMdoSdo;

  /*
  // if we want to assign the top level "function" to the SDO - not for now
  //pEquateMdoSdo->m_pFunc = equationArg.argGetFunc();

  static const GTWCNVTR_TYPE typesToTry[] = {
    GTWCNVTR_TYPE_INT64,
    GTWCNVTR_TYPE_DOUBLE,
    GTWCNVTR_TYPE_SFLOAT,
    GTWCNVTR_TYPE_LONG,
    GTWCNVTR_TYPE_ULONG,
    GTWCNVTR_TYPE_SHORT,
    GTWCNVTR_TYPE_USHORT,
    GTWCNVTR_TYPE_CHAR,
    GTWCNVTR_TYPE_UCHAR,
    GTWCNVTR_TYPE_BOOL,
    GTWCNVTR_TYPE_STRING,
    GTWCNVTR_TYPE_TMWDTIME,
  };

  for (int i = 0; i < TMWDEFS_ARRAY_LENGTH(typesToTry); i++)
  {
    GTWCNVTR_TYPE type = typesToTry[i];
    bool bCnvtrSet = TMWDEFS_FALSE;

    switch (type)
    {
      case GTWCNVTR_TYPE_STRING:
        {
          GTWReadConverterTemplate<CStdString>  *cnvtrFunc = (GTWReadConverterTemplate<CStdString> *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_BOOL:
        {
          GTWReadConverterTemplate<bool>  *cnvtrFunc = (GTWReadConverterTemplate<bool> *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_CHAR:
        {
          GTWReadConverterTemplate<TMWTYPES_CHAR>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_CHAR>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_SHORT:
        {
          GTWReadConverterTemplate<TMWTYPES_SHORT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_SHORT>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_LONG:
        {
          GTWReadConverterTemplate<TMWTYPES_INT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_INT>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_UCHAR:
        {
          GTWReadConverterTemplate<TMWTYPES_UCHAR>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_UCHAR>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_USHORT:
        {
          GTWReadConverterTemplate<TMWTYPES_USHORT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_USHORT>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_ULONG:
        {
          GTWReadConverterTemplate<TMWTYPES_UINT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_UINT>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_SFLOAT:
        {
          GTWReadConverterTemplate<TMWTYPES_SFLOAT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_SFLOAT>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_DOUBLE:
        {
          GTWReadConverterTemplate<TMWTYPES_DOUBLE>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_DOUBLE>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
      case GTWCNVTR_TYPE_INT64:
        {
          GTWReadConverterTemplate<TMWTYPES_INT64>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_INT64>  *)equationArg.argGetCnvtr(type, pEquateMdoSdo, "", TMWDEFS_NULL, TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
      break;
      case GTWCNVTR_TYPE_TMWDTIME:
        {
          GTWReadConverterTemplate<TMWDTIME>  *cnvtrFunc = (GTWReadConverterTemplate<TMWDTIME>  *)equationArg.argGetCnvtr(type,pEquateMdoSdo,"",TMWDEFS_NULL,TMWDEFS_NULL);
          if (cnvtrFunc != TMWDEFS_NULL)
          {
            pEquateMdoSdo->setCnvtr(cnvtrFunc, type);
            bCnvtrSet = TMWDEFS_TRUE;
          }
        }
        break;
    }

    if (bCnvtrSet == TMWDEFS_TRUE)
    {
      / evaluate equation for the first time, and set update time /
      if (bDoInitialUpdate == true)
      {
        pEquateMdoSdo->updateSDO(GTWDEFS_UPDTRSN_REQUESTED,pEquateMdoSdo);
      }
    
      if (bInsertIntoCollection == true)
        GetGTWApp()->getMdoCollection()->InsertCollectionMember(pEquateMdoSdo);
      *ppMdo = pEquateMdoSdo;
      return (GTWDEFS_STAT_SUCCESS);
    }
  }
  */

GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Equation, connectionToken, "TR_EQUATION_PRODUCES_BAD_TYPE", "Equation produces type that cannot be used");

  return(GTWDEFS_STAT_NOT_VALID);
}

bool GTWEquationDataObject::modifyEquation(const char *pUserTagName,
  const char                *pEquationString,
  const char                *connectionToken)
{
  //
  // First make sure equation changes are valid 
  //
  if (!testModify(pUserTagName, pEquationString, connectionToken))
  {
    return false;
  }

  //
  // Now change real equation
  //
#pragma message(__LOC__ "TODO: check for uniqueness")
  m_name = pUserTagName; // TODO: check for uniqueness
  m_sEquationString = pEquationString;
  DeleteConverters();

  GTWEquationArgument equationArg;

  CStdString           equationStrobj(pEquationString);
  STRMIO_CHANNEL   equationStream(&equationStrobj);
  TMWTOKEN_CHANNEL equationChannel(&equationStream);

  if (!parseTokens(GS_START,
    this,   /* destination of result */
    &equationArg,    /* base active function converter */
    0,               /* precedence order */
    &equationChannel,
    connectionToken, this))
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_General, nullptr, "%s", "GTWEquationDataObject::modifyEquation:assert(false)");
#ifdef _WIN32
    assert(false);
#endif
    return false;
  }

  if (CreateConverters(&equationArg, connectionToken))
  {
    updateSDO(GTWDEFS_UPDTRSN_REQUESTED, this);
    return true;
  }

  LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_General, nullptr, "%s", "GTWEquationDataObject::modifyEquation:assert(false)");
#ifdef _WIN32
  assert(false);
#endif

  return false;
}

bool GTWEquationDataObject::testModify(const char *pUserTagName,
                                       const char  *pEquationString,
                                       const char* connectionToken)
{
  GTWEquationDataObject tempEquationDO(GTWEquationDataObject::getValidateEquationName(), pEquationString);
  GTWEquationArgument equationArg;

  CStdString           equationStrobj(pEquationString);
  STRMIO_CHANNEL   equationStream(&equationStrobj);
  TMWTOKEN_CHANNEL equationChannel(&equationStream);

  if (!parseTokens(GS_START,
    &tempEquationDO,   /* destination of result */
    &equationArg,    /* base active function converter */
    0,               /* precedence order */
    &equationChannel,
    connectionToken, &tempEquationDO))
  {
    return false;
  }
  if (!tempEquationDO.CreateConverters(&equationArg, connectionToken))
  {
    return false;
  }

  return true;
}

bool GTWEquationDataObject::CreateConverters(GTWEquationArgument *pEquationArg, const char* connectionToken)
{
  static const GTWCNVTR_TYPE typesToTry[] =
  {
    GTWCNVTR_TYPE_DOUBLE,
    GTWCNVTR_TYPE_SFLOAT,
    GTWCNVTR_TYPE_LONG,
    GTWCNVTR_TYPE_INT64,
    GTWCNVTR_TYPE_UINT64,
    GTWCNVTR_TYPE_ULONG,
    GTWCNVTR_TYPE_SHORT,
    GTWCNVTR_TYPE_USHORT,
    GTWCNVTR_TYPE_CHAR,
    GTWCNVTR_TYPE_UCHAR,
    GTWCNVTR_TYPE_BOOL,
    GTWCNVTR_TYPE_STRING,
    GTWCNVTR_TYPE_TMWDTIME,
  };

  for (int i = 0; i < TMWDEFS_ARRAY_LENGTH(typesToTry); i++)
  {
    GTWCNVTR_TYPE type = typesToTry[i];

    switch (type)
    {
    case GTWCNVTR_TYPE_STRING:
    {
      GTWReadConverterTemplate<CStdString>  *cnvtrFunc = (GTWReadConverterTemplate<CStdString> *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_BOOL:
    {
      GTWReadConverterTemplate<bool>  *cnvtrFunc = (GTWReadConverterTemplate<bool> *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_CHAR:
    {
      GTWReadConverterTemplate<TMWTYPES_CHAR>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_CHAR>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_SHORT:
    {
      GTWReadConverterTemplate<TMWTYPES_SHORT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_SHORT>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_LONG:
    {
      GTWReadConverterTemplate<TMWTYPES_INT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_INT>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_INT64:
    {
      GTWReadConverterTemplate<int64_t>  *cnvtrFunc = (GTWReadConverterTemplate<int64_t>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_UINT64:
    {
      GTWReadConverterTemplate<uint64_t>* cnvtrFunc = (GTWReadConverterTemplate<uint64_t>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_UCHAR:
    {
      GTWReadConverterTemplate<TMWTYPES_UCHAR>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_UCHAR>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_USHORT:
    {
      GTWReadConverterTemplate<TMWTYPES_USHORT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_USHORT>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_ULONG:
    {
      GTWReadConverterTemplate<TMWTYPES_UINT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_UINT>  *)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_SFLOAT:
    {
      GTWReadConverterTemplate<TMWTYPES_SFLOAT>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_SFLOAT>*)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_DOUBLE:
    {
      GTWReadConverterTemplate<TMWTYPES_DOUBLE>  *cnvtrFunc = (GTWReadConverterTemplate<TMWTYPES_DOUBLE>*)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    case GTWCNVTR_TYPE_TMWDTIME:
    {
      GTWReadConverterTemplate<TMWDTIME>  *cnvtrFunc = (GTWReadConverterTemplate<TMWDTIME>*)pEquationArg->argGetCnvtr(type, this, "", connectionToken, false);
      if (cnvtrFunc != TMWDEFS_NULL)
      {
        setCnvtr(cnvtrFunc, type);
        return true;
      }
    }
    break;
    }
  }

  return false;
}

/*****************************************************************************/
/*                                                                           */
/*                            END OF GTWequat.cpp                            */
/*                                                                           */
/*****************************************************************************/

void GTWEquationDataObject::getValueString(const GTWEXPND_FORMAT &pFormat, TMWTYPES_CHAR *msg, TMWTYPES_USHORT msgLen)
{
  switch(m_equationType)
  {
    case(GTWCNVTR_TYPE_BOOL):
      tmwtarg_snprintf(msg, msgLen, pFormat.stringFormat,m_EquationValue.boolValue?"On":"Off");
      break;
    case(GTWCNVTR_TYPE_CHAR):
    case(GTWCNVTR_TYPE_SHORT):
    case(GTWCNVTR_TYPE_LONG):
      tmwtarg_snprintf(msg, msgLen, pFormat.signedFormat, m_EquationValue.signedValue);
      break;
    case(GTWCNVTR_TYPE_UCHAR):
    case(GTWCNVTR_TYPE_USHORT):
    case(GTWCNVTR_TYPE_ULONG):
      tmwtarg_snprintf(msg, msgLen, pFormat.unsignedFormat, m_EquationValue.unsignedValue);
      break;
    case(GTWCNVTR_TYPE_SFLOAT):
      tmwtarg_snprintf(msg, msgLen, pFormat.floatFormat, m_EquationValue.floatValue);
      break;
    case(GTWCNVTR_TYPE_INT64):
      tmwtarg_snprintf(msg, msgLen, pFormat.signed64bitIntFormat, m_EquationValue.signed64bitIntValue);
      break;
    case(GTWCNVTR_TYPE_UINT64):
      tmwtarg_snprintf(msg, msgLen, pFormat.unsigned64bitIntFormat, m_EquationValue.unsigned64bitIntValue);
      break;
    case(GTWCNVTR_TYPE_DOUBLE):
	    tmwtarg_snprintf(msg, msgLen, pFormat.floatFormat, m_EquationValue.doubleValue);
      break;
    default:
	    tmwtarg_snprintf(msg, msgLen, pFormat.floatFormat, m_EquationValue.doubleValue);
      break;
  }
}

GTWDEFS_STD_QLTY GTWEquationDataObject::getStdQuality()
{
  return m_equationStdQuality;
}

GTWDEFS_TYPE GTWEquationDataObject::getMdoType(void)
{
  switch (m_equationType)
  {
  case GTWCNVTR_TYPE_STRING:
    return GTWDEFS_TYPE_STRING;
    break;
  case GTWCNVTR_TYPE_TMWDTIME:
    return GTWDEFS_TYPE_TIME;
    break;
  case GTWCNVTR_TYPE_INT64:
    return GTWDEFS_TYPE_INT64;
    break;
  case GTWCNVTR_TYPE_UINT64:
    return GTWDEFS_TYPE_UINT64;
    break;
  case GTWCNVTR_TYPE_DOUBLE:
    return GTWDEFS_TYPE_DOUBLE;
    break;
  case GTWCNVTR_TYPE_SFLOAT:
    return GTWDEFS_TYPE_SFLOAT;
    break;
  case GTWCNVTR_TYPE_LONG:
    return GTWDEFS_TYPE_LONG;
    break;
  case GTWCNVTR_TYPE_ULONG:
    return GTWDEFS_TYPE_ULONG;
    break;
  case GTWCNVTR_TYPE_SHORT:
    return GTWDEFS_TYPE_SHORT;
    break;
  case GTWCNVTR_TYPE_CHAR:
    return GTWDEFS_TYPE_CHAR;
    break;
  case GTWCNVTR_TYPE_USHORT:
    return GTWDEFS_TYPE_USHORT;
    break;
  case GTWCNVTR_TYPE_UCHAR:
    return GTWDEFS_TYPE_UCHAR;
    break;
  case GTWCNVTR_TYPE_BOOL:
    return GTWDEFS_TYPE_BOOL;
    break;
  }
  LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_General, nullptr, "%s", "GTWEquationDataObject::getMDOType:assert(false)");
#ifdef _WIN32
  assert(false);
#endif

  return GTWDEFS_TYPE_UNKNOWN;
}
  


