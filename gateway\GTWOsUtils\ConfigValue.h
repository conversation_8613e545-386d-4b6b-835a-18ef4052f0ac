#pragma once

#include <string>
#include <stdexcept>
#include <functional>
#include "gateway/GTWOsUtils/filesystem.hpp"
#include <optional>
#include <regex>
#include "GTWOsUtilsDll.h"
#include "gateway/GTWWebLib/json.hpp"
#include "GtwLogger.h"
#include "StdString.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#endif

enum class PathType {
  File,
  Directory,
  Either
};

enum class HostType {
  Any,
  IPv4Only,
  IPv6Only,
  NameOnly
};

enum class APP_TYPE {
  APP_NOTSET,
  APP_MON,
  APP_ENGINE,
  APP_RED,
  APP_SETTINGS
};

typedef enum {
  VALIDATE_RESULT_NOT_DONE,
  VALIDATE_RESULT_SUCCESS,
  VALIDATE_RESULT_WARNING,
  VALIDATE_RESULT_ERROR,
  VALIDATE_RESULT_FAIL
} VALIDATE_RESULT_ENUM;


template<typename T>
class GTWOSUTILS_API ConfigValue {
public:
  virtual ~ConfigValue() = default;

  virtual T& get() {
    if (m_workspaceOverridable && m_workspaceValue.has_value()) {
      return m_workspaceValue.value();
    }
    return m_value;
  }

  virtual const T& get() const {
    if (m_workspaceOverridable && m_workspaceValue.has_value()) {
      return m_workspaceValue.value();
    }
    return m_value;
  }

  virtual void set(const T& value) {
    T oldValue = get();
    validate(value);
    if (m_workspaceOverridable) {
      m_workspaceValue = value;
    }
    else {
      m_value = value;
    }
    onChanged(oldValue, value);
  }

  virtual void setWorkspaceValue(const T& value) {
    if (!m_workspaceOverridable) {
      throw std::runtime_error("Config value " + name() + " is not workspace overridable");
    }
    T oldValue = get();
    validate(value);
    m_workspaceValue = value;
    onChanged(oldValue, value);
  }

  virtual void clearWorkspaceValue() {
    if (m_workspaceValue.has_value()) {
      T oldValue = get();
      m_workspaceValue.reset();
      onChanged(oldValue, get());
    }
  }

  virtual void reset() {
    set(getDefault());
    m_workspaceValue.reset();
  }

  virtual std::string name() const = 0;

  bool isWorkspaceOverridable() const { return m_workspaceOverridable; }
  bool hasWorkspaceValue() const { return m_workspaceValue.has_value(); }

  virtual void toJson(nlohmann::json& globalJson, nlohmann::json& workspaceJson) const {
    globalJson[name()] = m_value;
    if (m_workspaceOverridable && m_workspaceValue.has_value()) {
      workspaceJson[name()] = m_workspaceValue.value();
    }
  }

  virtual void fromJson(const nlohmann::json& globalJson, const nlohmann::json& workspaceJson) {
    if (globalJson.contains(name())) {
      set(globalJson[name()].template get<T>());
    }
    if (m_workspaceOverridable && workspaceJson.contains(name())) {
      m_workspaceValue = workspaceJson[name()].template get<T>();
    }
  }

protected:
  ConfigValue(bool workspaceOverridable = false)
    : m_workspaceOverridable(workspaceOverridable) {}

  virtual void validate(const T& value) {}
  virtual void onChanged(const T& oldValue, const T& newValue) {}
  virtual T getDefault() const = 0;

  T m_value;
  bool m_workspaceOverridable;
  std::optional<T> m_workspaceValue;
};

class GTWOSUTILS_API RangeConfigValue : public ConfigValue<int> {
public:
  RangeConfigValue(int defaultValue, int minValue, int maxValue, bool workspaceOverridable = false)
    : ConfigValue<int>(workspaceOverridable),
    m_min(minValue),
    m_max(maxValue),
    m_defaultValue(defaultValue) {
    m_value = defaultValue;
  }

  RangeConfigValue(const std::string& name, int defaultValue, int minValue, int maxValue, bool workspaceOverridable = false)
    : ConfigValue<int>(workspaceOverridable),
    m_name(name),
    m_min(minValue),
    m_max(maxValue),
    m_defaultValue(defaultValue) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const int& value) override {
    if (value < m_min || value > m_max) {
      throw std::runtime_error("Value out of range for " + m_name);
    }
  }
  int getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  int m_min;
  int m_max;
  int m_defaultValue;
};

class GTWOSUTILS_API DoubleRangeConfigValue : public ConfigValue<double> {
public:
  DoubleRangeConfigValue(double defaultValue, double minValue, double maxValue, bool workspaceOverridable = false)
    : ConfigValue<double>(workspaceOverridable),
    m_min(minValue),
    m_max(maxValue),
    m_defaultValue(defaultValue) {
    m_value = defaultValue;
  }

  DoubleRangeConfigValue(const std::string& name, double defaultValue, double minValue, double maxValue, bool workspaceOverridable = false)
    : ConfigValue<double>(workspaceOverridable),
    m_name(name),
    m_min(minValue),
    m_max(maxValue),
    m_defaultValue(defaultValue) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const double& value) override {
    if (value < m_min || value > m_max) {
      throw std::runtime_error("Value out of range for " + m_name);
    }
  }
  double getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  double m_min;
  double m_max;
  double m_defaultValue;
};

class GTWOSUTILS_API PathConfigValue : public ConfigValue<std::string> {
public:
  PathConfigValue(PathType type = PathType::Either, bool mustExist = false, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_pathType(type),
    m_mustExist(mustExist) {
    m_value = "";
  }

  PathConfigValue(const std::string& name, PathType type = PathType::Either, bool mustExist = false, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_name(name),
    m_pathType(type),
    m_mustExist(mustExist) {
    m_value = "";
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const std::string& value) override {
    if (value.empty()) {
      if (m_mustExist) {
        throw std::runtime_error("Path cannot be empty for " + name());
      }
      return;
    }

    try {
      std::filesystem::path path(value);
      if (m_mustExist && !std::filesystem::exists(path)) {
        throw std::runtime_error("Path does not exist: " + value);
      }

      if (m_pathType == PathType::File && std::filesystem::is_directory(path)) {
        throw std::runtime_error("Expected file but got directory: " + value);
      }

      if (m_pathType == PathType::Directory && !std::filesystem::is_directory(path)) {
        throw std::runtime_error("Expected directory but got file: " + value);
      }
    }
    catch (const std::filesystem::filesystem_error& e) {
      throw std::runtime_error("Invalid path: " + std::string(e.what()));
    }
  }

  void onChanged(const std::string& oldValue, const std::string& newValue) override {
    auto tempValue = get();
    decodeFilePath(tempValue);
    if (!validatePath()) {
      throw std::runtime_error("Path validation failed for " + name());
    }
  }

  void decodeFilePath(std::string& path) {
    CStdString s = path;
    s.Replace("%3A", ":");
    s.Replace("%5C", "\\");
    s.Replace("%2F", "/");
    path = s;
  }

  virtual bool validatePath() const { return true; }
  std::string getDefault() const override { return ""; }

private:
  std::string m_name;
  PathType m_pathType;
  bool m_mustExist;
};

class GTWOSUTILS_API BoolConfigValue : public ConfigValue<bool> {
public:
  BoolConfigValue(const std::string& name, bool defaultValue, bool workspaceOverridable = false)
    : ConfigValue<bool>(workspaceOverridable),
    m_name(name),
    m_defaultValue(defaultValue) {
    m_value = defaultValue;
  }

  BoolConfigValue(const std::string& name, bool defaultValue, std::function<bool(bool)> constraint, bool workspaceOverridable = false)
    : ConfigValue<bool>(workspaceOverridable),
    m_name(name),
    m_defaultValue(defaultValue),
    m_constraint(constraint) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const bool& value) override {
    if (m_constraint && !m_constraint(value)) {
      throw std::runtime_error("Invalid boolean value for " + m_name);
    }
  }
  bool getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  bool m_defaultValue;
  std::function<bool(bool)> m_constraint;
};

class GTWOSUTILS_API HostConfigValue : public ConfigValue<std::string> {
public:
  HostConfigValue(HostType type = HostType::Any, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_hostType(type) {
    m_value = "";
  }

  HostConfigValue(const std::string& name, HostType type = HostType::Any, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_name(name),
    m_hostType(type) {
    m_value = "";
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const std::string& value) override {
    if (value.empty()) {
      throw std::runtime_error("Host value cannot be empty for " + name());
    }

    switch (m_hostType) {
    case HostType::IPv4Only:
      if (!validateIPv4(value)) {
        throw std::runtime_error("Invalid IPv4 address: " + value);
      }
      break;
    case HostType::IPv6Only:
      if (!validateIPv6(value)) {
        throw std::runtime_error("Invalid IPv6 address: " + value);
      }
      break;
    case HostType::NameOnly:
      if (!validateHostname(value)) {
        throw std::runtime_error("Invalid hostname: " + value);
      }
      break;
    case HostType::Any:
      if (!validateIPv4(value) && !validateIPv6(value) && !validateHostname(value)) {
        throw std::runtime_error("Invalid host value: " + value);
      }
      break;
    }
  }

  void onChanged(const std::string& oldValue, const std::string& newValue) override {
    if (newValue == "localhost") {
      m_value = "127.0.0.1";
    }
  }

  bool validateIPv4(const std::string& addr) const {
    struct sockaddr_in sa;
    return inet_pton(AF_INET, addr.c_str(), &(sa.sin_addr)) == 1;
  }

  bool validateIPv6(const std::string& addr) const {
    struct sockaddr_in6 sa;
    return inet_pton(AF_INET6, addr.c_str(), &(sa.sin6_addr)) == 1;
  }

  bool validateHostname(const std::string& name) const {
    std::regex hostnameRegex(
      "^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])"
      "(\\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9]))*$"
    );
    return std::regex_match(name, hostnameRegex);
  }

  std::string getDefault() const override { return ""; }

private:
  std::string m_name;
  HostType m_hostType;
};

class GTWOSUTILS_API PortConfigValue : public ConfigValue<int> {
public:
  PortConfigValue(bool checkInUse = false, bool workspaceOverridable = false)
    : ConfigValue<int>(workspaceOverridable),
    m_checkInUse(checkInUse) {
    m_value = 0;
  }

  PortConfigValue(const std::string& name, bool checkInUse = false, bool workspaceOverridable = false)
    : ConfigValue<int>(workspaceOverridable),
    m_name(name),
    m_checkInUse(checkInUse) {
    m_value = 0;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const int& value) override {
    if (value <= 0 || value > 65535) {
      throw std::runtime_error("Invalid port number " + std::to_string(value) +
        " for " + name());
    }
  }

  void onChanged(const int& oldValue, const int& newValue) override {
    if (oldValue != newValue) {
      //LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_General, nullptr,
      //  "Port changed from %d to %d for %s", oldValue, newValue, name().c_str());
    }
  }

  bool isPortInUse(int port) const;

  int getDefault() const override { return 0; }

private:
  std::string m_name;
  bool m_checkInUse;
};

class GTWOSUTILS_API SizeConfigValue : public ConfigValue<std::size_t> {
public:
  SizeConfigValue(const std::string& name, std::size_t defaultValue = 100,
    std::size_t minValue = 1, std::size_t maxValue = 10000, bool workspaceOverridable = false)
    : ConfigValue<std::size_t>(workspaceOverridable),
    m_name(name),
    m_defaultValue(defaultValue),
    m_min(minValue),
    m_max(maxValue) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const std::size_t& value) override {
    if (value < m_min || value > m_max) {
      throw std::runtime_error("Value out of range for " + m_name);
    }
  }
  std::size_t getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  std::size_t m_defaultValue;
  std::size_t m_min;
  std::size_t m_max;
};

class GTWOSUTILS_API StringConfigValue : public ConfigValue<std::string> {
public:
  StringConfigValue(const std::string& name, const std::string& defaultValue = "",
    size_t maxLength = 2048, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_name(name),
    m_defaultValue(defaultValue),
    m_maxLength(maxLength) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const std::string& value) override {
    if (value.length() > m_maxLength) {
      throw std::runtime_error("String too long for " + m_name);
    }
  }
  std::string getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  std::string m_defaultValue;
  size_t m_maxLength;
};

template<typename EnumT>
class GTWOSUTILS_API EnumConfigValue : public ConfigValue<EnumT> {
public:
  using ValidValuesFunc = std::function<bool(EnumT)>;

  EnumConfigValue(const std::string& name, EnumT defaultValue, ValidValuesFunc validator, bool workspaceOverridable = false)
    : ConfigValue<EnumT>(workspaceOverridable),
    m_name(name),
    m_validator(validator),
    m_defaultValue(defaultValue) {
    this->m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const EnumT& value) override {
    if (!m_validator(value)) {
      throw std::runtime_error("Invalid enum value for " + name());
    }
  }
  EnumT getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  ValidValuesFunc m_validator;
  EnumT m_defaultValue;
};

class GTWOSUTILS_API IntConfigValue : public ConfigValue<int> {
public:
  IntConfigValue(const std::string& name, int defaultValue = 0,
    int minValue = INT_MIN, int maxValue = INT_MAX, bool workspaceOverridable = false)
    : ConfigValue<int>(workspaceOverridable),
    m_name(name),
    m_defaultValue(defaultValue),
    m_min(minValue),
    m_max(maxValue) {
    m_value = defaultValue;
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const int& value) override {
    if (value < m_min || value > m_max) {
      throw std::runtime_error("Value " + std::to_string(value) +
        " out of range [" + std::to_string(m_min) + "," +
        std::to_string(m_max) + "] for " + name());
    }
  }
  int getDefault() const override { return m_defaultValue; }

private:
  std::string m_name;
  int m_defaultValue;
  int m_min;
  int m_max;
};

class GTWOSUTILS_API PatternConfigValue : public ConfigValue<std::string> {
public:
  using PatternFunc = std::function<bool(const std::string&)>;

  PatternConfigValue(PatternFunc validator, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_validator(validator) {
    m_value = "";
  }

  PatternConfigValue(const std::string& name, PatternFunc validator, bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable),
    m_name(name),
    m_validator(validator) {
    m_value = "";
  }

  std::string name() const override { return m_name; }
  void setName(const std::string& name) { m_name = name; }

protected:
  void validate(const std::string& value) override {
    if (!m_validator(value)) {
      throw std::runtime_error("Invalid string pattern for " + name());
    }
  }
  std::string getDefault() const override { return ""; }

private:
  std::string m_name;
  PatternFunc m_validator;
};


class GTWOSUTILS_API LocalHostConfig : public ConfigValue<std::string> {
public:
  LocalHostConfig(bool workspaceOverridable = false)
    : ConfigValue<std::string>(workspaceOverridable) {
    m_value = "127.0.0.1";
  }

  std::string name() const override { return "localHost"; }

protected:
  void validate(const std::string& value) override {
    if (value != "127.0.0.1" && value != "localhost") {
      throw std::runtime_error("Invalid localhost value: " + value);
    }
  }
  std::string getDefault() const override { return "127.0.0.1"; }
};